{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "barryvdh/laravel-dompdf": "^3.1", "doctrine/dbal": "^4.2", "http-interop/http-factory-guzzle": "^1.2", "laravel/fortify": "^1.24", "laravel/framework": "^11.9", "laravel/nightwatch": "^1.7", "laravel/scout": "^10.12", "laravel/tinker": "^2.9", "league/flysystem-aws-s3-v3": "^3.0", "league/flysystem-sftp-v3": "^3.30", "livewire/flux": "^2.0", "livewire/flux-pro": "^2.0", "livewire/livewire": "^3.0", "maatwebsite/excel": "^3.1", "meilisearch/meilisearch-php": "^1.12", "prinsfrank/standards": "^3.11", "prism-php/prism": "^0.85.0", "spatie/laravel-permission": "^6.20", "spatie/laravel-tags": "^4.7", "spatie/simple-excel": "^3.7"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.15", "fakerphp/faker": "^1.23", "laravel/boost": "^1.0", "laravel/pint": "^1.13", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^11.0.1", "predis/predis": "^3.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true, "repositories": {"flux-pro": {"type": "composer", "url": "https://composer.fluxui.dev"}}}