<?php

namespace App\Actions\Orders\Statuses;

use App\Models\Order\Order;
use App\Enums\OrderStatuses;
use Illuminate\Support\Facades\DB;

class Reject
{
    public static function run(Order $order): array
    {
        DB::beginTransaction();

        try {
            // Check if the order is in SUBMITTED or in OPENED status
            if ($order->status !== OrderStatuses::Submitted && $order->status !== OrderStatuses::Open) {
                DB::rollBack();
                return [
                    'success' => false,
                    'message' => 'The ORDER can only be rejected if it is in SUBMITTED or OPEN status.'
                ];
            }

            // Update the order status to REJECTED
            $order->update([
                'status' => OrderStatuses::Rejected->value
            ]);

            DB::commit();

            return [
                'success' => true,
                'message' => 'The ORDER has been rejected.'
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            return [
                'success' => false,
                'message' => 'An error occurred while rejecting the order.'
            ];
        }
    }
}
