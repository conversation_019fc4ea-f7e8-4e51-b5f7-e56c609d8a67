<?php

namespace App\Actions\Orders\Statuses;

use App\Models\Order\Order;
use App\Enums\OrderStatuses;
use Illuminate\Support\Facades\DB;

class Submit
{
    public static function run(Order $order): array
    {
        DB::beginTransaction();

        try {
            // Check if the order has rows
            if ($order->orderRows()->count() === 0) {
                DB::rollBack();
                return [
                    'success' => false,
                    'message' => 'The ORDER has no rows.'
                ];
            }

            // Check if the order has custom products
            if ($order->orderRows()->where('custom_product_id', '!=', null)->count() > 0) {
                DB::rollBack();
                return [
                    'success' => false,
                    'message' => 'The ORDER has custom products.'
                ];
            }

            // Check if the order has addresses
            if ($order->invoicing_address_id === null || $order->shipping_address_id === null) {
                DB::rollBack();
                return [
                    'success' => false,
                    'message' => 'The ORDER has no addresses.'
                ];
            }

            // Transfer the product data to the order row
            $order->orderRows()->each(function ($row) {
                // Calculate selling price based on product type
                $sellingPrice = $row->product->hasModules()
                    ? $row->product->getModularPrice($row->options ?? [])
                    : $row->product->selling_price;

                $row->update([
                    'sku' => $row->product->sku,
                    'description' => $row->product->description,
                    'purchasing_price' => $row->product->purchasing_price,
                    'selling_price' => $sellingPrice,
                    'discount' => $row->getDiscountForClient(),
                ]);
            });

            // Update the order status (keep original address references)
            $order->update([
                'status' => OrderStatuses::Submitted->value
            ]);

            DB::commit();

            return [
                'success' => true,
                'message' => 'The ORDER has been submitted.'
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            return [
                'success' => false,
                'message' => 'An error occurred while submitting the order.'
            ];
        }
    }
}
