<?php

namespace App\Actions\Orders\Exports;

use App\Models\Order\Order;
use Barryvdh\DomPDF\Facade\Pdf;

class DownloadPDF
{
    public static function run(Order $order)
    {
        // Optimized eager loading to prevent N+1 queries during PDF generation
        $order = $order->load([
            'client', 
            'client.discountGroups',
            'paymentTerm',
            'paymentTerm.items',
            'addons',
            'cartGroups' => function($query) {
                $query->orderBy('sort');
            },
            'cartGroups.orderRows' => function($query) {
                $query->with([
                    'product' => function($q) {
                        $q->withTrashed()
                            ->with(['brand:id,name', 'collection:id,name']);
                    },
                    'customProduct:id,sku,description,brand_name,dimensions,supplier_color,image'
                ])->orderBy('sort');
            }
        ]);
        
        $sortedCartGroups = $order->cartGroups;
        
        // Pre-calculate totals to avoid multiple expensive calculations
        $totalAmount = $order->totalAmount;
        $addonsAmount = $order->addonsAmount;
        $vatTotal = $order->getVatTotalAttribute($withAddons = true);
        $grossTotal = $order->getTotalAmountAttribute($withAddons = true, $withVat = true);
        $payments = $order->payments;
        
        $orderData = [
            'order' => $order,
            'totalAmount' => eu_currency($totalAmount),
            'addonsAmount' => eu_currency($addonsAmount),
            'vatTotal' => eu_currency($vatTotal),
            'grossTotal' => eu_currency($grossTotal),
            'payments' => $payments,
            'sortedCartGroups' => $sortedCartGroups
        ];
        
        $pdf = Pdf::loadView('exports.order', $orderData);
        $pdf->setOption([
            'isRemoteEnabled' => true,
        ]);

        $fileName = str_replace('/', '-', $order->code) . '.pdf';

        return response()->streamDownload(function () use ($pdf) {
            echo $pdf->stream();
        }, $fileName);
    }
}
