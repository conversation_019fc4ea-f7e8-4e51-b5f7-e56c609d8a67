<?php

namespace App\Actions\Partners\Relations;

use App\Models\Partner;
use App\Actions\Clients\Relations\AttachUsers as AttachUsersToClientResources;

class AttachUsers
{
    public static function run($usersId, Partner $partner) : void
    {
        $clients = $partner->clients()->with('users')->get();
        foreach ($clients as $client) {
            AttachUsersToClientResources::run($usersId, $client, $partner);
        }
    }
}
