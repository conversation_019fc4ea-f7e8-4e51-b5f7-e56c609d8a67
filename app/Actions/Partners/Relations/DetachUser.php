<?php

namespace App\Actions\Partners\Relations;

use App\Actions\Clients\Relations\DetachUser as DetachUserFromClientResources;
use App\Models\Partner;

class DetachUser
{
    public static function run($userId, Partner $partner) : void
    {
        $clients = $partner->clients()->with('users')->get();
        foreach ($clients as $client) {
            DetachUserFromClientResources::run($userId, $client, $partner);
        }
    }
}
