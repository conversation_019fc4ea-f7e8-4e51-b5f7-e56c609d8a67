<?php

namespace App\Actions\OpenAi;

use App\Models\Order\Order;
use App\Models\User;
use Prism\Prism\Prism;
use Prism\Prism\Enums\Provider;
use Prism\Prism\Schema\ObjectSchema;
use Prism\Prism\Schema\ArraySchema;
use Prism\Prism\Schema\NumberSchema;

class GetMostRelevatOrdersForUser
{
    /**
     * Return the IDs of the five most relevant orders for the authenticated user
     */
    public static function run(int $userId): array
    {
        $user = User::find($userId);

        // Get the user's orders directly from the database
        $orders = Order::query()
            ->withoutGlobalScopes()
            ->where('internal_referent_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->limit(10) // Get more than 5 to let AI choose the most relevant
            ->get(['id', 'created_at', 'status']);

        // Convert orders to a readable format for the AI
        $ordersData = $orders->map(function ($order) {
            return [
                'id' => $order->id,
                'created_at' => $order->created_at->format('Y-m-d H:i:s'),
                'status' => $order->status,
            ];
        })->toArray();

        // Define the schema for structured output
        $schema = new ObjectSchema(
            name: 'latest_orders',
            description: 'The IDs of the latest 5 orders for the user',
            properties: [
                new ArraySchema(
                    name: 'order_ids',
                    description: 'Array containing the IDs of the latest 5 orders',
                    items: new NumberSchema('order_id', 'The ID of an order')
                )
            ],
            requiredFields: ['order_ids']
        );

        $ordersJson = json_encode($ordersData);
        $prompt = "Given the following orders data for user ID {$userId}: {$ordersJson}
                Please analyze these orders and return the IDs of the 5 most relevant/recent orders. Consider factors like:
                - Recency (newer orders are generally more relevant)
                - Order status (completed orders might be more relevant than cancelled ones)

                Return exactly 5 order IDs in the order_ids array, ordered by relevance (most relevant first).";

        $response = Prism::structured()
            ->using(Provider::OpenAI, 'gpt-4o')
            ->withSchema($schema)
            ->withPrompt($prompt)
            ->asStructured();

        // Return the structured data or empty array if parsing failed
        return $response->structured['order_ids'] ?? [];
    }
}
