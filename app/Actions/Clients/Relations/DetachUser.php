<?php

namespace App\Actions\Clients\Relations;

use App\Models\Client;
use App\Models\Partner;

class DetachUser
{
    public static function run($userId, Client $client, $executedBy = null) : void
    {
        if ($executedBy === null) {
            $executedBy = $client;
        }

        if($executedBy instanceof Partner) {
            // if executedBy is a Partner also detach users from client
            self::handleDetachUserFromResource($userId, $client, $executedBy);
        }

        // Preload projects with users
        $clientProjects = $client->projects()->with('users')->get();

        foreach ($clientProjects as $project) {
            self::handleDetachUserFromResource($userId, $project, $executedBy);
        }

        // Preload orders with users
        $clientOrders = $client->orders()->with('users')->get();

        foreach ($clientOrders as $order) {
            self::handleDetachUserFromResource($userId, $order, $executedBy);
        }
    }

    private static function handleDetachUserFromResource($userId, $resource, $executedBy){
        // Check if the user is already attached to the resources with 'relational' relation (client, project or order)
        $relationalRelation = $resource->users()
            ->where('user_id', $userId)
            ->where('relation_type', 'relational')
            ->first();

        $relationParentObjectName = class_basename($executedBy);
        $relationParentObjectId = $executedBy->id;

        $relationParentObject = [
            'type' => $relationParentObjectName,
            'id' => $relationParentObjectId
        ];

        if ($relationalRelation) {
            $relationParents = json_decode($relationalRelation->pivot->relation_parents ?? '[]', true);

            // Check if the executedBy object already exists in relation_parents
            $executedByObjectExists = collect($relationParents)->contains(function ($parent) use ($relationParentObject) {
                return $parent['type'] === $relationParentObject['type'] && $parent['id'] === $relationParentObject['id'];
            });

            if ($executedByObjectExists) {
                $newRelationParents = collect($relationParents)->reject(function ($parent) use ($relationParentObject) {
                    return $parent['type'] === $relationParentObject['type'] && $parent['id'] === $relationParentObject['id'];
                })->values()->all();
                if(!empty($newRelationParents)){
                    // Update existing relational relation with the new relation_parents
                    $resource->users()->updateExistingPivot($userId, [
                        'relation_type' => 'relational',
                        'relation_parents' => json_encode($newRelationParents)
                    ]);
                } else {
                    // If no relation parents left, detach the user completely
                    $resource->users()->detach($userId);
                }
            }
        }
    }
}
