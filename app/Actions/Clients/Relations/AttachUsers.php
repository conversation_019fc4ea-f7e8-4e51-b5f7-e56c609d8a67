<?php

namespace App\Actions\Clients\Relations;

use App\Models\Client;
use App\Models\Partner;

class AttachUsers
{
    /**
     * Attach the users to client's projects and orders
     */
    public static function run($usersId, Client $client, $executedBy = null) : void
    {
        if ($executedBy === null) {
            $executedBy = $client;
        }

        if($executedBy instanceof Partner) {
            foreach ($usersId as $userId) {
                // if executedBy is a Partner also attach users to client
                self::handleAttachUserToResource($usersId, $client, $executedBy);
            }
        }

        // Preload projects with users
        $clientProjects = $client->projects()->with('users')->get();

        foreach ($clientProjects as $project) {
            foreach ($usersId as $userId) {
                self::handleAttachUserToResource($userId, $project, $executedBy);
            }
        }

        // Preload orders with users
        $clientOrders = $client->orders()->with('users')->get();

        foreach ($clientOrders as $order) {
            foreach ($usersId as $userId) {
                self::handleAttachUserToResource($userId, $order, $executedBy);
            }
        }
    }

    private static function handleAttachUserToResource($userId, $resource, $executedBy){

        // Check if the user is already attached to the resources with 'relational' relation (client, project or order)
        $relationalRelation = $resource->users()
            ->where('user_id', $userId)
            ->where('relation_type', 'relational')
            ->first();


        $relationParentObjectName = class_basename($executedBy);
        $relationParentObjectId = $executedBy->id;

        $relationParentObject = [
            'type' => $relationParentObjectName,
            'id' => $relationParentObjectId
        ];

        if ($relationalRelation) {
            // Update existing relational relation
            $relationParents = json_decode($relationalRelation->pivot->relation_parents ?? '[]', true);

            // Check if the executedBy object already exists in relation_parents
            $executedByObjectExists = collect($relationParents)->contains(function ($parent) use ($relationParentObject) {
                return $parent['type'] === $relationParentObject['type'] && $parent['id'] === $relationParentObject['id'];
            });

            if (!$executedByObjectExists) {
                $relationParents[] = $relationParentObject;

                $resource->users()->updateExistingPivot($userId, [
                    'relation_parents' => json_encode($relationParents)
                ]);
            }
        } else {
            // Attach user with relation_type relational
            $resource->users()->attach($userId, [
                'relation_type' => 'relational',
                'relation_parents' => json_encode([$relationParentObject]),
            ]);
        }
    }
}
