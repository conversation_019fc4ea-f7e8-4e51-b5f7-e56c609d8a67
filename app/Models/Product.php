<?php

namespace App\Models;

use <PERSON><PERSON>\Tags\HasTags;
use <PERSON><PERSON>\Scout\Searchable;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Collection as ProductCollection;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Product extends Model
{
    use HasFactory, HasTags, Searchable, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'template',

        'brand_id',
        'collection_id',
        'discount_group_id',

        'parent_sku',
        'parent_category_label',
        'parent_category_value',

        'sku',
        'adhoc_sku',
        'adhoc_discount_category',
        'ean_code',

        'description',
        'extra_description',
        'image',
        'dimensions',

        'selling_price',
        'starting_selling_price',
        'purchasing_price',

        'purchase_units',
        'leadtime',
        'type',
        'country_of_origin',
        'supplier_color',
        'hs_code',

        'height',
        'length',
        'width',
        'diameter',
        'volume',
        'capacity',
        'net_weight',

        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected $casts = [
        'selling_price' => 'integer',
        'starting_selling_price' => 'integer',
        'purchasing_price' => 'integer',

        'purchase_units' => 'integer',
    ];

    /**
     * Set the searchable columns for the model in Meilisearch.
     *
     */
    public function toSearchableArray(): array
    {
        return [
            'id' => (int)$this->id,

            'sku' => (string)$this->sku,
            'adhoc_sku' => (string)$this->adhoc_sku,
            'ean_code' => (string)$this->ean_code,

            'description' => (string)$this->description,
            'extra_description' => (string)$this->extra_description,

            'selling_price' => (int)$this->selling_price,
            'purchasing_price' => (int)$this->purchasing_price,

            'leadtime' => (string)$this->leadtime,
            'type' => (string)$this->type,
            'height' => (string)$this->height,
            'length' => (string)$this->length,
            'width' => (string)$this->width,
            'diameter' => (string)$this->diameter,
            'volume' => (string)$this->volume,
            'capacity' => (string)$this->capacity,
            // 'notes' => (string) $this->notes,

            'discount_group_id' => (int)$this->discount_group_id,

            'brand_prefix' => (string)$this->brand?->prefix,
            'brand_name' => (string)$this->brand?->name,

            'collection_name' => (string)$this->collection?->name,

            'destination_room' => $this->tags->where('type', 'destination_room')->pluck('name')->toArray(), // Destination Room Tags
            'style' => $this->tags->where('type', 'style')->pluck('name')->toArray(), // Style Tags
            'material' => $this->tags->where('type', 'material')->pluck('name')->toArray(), // Material Tags
            'color' => $this->tags->where('type', 'color')->pluck('name')->toArray(), // Color Tags
            'general_feature' => $this->tags->where('type', 'general_feature')->pluck('name')->toArray(), // General Feature Tags
        ];
    }

    /**
     * Get the selling price amount.
     *
     * @return float
     */
    public function getSellingPriceAttribute(): ?float
    {
        return $this->attributes['selling_price'] ? $this->attributes['selling_price'] / 100 : NULL;
    }

    /**
     * Set the selling price amount.
     *
     * @param float $value
     * @return void
     */
    public function setSellingPriceAttribute(?float $value): void
    {
        $this->attributes['selling_price'] = $value === null ? null : (int)($value * 100);
    }

    /**
     * Get the selling price for a specific client.
     *
     * @return float
     */
    public function getSellingPriceForClient(?Client $client = null): ?float
    {
        $productDiscountGroup = $this->discountGroup;

        // Check if the client has the same discount group as the product
        if ($productDiscountGroup) {
            $clientDiscountGroup = $client?->discountGroups()->where('discount_group_id', $productDiscountGroup->id)->first();
            if ($clientDiscountGroup) {
                return $this->selling_price * (1 - $clientDiscountGroup->pivot->discount / 100);
            }
        }

        return $this->selling_price;
    }

    /**
     * Get the starting selling price amount.
     *
     * @return float
     */
    public function getStartingSellingPriceAttribute(): ?float
    {
        return $this->attributes['starting_selling_price'] ? $this->attributes['starting_selling_price'] / 100 : NULL;
    }

    /**
     * Set the starting selling price amount.
     *
     * @param float $value
     * @return void
     */
    public function setStartingSellingPriceAttribute(?float $value): void
    {
        $this->attributes['starting_selling_price'] = $value === null ? null : (int)($value * 100);
    }

    /**
     * Get the purchasing price amount.
     *
     * @return float
     */
    public function getPurchasingPriceAttribute(): ?float
    {
        return $this->attributes['purchasing_price'] ? $this->attributes['purchasing_price'] / 100 : NULL;
    }

    /**
     * Set the selling price amount.
     *
     * @param float $value
     * @return void
     */
    public function setPurchasingPriceAttribute(?float $value): void
    {
        $this->attributes['purchasing_price'] = $value === null ? null : (int)($value * 100);
    }

    /**
     * Get the product price if modular.
     * Optimized to use single WHERE IN query instead of multiple finds.
     */
    public function getModularPrice(?array $options): ?float
    {
        if (empty($options)) {
            return $this->selling_price;
        }

        $price = $this->selling_price;

        // Get all options in a single query
        $selectedOptions = Option::whereIn('id', $options)->get();

        foreach ($selectedOptions as $option) {
            $categorizedPrice = $this->prices()
                ->where('name', (string)$option->price_category)
                ->where('type', 'selling')
                ->first()?->price;
            $price += $option->price ?? $categorizedPrice ?? 0;
        }

        return $price;
    }

    /**
     * Return true if the product has variants.
     */
    public function hasVariants(): bool
    {
        return $this->parent_sku != null || $this->parent_sku != '';
    }

    /**
     * Return true if the product has modules.
     */
    public function hasModules(): bool
    {
        return $this->modules->count() > 0;
    }

    /**
     * Get all the variants for the product.
     */
    public function variants(): Collection
    {
        return Product::where('parent_sku', $this->parent_sku)->get();
    }

    /**
     * Get the collection that owns the product.
     */
    public function collection(): BelongsTo
    {
        return $this->belongsTo(ProductCollection::class);
    }

    /**
     * Get the brand that owns the product.
     */
    public function brand(): BelongsTo
    {
        return $this->belongsTo(Brand::class);
    }

    /**
     * Get the modules for the product.
     */
    public function modules(): HasMany
    {
        return $this->hasMany(Module::class);
    }

    /**
     * Get the prices for the product.
     */
    public function prices(): HasMany
    {
        return $this->hasMany(Price::class);
    }

    /**
     * Get the discount group that owns the product.
     */
    public function discountGroup(): BelongsTo
    {
        return $this->belongsTo(DiscountGroup::class);
    }

    /**
     * Get all orders that contain this product.
     */
    public function orders()
    {
        return $this->hasManyThrough(
            \App\Models\Order\Order::class,
            \App\Models\Order\OrderRow::class,
            'product_id', // Foreign key on OrderRow table
            'id', // Foreign key on Order table
            'id', // Local key on Product table
            'cart_group_id' // Local key on OrderRow table
        )->join('cart_groups', 'cart_groups.id', '=', 'order_rows.cart_group_id')
         ->where('cart_groups.order_id', '!=', null);
    }

    /**
     * Get order rows that contain this product.
     */
    public function orderRows(): HasMany
    {
        return $this->hasMany(\App\Models\Order\OrderRow::class);
    }
}
