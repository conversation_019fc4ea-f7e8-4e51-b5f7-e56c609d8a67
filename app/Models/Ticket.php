<?php

namespace App\Models;

use App\Enums\ModelTypes;
use App\Enums\TicketStatus;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;

class Ticket extends Model
{
    protected $fillable = [
        'title',
        'description',
        'status',
        'user_id',
        'ticketable_type',
        'ticketable_id',
    ];

    protected $casts = [
        'status' => TicketStatus::class,
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function ticketable()
    {
        return $this->morphTo();
    }

    protected function resourceType(): Attribute
    {
        return Attribute::make(
            get: fn() => ModelTypes::fromClass($this->ticketable_type)
        );
    }
}
