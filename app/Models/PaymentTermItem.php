<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class PaymentTermItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'payment_term_id',
        'percentage',
        'description',
    ];

    protected $casts = [
        'percentage' => 'float',
    ];

    public function paymentTerm(): BelongsTo
    {
        return $this->belongsTo(PaymentTerm::class);
    }
}
