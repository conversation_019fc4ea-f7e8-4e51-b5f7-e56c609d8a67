<?php

namespace App\Models\Order;

use App\Models\Scopes\UserRoleScope;
use App\Models\User;
use App\Models\Asset;
use App\Models\Client;
use App\Enums\VatTypes;
use App\Models\Address;
use App\Models\Contact;
use App\Models\Partner;
use App\Models\PaymentTerm;
use App\Enums\OrderStatuses;
use App\Models\Collaborator;
use App\Enums\OrderCodeTypes;
use App\Enums\OrderCodeRegions;
use App\Models\Project\Project;
use Illuminate\Database\Eloquent\Attributes\ScopedBy;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

#[ScopedBy([UserRoleScope::class])]
class Order extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'project_id',

        'date',
        'status',
        'code_progressive',
        'code',
        'description',
        'order_code',
        'order_code_progressive',
        'order_code_date',
        'order_code_type',
        'order_code_region',
        'confirmation_file',

        'internal_referent_id',
        'area_manager_id',

        'client_id',
        'partner_id',
        'invoicing_address_id',
        'shipping_address_id',
        'payment_term_id',
        'vat_type'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'status' => OrderStatuses::class,
        'date' => 'date',
        'order_code_date' => 'date',
        'order_code_type' => OrderCodeTypes::class,
        'order_code_region' => OrderCodeRegions::class,
        'vat_type' => VatTypes::class,
    ];

    /**
     * Get order total amount.
     * Optimized to use eager-loaded relationships and reduce N+1 queries.
     */
    public function getTotalAmountAttribute(?bool $withAddons = false, ?bool $withVat = false): float
    {
        // Use eager-loaded orderRows if available, otherwise load them efficiently
        $orderRows = $this->relationLoaded('orderRows')
            ? $this->orderRows
            : $this->orderRows()->with(['product', 'product.modules', 'customProduct'])->get();

        $total = $orderRows->sum(function ($orderRow) {
            return $orderRow->rowFinalPrice;
        });

        if ($withAddons) {
            // Use eager-loaded addons if available
            $addons = $this->relationLoaded('addons')
                ? $this->addons
                : $this->addons()->get();

            $addonsTotal = $addons->sum(function ($addon) use ($total) {
                return $total * ($addon->percent / 100);
            });
            $total += $addonsTotal;
        }

        if ($withVat) {
            $total += $total * $this->vat_type->rate();
        }

        return $total;
    }

    /**
     * Get addons amount.
     * Optimized to use eager-loaded relationships and reduce N+1 queries.
     */
    public function getAddonsAmountAttribute(): float
    {
        // Use eager-loaded orderRows if available, otherwise load them efficiently
        $orderRows = $this->relationLoaded('orderRows')
            ? $this->orderRows
            : $this->orderRows()->with(['product', 'product.modules', 'customProduct'])->get();

        $total = $orderRows->sum(function ($orderRow) {
            return $orderRow->rowFinalPrice;
        });

        // Use eager-loaded addons if available
        $addons = $this->relationLoaded('addons')
            ? $this->addons
            : $this->addons()->get();

        return $addons->sum(function ($addon) use ($total) {
            return $total * ($addon->percent / 100);
        });
    }

    /**
     * Get VAT total on the order.
     */
    public function getVatTotalAttribute(?bool $withAddons = false): float
    {
        return $this->getTotalAmountAttribute($withAddons, $withVat = false) * $this->vat_type->rate();
    }

    /**
     * Get the order payments.
     * Optimized to avoid multiple calls to getTotalAmountAttribute.
     */
    public function getPaymentsAttribute(): array
    {
        if (!$this->paymentTerm || !$this->paymentTerm->items) {
            return [];
        }

        // Calculate total amount once to avoid multiple expensive calculations
        $totalAmount = $this->getTotalAmountAttribute($withAddons = true, $withVat = true);

        return $this->paymentTerm->items->map(function ($item) use ($totalAmount) {
            $itemValue = $totalAmount * ($item->percentage / 100);
            return [
                'description' => $item->description,
                'percentage' => $item->percentage,
                'value' => $itemValue,
                'formatted_value' => '€ ' . number_format($itemValue, 2, ',', '.')
            ];
        })->toArray();
    }

    /**
     * Get the cart groups for the order.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function cartGroups(): HasMany
    {
        return $this->hasMany(CartGroup::class)->orderBy('sort');
    }

    /**
     * Get the order rows for the order.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasManyThrough
     */
    public function orderRows(): HasManyThrough
    {
        return $this->hasManyThrough(OrderRow::class, CartGroup::class);
    }

    /**
     * Get the project that owns the order.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Get all the collaborators for the order.
     */
    public function collaborators(): MorphMany
    {
        return $this->morphMany(Collaborator::class, 'collaboratable');
    }

    /**
     * Get the internal referent for the order.
     */
    public function internalReferent(): BelongsTo
    {
        return $this->belongsTo(User::class, 'internal_referent_id');
    }

    /**
     * Get the area manager for the order.
     */
    public function areaManager(): BelongsTo
    {
        return $this->belongsTo(User::class, 'area_manager_id');
    }

    /**
     * Get the client referents for the order.
     */
    public function clientReferents()
    {
        return $this->belongsToMany(Contact::class, 'contact_order');
    }

    /**
     * Get the client for the order.
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class, 'client_id');
    }

    /**
     * Get the partner for the order.
     */
    public function partner(): BelongsTo
    {
        return $this->belongsTo(Partner::class, 'partner_id');
    }

    /**
     * Get all the assets for the order.
     */
    public function assets(): MorphMany
    {
        return $this->morphMany(Asset::class, 'assetable');
    }

    /**
     * Get all the assets retated to the order.
     */
    public function relatedAssets(): MorphMany
    {
        return $this->morphMany(Asset::class, 'relatable');
    }

    /**
     * Get the invoicing address for the order.
     */
    public function invoicingAddress(): BelongsTo
    {
        return $this->belongsTo(Address::class, 'invoicing_address_id');
    }

    /**
     * Get the shipping address for the order.
     */
    public function shippingAddress(): BelongsTo
    {
        return $this->belongsTo(Address::class, 'shipping_address_id');
    }

    /**
     * Check if the order has cart groups.
     */
    public function hasCartGroups(): bool
    {
        return $this->cartGroups()->exists();
    }

    /**
     * Get the addons for the order.
     */
    public function addons(): HasMany
    {
        return $this->hasMany(OrderAddon::class);
    }

    /**
     * Get the payment term for the order.
     */
    public function paymentTerm(): BelongsTo
    {
        return $this->belongsTo(PaymentTerm::class);
    }

    /**
     * Get all the users for the order.
     */
    public function users(): MorphToMany
    {
        return $this->morphToMany(User::class, 'userable')->withPivot(['relation_type', 'relation_parents'])->withTimestamps();
    }

    /**
     * Get all products in this order.
     */
    public function products()
    {
        return $this->hasManyThrough(
            \App\Models\Product::class,
            OrderRow::class,
            'cart_group_id', // Foreign key on OrderRow table
            'id', // Foreign key on Product table
            'id', // Local key on Order table
            'product_id' // Local key on OrderRow table
        )->join('cart_groups', 'cart_groups.order_id', '=', 'orders.id')
            ->whereNotNull('order_rows.product_id');
    }

    public static function modelAttributes(): array
    {
        return [
            [
                'key' => 'date',
                'label' => 'Date',
                'required' => true
            ],
            [
                'key' => 'description',
                'label' => 'Description',
                'required' => true
            ],
            [
                'key' => 'internal_referent_id',
                'label' => 'Internal Referent',
                'required' => false
            ],
            [
                'key' => 'area_manager_id',
                'label' => 'Area Manager',
                'required' => false
            ],
            [
                'key' => 'order_code_type',
                'label' => 'Order Code Type',
                'required' => false
            ],
            [
                'key' => 'order_code_region',
                'label' => 'Order Code Region',
                'required' => false
            ],
            [
                'key' => 'order_code_progressive',
                'label' => 'Progressive',
                'required' => false
            ],
            [
                'key' => 'client_id',
                'label' => 'Client',
                'required' => false
            ],
            [
                'key' => 'partner_id',
                'label' => 'Partner',
                'required' => false
            ],
            [
                'key' => 'payment_term_id',
                'label' => 'Payment Term',
                'required' => false
            ],
            [
                'key' => 'vat_type',
                'label' => 'VAT Type',
                'required' => false
            ],
            [
                'key' => 'invoicing_address_id',
                'label' => 'Invoicing Address',
                'required' => false
            ],
            [
                'key' => 'shipping_address_id',
                'label' => 'Shipping Address',
                'required' => false
            ],
            [
                'key' => 'project_id',
                'label' => 'Project',
                'required' => false
            ],
            [
                'key' => 'confirmation_file',
                'label' => 'Confirmation File',
                'required' => false
            ]
        ];
    }
}
