<?php

namespace App\Models\Order;

use App\Models\Option;
use App\Models\Product;
use App\Models\CustomProduct;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OrderRow extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'sort',

        'cart_group_id',
        'product_id',
        'custom_product_id',

        'sku',
        'description',
        'options',

        'position_id',

        'quantity',
        'shipped_quantity',
        'stocked_quantity',
        'ordered_quantity',

        'selling_price',
        'selling_price_override',
        'purchasing_price',
        'discount',
        'discount_override',
        'variation',

        'expected_delivery_date',
        'required_delivery_date',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'options' => 'json',

        'quantity' => 'integer',
        'selling_price' => 'integer',
        'selling_price_override' => 'integer',
        'purchasing_price' => 'integer',
        'discount' => 'float',
        'discount_override' => 'float',
        'variation' => 'float',

        'expected_delivery_date' => 'date',
        'required_delivery_date' => 'date',
    ];

    /**
     * Get the selling price amount.
     *
     * @return float
     */
    public function getSellingPriceAttribute(): ?float
    {
        return $this->attributes['selling_price'] ? $this->attributes['selling_price'] / 100 : NULL;
    }

    /**
     * Set the selling price amount.
     *
     * @param float $value
     * @return void
     */
    public function setSellingPriceAttribute(?float $value): void
    {
        $this->attributes['selling_price'] = $value === null ? null : (int)($value * 100);
    }

    /**
     * Get the selling price amount.
     *
     * @return float
     */
    public function getSellingPriceOverrideAttribute(): ?float
    {
        return $this->attributes['selling_price_override'] ? $this->attributes['selling_price_override'] / 100 : NULL;
    }

    /**
     * Set the selling price amount.
     *
     * @param float $value
     * @return void
     */
    public function setSellingPriceOverrideAttribute(?float $value): void
    {
        $this->attributes['selling_price_override'] = $value === null ? null : (int)($value * 100);
    }

    /**
     * Get the purchasing price amount.
     *
     * @return float
     */
    public function getPurchasingPriceAttribute(): ?float
    {
        return $this->attributes['purchasing_price'] ? $this->attributes['purchasing_price'] / 100 : NULL;
    }

    /**
     * Set the selling price amount.
     *
     * @param float $value
     * @return void
     */
    public function setPurchasingPriceAttribute(?float $value): void
    {
        $this->attributes['purchasing_price'] = $value === null ? null : (int)($value * 100);
    }

    /**
     * Get the discount for the client.
     * Optimized to handle null cartGroup or order relationships.
     */
    public function getDiscountForClient()
    {
        // Get the client safely, handling null relationships
        $client = $this->cartGroup?->order?->client;

        // Return 0 if no client is found
        if (!$client) {
            return 0;
        }

        // Check if the row has a product associated
        if ($this->product) {
            $clientDiscountGroup = $client->discountGroups()->where('discount_group_id', $this->product->discountGroup?->id)->first();
        } elseif ($this->product_id) {
            $product = Product::withTrashed()->find($this->product_id);
            if ($product && $product->discountGroup) {
                $clientDiscountGroup = $client->discountGroups()->where('discount_group_id', $product->discountGroup?->id)->first();
            } else {
                $clientDiscountGroup = null;
            }
        } elseif ($this->customProduct) {
            $clientDiscountGroup = null;
        }

        // Return the discount, or 0 if no client discount group is found
        return $clientDiscountGroup->pivot->discount ?? 0;
    }

    /**
     * Get row unit price.
     * Optimized to reduce database queries by leveraging eager-loaded relationships.
     */
    public function getRowUnitPriceAttribute()
    {
        // Check if row has a product associated (use eager-loaded relationship)
        if ($this->relationLoaded('product') && $this->product) {
            if ($this->product->relationLoaded('modules') && $this->product->hasModules()) {
                // Get the price, discount and variation, based on the selected options
                $price = $this->selling_price_override ?? $this->selling_price ?? $this->product->getModularPrice($this->options);
                $discount = $this->discount_override ?? $this->discount ?? $this->getDiscountForClient();
                $variation = $this->variation ?? 0;
            } else {
                // Get the price, discount and variation
                $price = $this->selling_price_override ?? $this->selling_price ?? $this->product->selling_price;
                $discount = $this->discount_override ?? $this->discount ?? $this->getDiscountForClient();
                $variation = $this->variation ?? 0;
            }
        } elseif ($this->relationLoaded('customProduct') && $this->customProduct) {
            $price = $this->selling_price_override ?? 0;
            $discount = $this->discount_override ?? 0;
            $variation = $this->variation ?? 0;
        } elseif ($this->product_id && ($product = Product::withTrashed()->find($this->product_id))) {
            // Fallback for soft-deleted products (only when product is not eager-loaded)
            if ($product->hasModules()) {
                $price = $this->selling_price_override ?? $this->selling_price ?? $product->getModularPrice($this->options);
            } else {
                $price = $this->selling_price_override ?? $this->selling_price ?? $product->selling_price;
            }
            $discount = $this->discount_override ?? $this->discount ?? $this->getDiscountForClient();
            $variation = $this->variation ?? 0;
        } else {
            return 0;
        }

        // Calculate the net price ((price - discount) +/- variation)
        $discountedPrice = $price - ($price * $discount / 100);
        $finalPrice = $discountedPrice + ($discountedPrice * $variation / 100);

        return $finalPrice;
    }

    /**
     * Get row final price.
     */
    public function getRowFinalPriceAttribute()
    {
        return $this->rowUnitPrice * $this->quantity;
    }

    /**
     * Get modular product SKU, based on the selected options code.
     * Optimized to use eager-loaded product relationship and single WHERE IN query.
     */
    public function getModularSku()
    {
        // Use eager-loaded product if available, otherwise fallback to query
        $product = $this->relationLoaded('product') && $this->product
            ? $this->product
            : Product::withTrashed()->find($this->product_id);

        $productSku = $product->sku ?? '';

        // Get options efficiently with single WHERE IN query
        if (empty($this->options) || !is_array($this->options)) {
            return $productSku;
        }

        $optionCodes = Option::whereIn('id', $this->options)->pluck('code')->toArray();
        return $productSku . implode('', $optionCodes);
    }

    /**
     * Get the selected options for the row.
     */
    public function getSelectedOptions(): array
    {
        if (empty($this->options) || !is_array($this->options)) {
            return [];
        }

        return Option::whereIn('id', $this->options)->get()->toArray();
    }

    /**
     * Get the cart group that owns the order row.
     */
    public function cartGroup(): BelongsTo
    {
        return $this->belongsTo(CartGroup::class);
    }

    /**
     * Get the product that owns the order row.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class)->withTrashed();
    }

    /**
     * Get the custom product for the order row.
     */
    public function customProduct(): BelongsTo
    {
        return $this->belongsTo(CustomProduct::class);
    }
}
