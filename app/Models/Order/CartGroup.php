<?php

namespace App\Models\Order;

use App\Models\Product;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CartGroup extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'order_id',
        'product_id',

        'name',
        'sort',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [

    ];

    /**
     * Get the order rows for the cart group.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function orderRows(): Has<PERSON><PERSON>
    {
        return $this->hasMany(OrderRow::class);
    }

    /**
     * Get the order that owns the cart group.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the products for the cart group.
     */
    public function products()
    {
        return $this->belongsToMany(Product::class, 'order_rows')
            ->withPivot(['quantity', 'options'])
            ->withTimestamps();
    }
}
