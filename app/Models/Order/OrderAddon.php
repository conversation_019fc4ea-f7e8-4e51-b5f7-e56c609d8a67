<?php

namespace App\Models\Order;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OrderAddon extends Model
{
    protected $fillable = [
        'order_id',
        'code',
        'description',
        'percent',
    ];

    protected $casts = [
        'percent' => 'float',
    ];

    /**
     * Get the addon price.
     */
    public function getPriceAttribute(): float
    {
        return $this->order->total_amount * $this->percent / 100;
    }

    /**
     * Get formatted addon price.
     */
    public function getFormattedPriceAttribute(): string
    {
        return $this->price ? '€ ' . number_format($this->price, 2, ',', '.') : '€ --';
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }
}
