<?php

namespace App\Models;

use App\Enums\AssetTypes;
use App\Enums\AssetStatuses;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Asset extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'assetable_id',
        'assetable_type',
        'relatable_id',
        'relatable_type',
        
        'type',
        'name',
        'link',
        'file',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'assetable_id' => 'integer',
        'assetable_type' => 'string',
        'relatable_id' => 'integer',
        'relatable_type' => 'string',
        
        'type' => AssetTypes::class,
        'status' => AssetStatuses::class,
    ];

    /**
     * Get the parent assetable model (Order and Project).
     */
    public function assetable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the parent relatable model.
     */
    public function relatable(): MorphTo
    {
        return $this->morphTo();
    }
}
