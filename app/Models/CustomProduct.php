<?php

namespace App\Models;

use App\Models\Order\OrderRow;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CustomProduct extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'sku',
        'description',
        'brand_name',
        'supplier_color',
        'dimensions',
        'image',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [];

    /**
     * Get the order row for the custom product.
     */
    public function orderRow(): HasOne
    {
        return $this->hasOne(OrderRow::class);
    }

    /**
     * Get the user that owns the custom product.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
