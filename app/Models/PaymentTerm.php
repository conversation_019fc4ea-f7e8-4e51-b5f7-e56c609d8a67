<?php

namespace App\Models;

use App\Models\Order\Order;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class PaymentTerm extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'name',
    ];

    public function items(): Has<PERSON>any
    {
        return $this->hasMany(PaymentTermItem::class);
    }

    public function clients(): HasMany
    {
        return $this->hasMany(Client::class);
    }

    public function partners(): HasMany
    {
        return $this->hasMany(Partner::class);
    }

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }
}
