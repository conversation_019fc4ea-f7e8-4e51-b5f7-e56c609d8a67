<?php

namespace App\Models;

use App\Enums\AddressTypes;
use App\Models\Order\Order;
use Illuminate\Database\Eloquent\Model;
use PrinsFrank\Standards\Country\CountryAlpha3;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Address extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'addressable_id',
        'addressable_type',
        'name',
        'type',
        'parent_id',
        'code_invoicing',
        'code_shipping',
        'company',
        'vat_number',
        'fiscal_code',
        'sdi_code',
        'street',
        'city',
        'state',
        'zip',
        'country',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'type' => AddressTypes::class,
            'country' => CountryAlpha3::class,
        ];
    }

    /**
     * Get the parent addressable model (Client, Partner or Supplier).
     */
    public function addressable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the parent address model.
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(self::class, 'parent_id');
    }

    /**
     * Get the children addresses.
     */
    public function children(): HasMany
    {
        return $this->hasMany(self::class, 'parent_id');
    }

    /**
     * Get the invoicing orders.
     */
    public function invoicingOrders(): HasMany
    {
        return $this->hasMany(Order::class, 'invoicing_address_id');
    }

    /**
     * Get the shipping orders.
     */
    public function shippingOrders(): HasMany
    {
        return $this->hasMany(Order::class, 'shipping_address_id');
    }
}
