<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Price extends Model
{
    use SoftDeletes;
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'product_id',
        'type',
        'name',
        'price',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected $casts = [
        'price' => 'integer',
    ];

    /**
     * Get the price amount.
     *
     * @return float
     */
    public function getPriceAttribute(): ?float
    {
        return $this->attributes['price'] ? $this->attributes['price'] / 100 : NULL;
    }

    /**
     * Set the selling price amount.
     *
     * @param float $value
     * @return void
     */
    public function setPriceAttribute(?float $value): void
    {
        $this->attributes['price'] = $value === null ? null : (int) ($value * 100);
    }

    /**
     * Get the product's formatted selling price.
     *
     * @return string
     */
    public function getFormattedPriceProperty()
    {
        return $this->price ? '€ ' . number_format($this->price, 2, ',', '.') : '€ --';
    }

    /**
     * Get the product that owns the price.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }
}
