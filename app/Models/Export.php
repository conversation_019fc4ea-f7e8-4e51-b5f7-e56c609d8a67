<?php

namespace App\Models;

use App\Enums\ExportSources;
use App\Enums\ExportStatuses;
use App\Enums\ExportTypes;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Export extends Model
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'type',
        'source',
        'user_id',
        'resource_id',
        'resource_type',
        'file_paths',
        'file_disk',
        'status',
        'total_files',
        'processed_files',
        'failed_files',
        'started_at',
        'completed_at',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'type' => ExportTypes::class,
        'source' => ExportSources::class,
        'status' => ExportStatuses::class,
        'file_paths' => 'array',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'total_files' => 'integer',
        'processed_files' => 'integer',
        'failed_files' => 'integer',
    ];

    /**
     * Get the user that initiated this export.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the resource that this export belongs to (polymorphic).
     */
    public function resource(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the errors for this export.
     */
    public function errors(): HasMany
    {
        return $this->hasMany(ExportError::class);
    }

    /**
     * Get the progress percentage.
     */
    public function getProgressPercentageAttribute(): float
    {
        if ($this->total_files === 0) {
            return 0;
        }

        return round(($this->processed_files / $this->total_files) * 100, 2);
    }

    /**
     * Get the success rate.
     */
    public function getSuccessRateAttribute(): float
    {
        if ($this->total_files === 0) {
            return 0;
        }

        $successfulFiles = $this->processed_files - $this->failed_files;
        return round(($successfulFiles / $this->total_files) * 100, 2);
    }

    /**
     * Get the duration of the export.
     */
    public function getDurationAttribute(): ?string
    {
        if (!$this->started_at || !$this->completed_at) {
            return null;
        }

        $duration = $this->completed_at->diffInSeconds($this->started_at);

        if ($duration < 60) {
            return "{$duration}s";
        } elseif ($duration < 3600) {
            $minutes = floor($duration / 60);
            $seconds = $duration % 60;
            return "{$minutes}m {$seconds}s";
        } else {
            $hours = floor($duration / 3600);
            $minutes = floor(($duration % 3600) / 60);
            return "{$hours}h {$minutes}m";
        }
    }

    /**
     * Check if the export has errors.
     */
    public function hasErrors(): bool
    {
        return $this->failed_files > 0 || $this->errors()->exists();
    }

    /**
     * Get formatted error log for display.
     */
    public function getFormattedErrorLog(): string
    {
        if (!$this->hasErrors()) {
            return 'No errors found for this export.';
        }

        $errorLog = [];
        $errorLog[] = "Export ID: {$this->id}";
        $errorLog[] = "Type: {$this->type->label()}";
        $errorLog[] = "Source: {$this->source->label()}";
        $errorLog[] = "Status: {$this->status->label()}";
        if ($this->resource_id && $this->resource_type) {
            $errorLog[] = "Resource: {$this->resource_type} ID: {$this->resource_id}";
        }
        $errorLog[] = "Failed Files: {$this->failed_files}/{$this->total_files}";
        $errorLog[] = "Started: " . ($this->started_at ? $this->started_at->format('Y-m-d H:i:s') : 'N/A');
        $errorLog[] = "Completed: " . ($this->completed_at ? $this->completed_at->format('Y-m-d H:i:s') : 'N/A');
        $errorLog[] = str_repeat('=', 80);
        $errorLog[] = '';

        foreach ($this->errors as $error) {
            $errorLog[] = "File Type: {$error->file_type}";
            $errorLog[] = "Error: {$error->error_message}";
            $errorLog[] = "Timestamp: {$error->created_at->format('Y-m-d H:i:s')}";
            $errorLog[] = '';
            $errorLog[] = "Stack Trace:";
            $errorLog[] = $error->error_trace;
            $errorLog[] = str_repeat('-', 80);
            $errorLog[] = '';
        }

        return implode("\n", $errorLog);
    }

    /**
     * Scope for active exports (pending or processing).
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', [
            ExportStatuses::Pending,
            ExportStatuses::Processing,
        ]);
    }
}
