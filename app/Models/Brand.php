<?php

namespace App\Models;

use <PERSON><PERSON>\Tags\HasTags;
use App\Enums\BrandRating;
use App\Enums\BrandLeadTime;
use App\Enums\DeliveryTerms;
use Lara<PERSON>\Scout\Searchable;
use App\Enums\BrandPriceRange;
use App\Enums\BrandPartnershipLevel;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Brand extends Model
{
    use HasFactory, HasTags, Searchable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'prefix',
        'name',
        'price_range',
        'rating',
        'partnership_level',
        'lead_time',
        'purchase_price_list',
        'purchase_conditions',
        'minimum_orderable',
        'extra_costs',
        'delivery_terms',
        'yearly_bonus_info',
        'catalogs',
        'pricelist',
        'valid_from',
        'expected_pricelist_update',
        'social_link',
        'supplier_media_link',
        'supplier_media_link_user',
        'supplier_media_link_password',
        'image',
        'notes',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected $casts = [
        'price_range' => BrandPriceRange::class,
        'rating' => BrandRating::class,
        'partnership_level' => BrandPartnershipLevel::class,
        'lead_time' => BrandLeadTime::class,
        'delivery_terms' => DeliveryTerms::class,
        'minimum_orderable' => 'integer',
        'valid_from' => 'date',
        'expected_pricelist_update' => 'date',
    ];

    /**
     * Get the minimum orderable amount.
     *
     * @return float
     */
    public function getMinimumOrderableAttribute(): ?float
    {
        return $this->attributes['minimum_orderable'] ? $this->attributes['minimum_orderable'] / 100 : NULL;
    }

    /**
     * Set the minimum orderable amount.
     *
     * @param float $value
     * @return void
     */
    public function setMinimumOrderableAttribute(?float $value): void
    {
        $this->attributes['minimum_orderable'] = $value === null ? null : (int) ($value * 100);
    }

    /**
     * Get all the suppliers that belong to the brand.
     */
    public function suppliers(): BelongsToMany
    {
        return $this->belongsToMany(Supplier::class);
    }

    /**
     * Get all the price groups that belong to the brand.
     */
    public function discountGroups(): HasMany
    {
        return $this->hasMany(DiscountGroup::class);
    }

    /**
     * Get all clients that belong to the brand.
     */
    public function clients(): BelongsToMany
    {
        return $this->belongsToMany(Client::class);
    }

    /**
     * Get all the partners that belong to the brand.
     */
    public function partners(): BelongsToMany
    {
        return $this->belongsToMany(Partner::class);
    }

    /**
     * Get all the contacts that belong to the brand.
     */
    public function contacts(): BelongsToMany
    {
        return $this->belongsToMany(Contact::class);
    }

    /**
     * Get all the products for the brand.
     */
    public function products(): HasMany
    {
        return $this->hasMany(Product::class);
    }

    /**
     * Set the searchable columns for the model in Meilisearch.
     */
    public function toSearchableArray(): array
    {
        return [
            'id' => (int) $this->id,
            'prefix' => (string) $this->prefix,
            'name' => (string) $this->name,
            'price_range' => (string) $this->price_range?->value,
            'rating' => (string) $this->rating?->value,
            'partnership_level' => (string) $this->partnership_level?->value,
            'lead_time' => (string) $this->lead_time?->value,

            // Tags
            'type' => $this->tags->where('type', 'type')->pluck('name')->toArray(),
            'material' => $this->tags->where('type', 'material')->pluck('name')->toArray(),
            'destination_room' => $this->tags->where('type', 'destination_room')->pluck('name')->toArray(),
            'style' => $this->tags->where('type', 'style')->pluck('name')->toArray(),
        ];
    }

    public static function modelAttributes(): array
    {
        return [
            [
                'key' => 'prefix',
                'label' => 'Prefix',
                'required' => true
            ],
            [
                'key' => 'name',
                'label' => 'Name',
                'required' => true
            ],
            [
                'key' => 'image',
                'label' => 'Logo',
                'required' => false
            ],
            [
                'key' => 'price_range',
                'label' => 'Price Range',
                'required' => false
            ],
            [
                'key' => 'rating',
                'label' => 'Rating',
                'required' => false
            ],
            [
                'key' => 'partnership_level',
                'label' => 'Partnership Level',
                'required' => false
            ],
            [
                'key' => 'lead_time',
                'label' => 'Lead Time',
                'required' => false
            ],
            [
                'key' => 'purchase_price_list',
                'label' => 'Purchase Price List',
                'required' => false
            ],
            [
                'key' => 'purchase_conditions',
                'label' => 'Purchase Conditions',
                'required' => false
            ],
            [
                'key' => 'minimum_orderable',
                'label' => 'Minimum Orderable Amount',
                'required' => false
            ],
            [
                'key' => 'extra_costs',
                'label' => 'Extra Costs',
                'required' => false
            ],
            [
                'key' => 'delivery_terms',
                'label' => 'Delivery Terms',
                'required' => false
            ],
            [
                'key' => 'yearly_bonus_info',
                'label' => "Yearly Bonus Info",
                "required" => false,
            ],
            [
                "key" => "catalogs",
                "label" => "Catalogs",
                "required" => false,
            ],
            [
                "key" => "pricelist",
                "label" => "Pricelist",
                "required" => false,
            ],
            [
                "key" => "valid_from",
                "label" => "Valid From",
                "required" => false,
            ],
            [
                "key" => "expected_pricelist_update",
                "label" => "Expected Pricelist Update",
                "required" => false,
            ],
            [
                "key" => "social_link",
                "label" => "Social Link",
                "required" => false,
            ],
            [
                "key" => "supplier_media_link",
                "label" => "Supplier Media Link",
                "required" => false,
            ],
            [
                "key" => "supplier_media_link_user",
                "label" => "Supplier Media Link User",
                "required" => false,
            ],
            [
                "key" => "supplier_media_link_password",
                "label" => "Supplier Media Link Password",
                "required" => false,
            ],
            [
                'key' => 'notes',
                'label' => 'Notes',
                'required' => false
            ],

        ];
    }
}
