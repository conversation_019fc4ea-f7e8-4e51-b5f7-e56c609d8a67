<?php

namespace App\Models;

use App\Enums\ImportSources;
use App\Enums\ImportStatuses;
use App\Enums\ImportTypes;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Import extends Model
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'type',
        'source',
        'user_id',
        'file_path',
        'file_disk',
        'status',
        'total_jobs',
        'processed_jobs',
        'failed_jobs',
        'started_at',
        'completed_at',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'type' => ImportTypes::class,
        'source' => ImportSources::class,
        'status' => ImportStatuses::class,
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'total_jobs' => 'integer',
        'processed_jobs' => 'integer',
        'failed_jobs' => 'integer',
    ];

    /**
     * Get the user that initiated this import.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the errors associated with this import.
     */
    public function errors(): HasMany
    {
        return $this->hasMany(ImportError::class);
    }

    /**
     * Get the progress percentage of the import.
     */
    public function progressPercentage(): Attribute
    {
        return Attribute::make(
            get: function (): int {
                if ($this->total_jobs === 0) {
                    return 0;
                }

                return (int) round(($this->processed_jobs / $this->total_jobs) * 100);
            }
        );
    }

    /**
     * Get the success rate percentage of the import.
     */
    public function successRate(): Attribute
    {
        return Attribute::make(
            get: function (): int {
                if ($this->processed_jobs === 0) {
                    return 100;
                }

                $successfulJobs = $this->processed_jobs - $this->failed_jobs;
                return (int) round(($successfulJobs / $this->processed_jobs) * 100);
            }
        );
    }

    /**
     * Check if the import is currently running.
     */
    public function isRunning(): bool
    {
        return $this->status->isActive();
    }

    /**
     * Check if the import is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === ImportStatuses::Completed;
    }

    /**
     * Check if the import has failed.
     */
    public function hasFailed(): bool
    {
        return $this->status === ImportStatuses::Failed;
    }

    /**
     * Check if the import has errors.
     */
    public function hasErrors(): bool
    {
        return $this->failed_jobs > 0;
    }

    /**
     * Get the duration of the import in seconds.
     */
    public function duration(): Attribute
    {
        return Attribute::make(
            get: function (): ?int {
                if (!$this->started_at) {
                    return null;
                }

                $endTime = $this->completed_at ?? now();
                return $this->started_at->diffInSeconds($endTime);
            }
        );
    }

    /**
     * Scope to filter by import type.
     */
    public function scopeOfType($query, ImportTypes $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to filter by import source.
     */
    public function scopeFromSource($query, ImportSources $source)
    {
        return $query->where('source', $source);
    }

    /**
     * Scope to filter by import status.
     */
    public function scopeWithStatus($query, ImportStatuses $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get active imports.
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', [ImportStatuses::Pending, ImportStatuses::Processing]);
    }

    /**
     * Scope to get completed imports.
     */
    public function scopeCompleted($query)
    {
        return $query->whereIn('status', [ImportStatuses::Completed, ImportStatuses::CompletedWithErrors, ImportStatuses::Failed]);
    }

    /**
     * Get formatted error log for display.
     */
    public function getFormattedErrorLog(): string
    {
        if (!$this->hasErrors()) {
            return 'No errors found.';
        }

        $errorLog = [];
        $errorLog[] = "=== IMPORT ERROR LOG ===";
        $errorLog[] = "Import ID: {$this->id}";
        $errorLog[] = "Type: {$this->type->label()}";
        $errorLog[] = "Total Errors: {$this->failed_jobs}";
        $errorLog[] = "Date: {$this->created_at->format('Y-m-d H:i:s')}";
        $errorLog[] = "";

        foreach ($this->errors as $index => $error) {
            $errorLog[] = "--- ERROR #" . ($index + 1) . " ---";
            $errorLog[] = "Job: {$error->short_job_class}";
            $errorLog[] = "Row Identifier: {$error->getRowIdentifier()}";
            $errorLog[] = "Time: {$error->created_at->format('H:i:s')}";
            $errorLog[] = "";
            $errorLog[] = "Error Message:";
            $errorLog[] = $error->error_message;
            $errorLog[] = "";

            if ($error->hasRowData()) {
                $errorLog[] = "Row Data:";
                foreach ($error->row_data as $key => $value) {
                    $errorLog[] = "  {$key}: " . ($value ?? 'NULL');
                }
                $errorLog[] = "";
            }

            $errorLog[] = "Stack Trace:";
            $errorLog[] = $error->error_trace;
            $errorLog[] = "";
            $errorLog[] = str_repeat("-", 50);
            $errorLog[] = "";
        }

        return implode("\n", $errorLog);
    }
}
