<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Option extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'sku',
        'code',
        'category',
        'description',
        'price',
        'price_category',
        'image',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected $casts = [
        'price' => 'integer',
    ];

    /**
     * Get the price amount.
     *
     * @return float
     */
    public function getPriceAttribute(): ?float
    {
        return $this->attributes['price'] ? $this->attributes['price'] / 100 : NULL;
    }

    /**
     * Set the price amount.
     *
     * @param float $value
     * @return void
     */
    public function setPriceAttribute(?float $value): void
    {
        $this->attributes['price'] = $value === null ? null : (int) ($value * 100);
    }

    /**
     * Get the product's formatted price.
     *
     * @return string
     */
    public function getFormattedPriceProperty()
    {
        return $this->price ? '€ ' . number_format($this->price, 2, ',', '.') : '€ --';
    }

    /**
     * Get the modules for the option.
     */
    public function modules(): BelongsToMany
    {
        return $this->belongsToMany(Module::class)->using(ModuleOption::class);
    }
}
