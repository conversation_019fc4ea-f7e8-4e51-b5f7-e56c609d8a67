<?php

namespace App\Models\Scopes;

use App\Models\Client;
use App\Models\Partner;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class UserRoleScope implements Scope
{
    /**
     * Apply the scope to a given Eloquent query builder.
     */
    public function apply(Builder $builder, Model $model): void
    {
        if (auth()->user()?->isAdmin()) {
            return;
        }

        if (request()->routeIs('orders.index') || request()->routeIs('orders.show')) {
            if ($model instanceof Client || $model instanceof Partner) {
                return;
            }
        }

        $userId = auth()->id();
        $builder->whereHas('users', function ($query) use ($userId) {
            $query->whereKey($userId);
        });
    }
}
