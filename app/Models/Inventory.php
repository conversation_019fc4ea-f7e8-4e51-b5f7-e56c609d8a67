<?php

namespace App\Models;

use App\Enums\WarehouseNames;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Inventory extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'sku',
        'warehouse',
        'quantity',
        'date',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'warehouse' => WarehouseNames::class,
        'date' => 'date',
        'quantity' => 'integer',
    ];

    /**
     * Get the product that owns this inventory record.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'sku', 'sku');
    }

    /**
     * Get total quantity (available + incoming) for a specific SKU.
     */
    public static function getTotalQuantityForSku(string $sku, ?WarehouseNames $warehouse = null): int
    {
        $query = static::where('sku', $sku);

        if ($warehouse) {
            $query->where('warehouse', $warehouse);
        }

        return $query->sum('quantity');
    }

    /**
     * Get available quantity (present stock) for a specific SKU.
     */
    public static function getAvailableQuantityForSku(string $sku, ?WarehouseNames $warehouse = null): int
    {
        $query = static::where('sku', $sku)->whereNull('date');

        if ($warehouse) {
            $query->where('warehouse', $warehouse);
        }

        return $query->sum('quantity');
    }

    /**
     * Get incoming quantity (future stock) for a specific SKU.
     */
    public static function getIncomingQuantityForSku(string $sku, ?WarehouseNames $warehouse = null): int
    {
        $query = static::where('sku', $sku)->whereNotNull('date');

        if ($warehouse) {
            $query->where('warehouse', $warehouse);
        }

        return $query->sum('quantity');
    }
}
