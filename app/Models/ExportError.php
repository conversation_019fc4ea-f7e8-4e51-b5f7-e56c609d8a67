<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ExportError extends Model
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'export_id',
        'file_type',
        'error_message',
        'error_trace',
    ];

    /**
     * Get the export that this error belongs to.
     */
    public function export(): BelongsTo
    {
        return $this->belongsTo(Export::class);
    }
}
