<?php

namespace App\Models;

use App\Enums\ClientPartnerPriorities;
use App\Enums\CommercialCategories;
use App\Enums\DeliveryTerms;
use App\Enums\PartnerTypes;
use App\Models\Order\Order;
use App\Models\Project\Project;
use App\Models\Scopes\UserRoleScope;
use Illuminate\Database\Eloquent\Attributes\ScopedBy;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use PrinsFrank\Standards\Country\CountryAlpha3;
use PrinsFrank\Standards\Language\LanguageAlpha2;

#[ScopedBy([UserRoleScope::class])]
class Partner extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'internal_referent_id',
        'area_manager_id',

        'company',
        'email',
        'country',
        'payment_term_id',
        'commercial_category',
        'priority',
        'minimum_orderable',
        'handling_and_packing',
        'delivery_terms',
        'countries_of_expertise',
        'notes',
    ];

    /**
     * Set the email attribute to lowercase.
     */
    public function setEmailAttribute(string $value): void
    {
        $this->attributes['email'] = strtolower(trim($value));
    }

    protected $casts = [
        'type' => PartnerTypes::class,
        'commercial_category' => CommercialCategories::class,
        'delivery_terms' => DeliveryTerms::class,
        'priority' => ClientPartnerPriorities::class,
        'minimum_orderable' => 'integer',
        'handling_and_packing' => 'integer',
        'country' => CountryAlpha3::class,
        'countries_of_expertise' => 'json',
    ];

    /**
     * Get the minimum orderable amount.
     */
    public function getMinimumOrderableAttribute(): ?float
    {
        return $this->attributes['minimum_orderable'] ? $this->attributes['minimum_orderable'] / 100 : null;
    }

    /**
     * Set the minimum orderable amount.
     */
    public function setMinimumOrderableAttribute(?float $value): void
    {
        $this->attributes['minimum_orderable'] = $value === null ? null : (int) ($value * 100);
    }

    /**
     * Get the internal referent for the client.
     */
    public function internalReferent(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the area manager for the partner.
     */
    public function areaManager(): BelongsTo
    {
        return $this->belongsTo(User::class, 'area_manager_id');
    }

    /**
     * Get all the users for the partner.
     */
    public function users(): MorphToMany
    {
        return $this->morphToMany(User::class, 'userable')->withPivot(['relation_type', 'relation_parents'])->withTimestamps();
    }

    /**
     * Get all the contacts for the partner.
     */
    public function contacts(): BelongsToMany
    {
        return $this->belongsToMany(Contact::class);
    }

    /**
     * Get all the brands that belong to the partner.
     */
    public function brands(): BelongsToMany
    {
        return $this->belongsToMany(Brand::class);
    }

    /**
     * Get all the addresses for the partner.
     */
    public function addresses(): MorphMany
    {
        return $this->morphMany(Address::class, 'addressable');
    }

    /**
     * Get the clients for the partner.
     */
    public function clients(): HasMany
    {
        return $this->hasMany(Client::class);
    }

    /**
     * Get the discount groups for the partner.
     */
    public function discountGroups(): BelongsToMany
    {
        return $this->belongsToMany(DiscountGroup::class)
            ->withPivot('discount');
    }

    /**
     * Get the payment term for the partner.
     */
    public function paymentTerm(): BelongsTo
    {
        return $this->belongsTo(PaymentTerm::class);
    }

    /**
     * Get the orders for the partner.
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Check if a brand can be detached from this partner.
     * A brand cannot be detached if it has products used in the partner's orders.
     */
    public function canDetachBrand(int $brandId): bool
    {
        return ! $this->orders()
            ->whereHas('orderRows.product', function ($query) use ($brandId) {
                $query->where('brand_id', $brandId);
            })
            ->exists();
    }

    /**
     * Check if a discount group can be detached from this partner.
     * A discount group cannot be detached if it has products used in the partner's orders.
     */
    public function canDetachDiscountGroup(int $discountGroupId): bool
    {
        return ! $this->orders()
            ->whereHas('orderRows.product', function ($query) use ($discountGroupId) {
                $query->where('discount_group_id', $discountGroupId);
            })
            ->exists();
    }

    /**
     * Get the projects for the partner.
     */
    public function projects(): HasMany
    {
        return $this->hasMany(Project::class);
    }

    /**
     * Get the country label name from alpha3 code.
     */
    public function getCountryLabelAttribute($countryAlpha3): string
    {
        if ($countryAlpha3 === null) {
            return '-';
        } else {
            return CountryAlpha3::from($countryAlpha3)->getNameInLanguage(LanguageAlpha2::English);
        }
    }

    public static function modelAttributes(): array
    {
        return [
            [
                'key' => 'type',
                'label' => 'Type',
                'required' => true,
            ],
            [
                'key' => 'company',
                'label' => 'Company',
                'required' => true,
            ],
            [
                'key' => 'email',
                'label' => 'email',
                'required' => true,
            ],
            [
                'key' => 'internal_referent_id',
                'label' => 'Internal Referent',
                'required' => false,
            ],
            [
                'key' => 'area_manager_id',
                'label' => 'Area Manager',
                'required' => false,
            ],
            [
                'key' => 'country',
                'label' => 'Country',
                'required' => false,
            ],
            [
                'key' => 'countries_of_expertise',
                'label' => 'Countries of Expertise',
                'required' => false,
            ],
            [
                'key' => 'payment_term_id',
                'label' => 'Payment Term',
                'required' => false,
            ],
            [
                'key' => 'commercial_category',
                'label' => 'Commercial Category',
                'required' => false,
            ],
            [
                'key' => 'priority',
                'label' => 'Priority',
                'required' => false,
            ],
            [
                'key' => 'minimum_orderable',
                'label' => 'Minimum Orderable Amount',
                'required' => false,
            ],
            [
                'key' => 'handling_and_packing',
                'label' => 'Handling and Packing Amount',
                'required' => false,
            ],
            [
                'key' => 'delivery_terms',
                'label' => 'Delivery Terms',
                'required' => false,
            ],
            [
                'key' => 'notes',
                'label' => 'Notes',
                'required' => false,
            ],
        ];
    }
}
