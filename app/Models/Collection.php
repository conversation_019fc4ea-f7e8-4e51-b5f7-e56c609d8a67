<?php

namespace App\Models;

use <PERSON><PERSON>\Tags\HasTags;
use <PERSON><PERSON>\Scout\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Collection extends Model
{
    use HasFactory, HasTags, Searchable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'code',
        'name',
        'image'
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected $casts = [];

    /**
     * Get the products for the collection.
     */
    public function products(): HasMany
    {
        return $this->hasMany(Product::class);
    }

    /**
     * Get the brand of the first product in the collection.
     */
    public function brandOfFirstProduct(): ?Brand
    {
        return $this->products()->first()?->brand;
    }

    public static function modelAttributes(): array
    {
        return [
            [
                'key' => 'code',
                'label' => 'Code',
                'required' => true
            ],
            [
                'key' => 'name',
                'label' => 'Name',
                'required' => true
            ],
            [
                'key' => 'image',
                'label' => 'Logo',
                'required' => false
            ]
        ];
    }
}
