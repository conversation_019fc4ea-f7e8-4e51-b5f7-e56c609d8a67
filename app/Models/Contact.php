<?php

namespace App\Models;

use App\Models\Order\Order;
use App\Models\Scopes\UserRoleScope;
use Illuminate\Database\Eloquent\Attributes\ScopedBy;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use PrinsFrank\Standards\Country\CountryAlpha3;
use PrinsFrank\Standards\Language\LanguageAlpha2;

#[ScopedBy([UserRoleScope::class])]
class Contact extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'departments',
        'position',
        'email',
        'phone',
        'country',
        'languages',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'country' => CountryAlpha3::class,
        'departments' => 'json',
        'languages' => 'json',
    ];

    /**
     * Set the email attribute to lowercase.
     */
    public function setEmailAttribute(string $value): void
    {
        $this->attributes['email'] = strtolower(trim($value));
    }

    /**
     * Get all the clients that the contact is related to.
     */
    public function clients(): BelongsToMany
    {
        return $this->belongsToMany(Client::class);
    }

    /**
     * Get all the partners that the contact is related to.
     */
    public function partners(): BelongsToMany
    {
        return $this->belongsToMany(Partner::class);
    }

    /**
     * Get all the suppliers that the contact is related to.
     */
    public function suppliers(): BelongsToMany
    {
        return $this->belongsToMany(Supplier::class);
    }

    /**
     * Get all the brands that the contact is related to.
     */
    public function brands(): BelongsToMany
    {
        return $this->belongsToMany(Brand::class);
    }

    /**
     * Get the collaborator associated with the contact.
     */
    public function collaborator(): HasOne
    {
        return $this->hasOne(Collaborator::class);
    }

    /**
     * Get all orders where this contact is the internal referent.
     */
    public function ordersAsInternalReferent(): HasMany
    {
        return $this->hasMany(Order::class, 'internal_referent_id');
    }

    /**
     * Get all orders where this contact is a client referent.
     */
    public function ordersAsClientReferent(): BelongsToMany
    {
        return $this->belongsToMany(Order::class, 'contact_order');
    }

    /**
     * Get all projects where this contact is the internal referent.
     */
    public function projectsAsInternalReferent(): HasMany
    {
        return $this->hasMany(Order::class, 'internal_referent_id');
    }

    /**
     * Get all projects where this contact is a client referent.
     */
    public function projectsAsClientReferent(): BelongsToMany
    {
        return $this->belongsToMany(Order::class, 'contact_order');
    }

    /**
     * Get the country label name from alpha3 code.
     */
    public function getCountryLabelAttribute($countryAlpha3): string
    {
        if ($countryAlpha3 === null) {
            return '-';
        } else {
            return CountryAlpha3::from($countryAlpha3)->getNameInLanguage(LanguageAlpha2::English);
        }
    }

    /**
     * Get all the users for the contact.
     */
    public function users(): MorphToMany
    {
        return $this->morphToMany(User::class, 'userable')->withPivot('relation_type')->withTimestamps();
    }

    public static function modelAttributes(): array
    {
        return [
            [
                'key' => 'name',
                'label' => 'Name',
                'required' => true,
            ],
            [
                'key' => 'email',
                'label' => 'Email',
                'required' => true,
            ],
            [
                'key' => 'phone',
                'label' => 'Phone',
                'required' => false,
            ],
            [
                'key' => 'departments',
                'label' => 'Departments',
                'required' => false,
            ],
            [
                'key' => 'position',
                'label' => 'Position',
                'required' => false,
            ],
            [
                'key' => 'country',
                'label' => 'Country',
                'required' => false,
            ],
            [
                'key' => 'languages',
                'label' => 'Languages',
                'required' => false,
            ],
            [
                'key' => 'notes',
                'label' => 'Notes',
                'required' => false,
            ],
        ];
    }
}
