<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class DiscountGroup extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'code',
        'discount',
        'description',
        'brand_id',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'discount' => 'float',
    ];

    /**
     * Get the brand that owns the discount group.
     */
    public function brand(): BelongsTo
    {
        return $this->belongsTo(Brand::class);
    }

    /**
     * Get the clients that belong to the discount group.
     */
    public function clients(): BelongsToMany
    {
        return $this->belongsToMany(Client::class)
            ->withPivot('discount');
    }

    /**
     * Get the partners that belong to the discount group.
     */
    public function partners(): BelongsToMany
    {
        return $this->belongsToMany(Partner::class)
            ->withPivot('discount');
    }

    /**
     * Get the products that belong to the discount group.
     */
    public function products(): HasMany
    {
        return $this->hasMany(Product::class);
    }

    public static function modelAttributes(): array
    {
        return [
            [
                'key' => 'code',
                'label' => 'Code',
                'required' => true
            ],
            [
                'key' => 'discount',
                'label' => 'Discount',
                'required' => true
            ],
            [
                'key' => 'brand_id',
                'label' => 'Brand',
                'required' => false
            ],
            [
                'key' => 'description',
                'label' => 'Description',
                'required' => false
            ]
        ];
    }
}
