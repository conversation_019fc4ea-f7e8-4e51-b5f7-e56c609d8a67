<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ImportError extends Model
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'import_id',
        'job_class',
        'row_data',
        'error_message',
        'error_trace',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'row_data' => 'array',
    ];

    /**
     * Get the import that this error belongs to.
     */
    public function import(): BelongsTo
    {
        return $this->belongsTo(Import::class);
    }

    /**
     * Get the short job class name without namespace.
     */
    public function getShortJobClassAttribute(): string
    {
        return class_basename($this->job_class);
    }

    /**
     * Get the first line of the error message.
     */
    public function getShortErrorMessageAttribute(): string
    {
        $lines = explode("\n", $this->error_message);
        return $lines[0] ?? '';
    }

    /**
     * Check if the error has row data.
     */
    public function hasRowData(): bool
    {
        return !empty($this->row_data);
    }

    /**
     * Get a specific field from the row data.
     */
    public function getRowField(string $field): mixed
    {
        return $this->row_data[$field] ?? null;
    }

    /**
     * Get a human-readable identifier from the row data.
     */
    public function getRowIdentifier(): string
    {
        // Try common identifier fields
        $identifierFields = ['sku', 'code', 'email', 'name', 'prefix'];

        foreach ($identifierFields as $field) {
            if (!empty($this->row_data[$field])) {
                return (string) $this->row_data[$field];
            }
        }

        // Fallback to first non-empty value
        foreach ($this->row_data as $value) {
            if (!empty($value)) {
                return (string) $value;
            }
        }

        return 'Unknown';
    }
}
