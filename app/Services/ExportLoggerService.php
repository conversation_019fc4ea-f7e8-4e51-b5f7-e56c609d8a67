<?php

namespace App\Services;

use App\Enums\ExportSources;
use App\Enums\ExportStatuses;
use App\Enums\ExportTypes;
use App\Models\Export;
use App\Models\ExportError;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ExportLoggerService
{
    /**
     * Start a new export and return the export ID.
     */
    public function startExport(
        ExportTypes $type,
        ExportSources $source,
        ?int $resourceId = null,
        ?string $resourceType = null,
        string $fileDisk = 'exchange',
        ?int $userId = null
    ): int {
        try {
            $export = Export::create([
                'type' => $type,
                'source' => $source,
                'user_id' => $userId ?? Auth::id(),
                'resource_id' => $resourceId,
                'resource_type' => $resourceType,
                'file_paths' => [],
                'file_disk' => $fileDisk,
                'status' => ExportStatuses::Pending,
                'started_at' => now(),
            ]);

            $logData = [
                'export_id' => $export->id,
                'type' => $type->value,
                'source' => $source->value,
            ];

            if ($resourceId && $resourceType) {
                $logData['resource_id'] = $resourceId;
                $logData['resource_type'] = $resourceType;
            }

            Log::info("Export started", $logData);

            return $export->id;
        } catch (\Exception $e) {
            $logData = [
                'type' => $type->value,
                'source' => $source->value,
                'error' => $e->getMessage(),
            ];

            if ($resourceId && $resourceType) {
                $logData['resource_id'] = $resourceId;
                $logData['resource_type'] = $resourceType;
            }

            Log::error("Failed to start export", $logData);

            throw $e;
        }
    }

    /**
     * Update the total number of files for an export.
     */
    public function setTotalFiles(int $exportId, int $totalFiles): void
    {
        try {
            Export::where('id', $exportId)->update([
                'total_files' => $totalFiles,
                'status' => ExportStatuses::Processing,
            ]);

            Log::info("Export total files set", [
                'export_id' => $exportId,
                'total_files' => $totalFiles,
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to set total files for export", [
                'export_id' => $exportId,
                'total_files' => $totalFiles,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Increment the processed files counter.
     */
    public function incrementProcessedFiles(int $exportId): void
    {
        try {
            DB::table('exports')
                ->where('id', $exportId)
                ->increment('processed_files');

            Log::info("Export processed files incremented", [
                'export_id' => $exportId,
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to increment processed files for export", [
                'export_id' => $exportId,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Add a file path to the export.
     */
    public function addFilePath(int $exportId, string $filePath): void
    {
        try {
            $export = Export::find($exportId);
            if ($export) {
                $filePaths = $export->file_paths ?? [];
                $filePaths[] = $filePath;
                $export->update(['file_paths' => $filePaths]);

                Log::info("File path added to export", [
                    'export_id' => $exportId,
                    'file_path' => $filePath,
                ]);
            }
        } catch (\Exception $e) {
            Log::error("Failed to add file path to export", [
                'export_id' => $exportId,
                'file_path' => $filePath,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Log an error for a specific export.
     */
    public function logError(
        int $exportId,
        string $fileType,
        string $errorMessage,
        string $errorTrace
    ): void {
        try {
            ExportError::create([
                'export_id' => $exportId,
                'file_type' => $fileType,
                'error_message' => $errorMessage,
                'error_trace' => $errorTrace,
            ]);

            // Increment failed files counter
            DB::table('exports')
                ->where('id', $exportId)
                ->increment('failed_files');

            Log::warning("Export error logged", [
                'export_id' => $exportId,
                'file_type' => $fileType,
                'error_message' => $errorMessage,
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to log export error", [
                'export_id' => $exportId,
                'file_type' => $fileType,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Mark an export as completed.
     */
    public function completeExport(int $exportId): void
    {
        try {
            $export = Export::find($exportId);

            if (!$export) {
                Log::warning("Export not found when trying to complete", ['export_id' => $exportId]);
                return;
            }

            // Determine final status based on file results
            $status = match (true) {
                $export->processed_files === 0 && $export->failed_files > 0 => ExportStatuses::Failed,
                $export->failed_files > 0 => ExportStatuses::CompletedWithErrors,
                default => ExportStatuses::Completed,
            };

            $export->update([
                'status' => $status,
                'completed_at' => now(),
            ]);

            Log::info("Export completed", [
                'export_id' => $exportId,
                'status' => $status->value,
                'processed_files' => $export->processed_files,
                'failed_files' => $export->failed_files,
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to complete export", [
                'export_id' => $exportId,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Mark an export as failed.
     */
    public function failExport(int $exportId, string $reason): void
    {
        try {
            Export::where('id', $exportId)->update([
                'status' => ExportStatuses::Failed,
                'completed_at' => now(),
            ]);

            Log::error("Export failed", [
                'export_id' => $exportId,
                'reason' => $reason,
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to mark export as failed", [
                'export_id' => $exportId,
                'reason' => $reason,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get export statistics.
     */
    public function getExportStats(int $exportId): ?array
    {
        try {
            $export = Export::with('errors')->find($exportId);
            
            if (!$export) {
                return null;
            }

            return [
                'id' => $export->id,
                'type' => $export->type,
                'status' => $export->status,
                'total_files' => $export->total_files,
                'processed_files' => $export->processed_files,
                'failed_files' => $export->failed_files,
                'progress_percentage' => $export->progress_percentage,
                'success_rate' => $export->success_rate,
                'duration' => $export->duration,
                'error_count' => $export->errors->count(),
                'started_at' => $export->started_at,
                'completed_at' => $export->completed_at,
            ];
        } catch (\Exception $e) {
            Log::error("Failed to get export stats", [
                'export_id' => $exportId,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }
}
