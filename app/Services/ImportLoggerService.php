<?php

namespace App\Services;

use App\Enums\ImportSources;
use App\Enums\ImportStatuses;
use App\Enums\ImportTypes;
use App\Models\Import;
use App\Models\ImportError;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ImportLoggerService
{
    /**
     * Start a new import and return the import ID.
     */
    public function startImport(
        ImportTypes $type,
        ImportSources $source,
        string $filePath,
        string $fileDisk = 'private',
        ?int $userId = null
    ): int {
        try {
            $import = Import::create([
                'type' => $type,
                'source' => $source,
                'user_id' => $userId ?? Auth::id(),
                'file_path' => $filePath,
                'file_disk' => $fileDisk,
                'status' => ImportStatuses::Pending,
                'started_at' => now(),
            ]);

            Log::info("Import started", [
                'import_id' => $import->id,
                'type' => $type->value,
                'source' => $source->value,
                'file_path' => $filePath,
            ]);

            return $import->id;
        } catch (\Exception $e) {
            Log::error("Failed to start import", [
                'type' => $type->value,
                'source' => $source->value,
                'file_path' => $filePath,
                'error' => $e->getMessage(),
            ]);
            
            throw $e;
        }
    }

    /**
     * Update the total number of jobs for an import.
     */
    public function setTotalJobs(int $importId, int $totalJobs): void
    {
        try {
            Import::where('id', $importId)->update([
                'total_jobs' => $totalJobs,
                'status' => ImportStatuses::Processing,
            ]);

            Log::info("Import total jobs set", [
                'import_id' => $importId,
                'total_jobs' => $totalJobs,
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to set total jobs for import", [
                'import_id' => $importId,
                'total_jobs' => $totalJobs,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Increment the processed jobs counter.
     */
    public function incrementProcessedJobs(int $importId): void
    {
        try {
            DB::table('imports')
                ->where('id', $importId)
                ->increment('processed_jobs');
        } catch (\Exception $e) {
            Log::error("Failed to increment processed jobs", [
                'import_id' => $importId,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Log an error for a specific import.
     */
    public function logError(
        int $importId,
        string $jobClass,
        array $rowData,
        string $errorMessage,
        string $errorTrace
    ): void {
        try {
            ImportError::create([
                'import_id' => $importId,
                'job_class' => $jobClass,
                'row_data' => $rowData,
                'error_message' => $errorMessage,
                'error_trace' => $errorTrace,
            ]);

            // Increment failed jobs counter
            DB::table('imports')
                ->where('id', $importId)
                ->increment('failed_jobs');

            Log::warning("Import error logged", [
                'import_id' => $importId,
                'job_class' => $jobClass,
                'error_message' => $errorMessage,
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to log import error", [
                'import_id' => $importId,
                'job_class' => $jobClass,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Mark an import as completed.
     */
    public function completeImport(int $importId): void
    {
        try {
            $import = Import::find($importId);

            if (!$import) {
                Log::warning("Import not found when trying to complete", ['import_id' => $importId]);
                return;
            }

            // Determine final status based on job results
            $status = match (true) {
                $import->processed_jobs === 0 && $import->failed_jobs > 0 => ImportStatuses::Failed,
                $import->failed_jobs > 0 => ImportStatuses::CompletedWithErrors,
                default => ImportStatuses::Completed,
            };

            $import->update([
                'status' => $status,
                'completed_at' => now(),
            ]);

            Log::info("Import completed", [
                'import_id' => $importId,
                'status' => $status->value,
                'total_jobs' => $import->total_jobs,
                'processed_jobs' => $import->processed_jobs,
                'failed_jobs' => $import->failed_jobs,
                'duration_seconds' => $import->duration,
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to complete import", [
                'import_id' => $importId,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Mark an import as failed.
     */
    public function failImport(int $importId, string $reason): void
    {
        try {
            Import::where('id', $importId)->update([
                'status' => ImportStatuses::Failed,
                'completed_at' => now(),
            ]);

            Log::error("Import failed", [
                'import_id' => $importId,
                'reason' => $reason,
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to mark import as failed", [
                'import_id' => $importId,
                'reason' => $reason,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get import statistics.
     */
    public function getImportStats(int $importId): ?array
    {
        try {
            $import = Import::with('errors')->find($importId);
            
            if (!$import) {
                return null;
            }

            return [
                'id' => $import->id,
                'type' => $import->type,
                'status' => $import->status,
                'total_jobs' => $import->total_jobs,
                'processed_jobs' => $import->processed_jobs,
                'failed_jobs' => $import->failed_jobs,
                'progress_percentage' => $import->progress_percentage,
                'success_rate' => $import->success_rate,
                'duration' => $import->duration,
                'error_count' => $import->errors->count(),
                'started_at' => $import->started_at,
                'completed_at' => $import->completed_at,
            ];
        } catch (\Exception $e) {
            Log::error("Failed to get import stats", [
                'import_id' => $importId,
                'error' => $e->getMessage(),
            ]);
            
            return null;
        }
    }
}
