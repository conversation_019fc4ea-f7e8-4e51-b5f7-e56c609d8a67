<?php

if (!function_exists('eu_currency')) {
    /**
     * Format a number as Euro currency.
     *
     * @param float|int|null $amount The amount to format
     * @param bool $includeSymbol Whether to include the € symbol
     * @return string
     */
    function eu_currency($amount, bool $includeSymbol = true)
    {
        // Handle null or empty values
        if ($amount === null || $amount === '') {
            return $includeSymbol ? '€ --' : '--';
        }

        // Format the number with 2 decimal places and thousands separator
        $formattedAmount = number_format((float) $amount, 2, ',', '.');

        // Add the Euro symbol if requested
        return $includeSymbol ? "€ {$formattedAmount}" : $formattedAmount;
    }
}
