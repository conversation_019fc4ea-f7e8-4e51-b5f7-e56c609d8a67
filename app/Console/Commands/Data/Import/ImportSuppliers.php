<?php

namespace App\Console\Commands\Data\Import;

use App\Enums\ImportSources;
use App\Enums\ImportTypes;
use App\Jobs\CompleteImportJob;
use App\Services\ImportLoggerService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Spatie\SimpleExcel\SimpleExcelReader;
use App\Jobs\Import\Entities\ImportSupplierJob;

class ImportSuppliers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'suppliers:import
                            {file : Path to the Excel file}
                            {--disk=private : Storage disk to use (local, public, private)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import suppliers from Excel file using queue jobs';

    /**
     * Execute the console command.
     */
    public function handle(ImportLoggerService $importLogger)
    {
        $filePath = $this->argument('file');
        $disk = $this->option('disk');

        $this->info("Starting suppliers import from Excel file: {$filePath}");

        
        // Start import logging
        $importId = $importLogger->startImport(
            ImportTypes::Suppliers,
            ImportSources::Cli,
            $filePath,
            $disk
        );


            
        try {// Check if file exists
            if (!Storage::disk($disk)->exists($filePath)) {
            $this->error("File not found: {$filePath} on disk: {$disk}");
            $importLogger->failImport($importId, "File not found: {$filePath} on disk: {$disk}");
            return 1;
        }


            // Get file content and create temporary file (R2 compatible)
            $fileContent = Storage::disk($disk)->get($filePath);
            $tempPath = tempnam(sys_get_temp_dir(), 'suppliers_import_') . '.xlsx';
            file_put_contents($tempPath, $fileContent);

            // Read all rows from Excel
            $this->output->writeln('Reading Excel file...');
            $rows = SimpleExcelReader::create($tempPath)
                ->getRows()
                ->toArray();

            // Clean up temporary file
            unlink($tempPath);

            if (empty($rows)) {
                $this->error("No data found in Excel file.");
                $importLogger->failImport($importId, "File not found: {$filePath} on disk: {$disk}");
            return 1;
            }

            $this->info("Found " . count($rows) . " rows to process");
            $this->info("Queuing jobs for suppliers import...");

            $jobsQueued = 0;

            // Process each row
            foreach ($rows as $row) {
                // Clean the row data
                $cleanRow = collect($row)->map(fn($v) => trim((string) $v) === '' ? null : $v)->toArray();

                // Skip empty rows
                if (empty(array_filter($cleanRow))) {
                    continue;
                }

                // Queue job for this row
                ImportSupplierJob::dispatch($cleanRow, $importId);
                $jobsQueued++;
            }

            // Set total jobs and queue completion job
            $importLogger->setTotalJobs($importId, $jobsQueued);
            CompleteImportJob::dispatch($importId);

            $this->info("Successfully queued {$jobsQueued} jobs for suppliers import!");
            $this->info("Jobs will be processed by queue workers.");
            $this->info("Monitor progress with: php artisan queue:work");

            return 0;

        } catch (\Exception $e) {
            $this->error("Failed to process Excel file: " . $e->getMessage());
            $importLogger->failImport($importId, "File not found: {$filePath} on disk: {$disk}");
            return 1;
        }
    }
}
