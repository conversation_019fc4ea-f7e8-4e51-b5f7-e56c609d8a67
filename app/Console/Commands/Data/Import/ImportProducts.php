<?php

namespace App\Console\Commands\Data\Import;

use App\Enums\ImportSources;
use App\Enums\ImportTypes;
use App\Jobs\CompleteImportJob;
use App\Services\ImportLoggerService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Spatie\SimpleExcel\SimpleExcelReader;
use App\Jobs\Import\Products\ImportSimpleProductJob;
use App\Jobs\Import\Products\ImportVariantProductJob;
use App\Jobs\Import\Products\ImportModularProductJob;
use App\Jobs\Import\Products\ImportModularOptionJob;

class ImportProducts extends Command
{
    protected $signature = 'products:import {type?}';

    protected $description = 'Import products from Excel feeds';

    public function handle(ImportLoggerService $importLogger)
    {
        $type = $this->argument('type');

        // Determine source: CLI when run manually, Scheduler when run automatically
        $source = app()->runningInConsole() && !app()->environment('testing')
            ? ImportSources::Cli
            : ImportSources::Scheduler;

        if ($type === 'simple' || is_null($type)) {
            $this->output->title('Starting simple products sync...');
            $this->sync('simple_products', ImportSimpleProductJob::class, ImportTypes::SimpleProducts, $importLogger, $source);
        }

        if ($type === 'variant' || is_null($type)) {
            $this->output->title('Starting variant products sync...');
            $this->sync('variant_products', ImportVariantProductJob::class, ImportTypes::VariantProducts, $importLogger, $source);
        }

        if ($type === 'modular' || is_null($type)) {
            $this->output->title('Starting modular options sync...');
            $this->sync('modular_options', ImportModularOptionJob::class, ImportTypes::ModularOptions, $importLogger, $source);

            $this->output->title('Starting modular products sync...');
            $this->sync('modular_products', ImportModularProductJob::class, ImportTypes::ModularProducts, $importLogger, $source);
        }
    }

    protected function sync(string $feedKey, string $jobClass, ImportTypes $importType, ImportLoggerService $importLogger, ImportSources $source): void
    {
        $url = config("app.feeds.$feedKey");
        $fileName = "imports/products/{$feedKey}.xlsx";

        // Start import logging
        $importId = $importLogger->startImport(
            $importType,
            $source,
            $fileName,
            config('filesystems.private')
        );

        try {
            // Download the Excel file
            $this->output->writeln('Downloading Excel file...');
            $response = Http::get($url);
            if ($response->successful()) {
                Storage::disk(config('filesystems.private'))->put($fileName, $response->body());
            } else {
                $this->output->error('Failed to download Excel file');
                $importLogger->failImport($importId, 'Failed to download Excel file');
                return;
            }

            // Read all rows from Excel
            $this->output->writeln('Reading Excel file...');
            $fileContent = Storage::disk(config('filesystems.private'))->get($fileName);
            $tempPath = tempnam(sys_get_temp_dir(), 'excel_import_') . '.xlsx';
            file_put_contents($tempPath, $fileContent);

            $rows = SimpleExcelReader::create($tempPath)
                ->getRows()
                ->toArray();

            // Clean up temporary file
            unlink($tempPath);

            $totalRows = count($rows);
            $this->output->writeln("Found {$totalRows} items to import");

            // Dispatch jobs for each row
            $this->output->writeln('Dispatching jobs to queue...');
            $jobsDispatched = 0;

            foreach ($rows as $row) {
                // Clean the row data
                $cleanRow = collect($row)->map(fn($v) => trim((string) $v) === '' ? null : $v)->toArray();

                // Only dispatch job if SKU exists
                if (!empty($cleanRow['sku'])) {
                    dispatch(new $jobClass($cleanRow, $importId));
                    $jobsDispatched++;
                }
            }

            // Set total jobs and queue completion job
            $importLogger->setTotalJobs($importId, $jobsDispatched);
            CompleteImportJob::dispatch($importId);

            $this->newLine();
            $this->output->success("Successfully dispatched {$jobsDispatched} import jobs to the queue!");
            $this->output->writeln("Jobs will be processed by queue workers.");

        } catch (\Exception $e) {
            $this->output->error('Failed to process Excel file: ' . $e->getMessage());
            $importLogger->failImport($importId, $e->getMessage());
        }
    }
}
