<?php

namespace App\Console\Commands\Data\Import;

use App\Enums\ImportSources;
use App\Enums\ImportTypes;
use App\Jobs\CompleteImportJob;
use App\Models\Inventory;
use App\Services\ImportLoggerService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Spatie\SimpleExcel\SimpleExcelReader;
use App\Jobs\Import\Inventory\ImportInventoryRowJob;

class ImportInventory extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'inventory:import';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import inventory from Excel files using queue jobs';

    /**
     * Execute the console command.
     */
    public function handle(ImportLoggerService $importLogger)
    {
        $this->info("Starting inventory import from Excel files");

        // Get file paths from environment
        $filePathsString = config('app.feeds.inventory_import_files');
        if (!$filePathsString) {
            $this->error("APP_FEED_INVENTORY_IMPORT_FILES environment variable not set");
            return 1;
        }

        $filePaths = array_map('trim', explode(',', $filePathsString));
        $disk = config('filesystems.exchange', 'exchange');

        $this->info("Files to process: " . implode(', ', $filePaths));

        // Check if all files exist
        foreach ($filePaths as $filePath) {
            if (!Storage::disk($disk)->exists($filePath)) {
                $this->error("File not found: {$filePath} on disk: {$disk}");
                return 1;
            }
        }

        // Start import logging
        $importId = $importLogger->startImport(
            ImportTypes::Inventory,
            ImportSources::Cli,
            implode(', ', $filePaths), // Store all file paths
            $disk
        );

        try {
            // Clear existing inventory data
            $this->info("Clearing existing inventory data...");
            Inventory::truncate();

            $totalJobsQueued = 0;

            // Process each file
            foreach ($filePaths as $filePath) {
                $this->info("Processing file: {$filePath}");

                // Get file content and create temporary file (R2 compatible)
                $fileContent = Storage::disk($disk)->get($filePath);
                $tempPath = tempnam(sys_get_temp_dir(), 'inventory_import_') . '.xlsx';
                file_put_contents($tempPath, $fileContent);

                // Read all rows from Excel
                $this->output->writeln("Reading Excel file: {$filePath}...");
                $rows = SimpleExcelReader::create($tempPath)
                    ->getRows()
                    ->toArray();

                // Clean up temporary file
                unlink($tempPath);

                if (empty($rows)) {
                    $this->warn("No data found in Excel file: {$filePath}");
                    continue;
                }

                $this->info("Found " . count($rows) . " rows in {$filePath}");

                // Process each row
                foreach ($rows as $row) {
                    // Clean the row data
                    $cleanRow = collect($row)->map(fn($v) => trim((string) $v) === '' ? null : $v)->toArray();

                    // Skip empty rows
                    if (empty(array_filter($cleanRow))) {
                        continue;
                    }

                    // Queue job for this row with import ID
                    ImportInventoryRowJob::dispatch($cleanRow, $importId);
                    $totalJobsQueued++;
                }
            }

            // Set total jobs and queue completion job
            $importLogger->setTotalJobs($importId, $totalJobsQueued);
            CompleteImportJob::dispatch($importId);

            $this->info("Successfully queued {$totalJobsQueued} jobs for inventory import!");
            $this->info("Jobs will be processed by queue workers.");
            $this->info("Monitor progress with: php artisan queue:work");

            return 0;

        } catch (\Exception $e) {
            $this->error("Failed to process Excel files: " . $e->getMessage());
            $importLogger->failImport($importId, $e->getMessage());
            return 1;
        }
    }
}
