<?php

namespace App\Console\Commands\Data\Relations;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Spatie\SimpleExcel\SimpleExcelReader;
use App\Jobs\Import\Relations\ImportClientBrandJob;

class ImportClientBrands extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'client-brands:import
                            {file : Path to the Excel file}
                            {--disk=private : Storage disk to use (local, public, private)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import client brands from Excel file using queue jobs';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $filePath = $this->argument('file');
        $disk = $this->option('disk');

        $this->info("Starting client brands import from Excel file: {$filePath}");

        // Check if file exists
        if (!Storage::disk($disk)->exists($filePath)) {
            $this->error("File not found: {$filePath} on disk: {$disk}");
            return 1;
        }

        try {
            // Get file content and create temporary file (R2 compatible)
            $fileContent = Storage::disk($disk)->get($filePath);
            $tempPath = tempnam(sys_get_temp_dir(), 'client_brands_import_') . '.xlsx';
            file_put_contents($tempPath, $fileContent);

            // Read all rows from Excel
            $this->output->writeln('Reading Excel file...');
            $rows = SimpleExcelReader::create($tempPath)
                ->getRows()
                ->toArray();

            // Clean up temporary file
            unlink($tempPath);

            if (empty($rows)) {
                $this->error("No data found in Excel file.");
                return 1;
            }

            $this->info("Found " . count($rows) . " rows to process");
            $this->info("Queuing jobs for client brands import...");

            $jobsQueued = 0;

            // Process each row
            foreach ($rows as $row) {
                // Clean the row data
                $cleanRow = collect($row)->map(fn($v) => trim((string) $v) === '' ? null : $v)->toArray();

                // Skip empty rows
                if (empty(array_filter($cleanRow))) {
                    continue;
                }

                // Queue job for this row
                ImportClientBrandJob::dispatch($cleanRow);
                $jobsQueued++;
            }

            $this->info("Successfully queued {$jobsQueued} jobs for client brands import!");
            $this->info("Jobs will be processed by queue workers.");
            $this->info("Monitor progress with: php artisan queue:work");

            return 0;

        } catch (\Exception $e) {
            $this->error("Failed to process Excel file: " . $e->getMessage());
            return 1;
        }
    }
}
