<?php

namespace App\Console\Commands\Data\Delete;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Spatie\SimpleExcel\SimpleExcelReader;
use App\Jobs\Delete\DeleteProductJob;

class DeleteProducts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'products:delete';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete products from the source';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->output->title('Starting products deletion...');

        $this->sync('products_to_be_deleted');
    }

    protected function sync(string $feedKey): void
    {
        $url = config("app.feeds.$feedKey");
        $fileName = "imports/products/{$feedKey}.xlsx";

        // Download the Excel file
        $this->output->writeln('Downloading Excel file...');
        $response = Http::get($url);
        if ($response->successful()) {
            Storage::disk(config('filesystems.private'))->put($fileName, $response->body());
        } else {
            $this->output->error('Failed to download Excel file');
            return;
        }

        // Read all rows from Excel
        $this->output->writeln('Reading Excel file...');
        try {
            $fileContent = Storage::disk(config('filesystems.private'))->get($fileName);
            $tempPath = tempnam(sys_get_temp_dir(), 'excel_import_') . '.xlsx';
            file_put_contents($tempPath, $fileContent);

            $rows = SimpleExcelReader::create($tempPath)
                ->getRows()
                ->toArray();

            // Clean up temporary file
            unlink($tempPath);
        } catch (\Exception $e) {
            $this->output->error('Failed to read Excel file: ' . $e->getMessage());
            return;
        }

        $totalRows = count($rows);
        $this->output->writeln("Found {$totalRows} products to delete");

        // Dispatch jobs for each row
        $this->output->writeln('Dispatching jobs to queue...');
        $jobsDispatched = 0;

        foreach ($rows as $row) {
            // Clean the row data
            $cleanRow = collect($row)->map(fn($v) => trim((string) $v) === '' ? null : $v)->toArray();

            // Only dispatch job if SKU exists
            if (!empty($cleanRow['sku'])) {
                DeleteProductJob::dispatch($cleanRow['sku']);
                $jobsDispatched++;
            }
        }

        $this->newLine();
        $this->output->success("Successfully dispatched {$jobsDispatched} delete jobs to the queue!");
        $this->output->writeln("Jobs will be processed by queue workers.");
    }
}
