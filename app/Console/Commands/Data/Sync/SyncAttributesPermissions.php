<?php

namespace App\Console\Commands\Data\Sync;

use App\Enums\ModelTypes;
use Illuminate\Console\Command;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class SyncAttributesPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-attributes-permissions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Adds default permissions to all existing roles for the required attributes of each resource';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to sync attributes permissions...');

        $existingRoles = Role::all();
        $permissionsToCreate = [];

        foreach (ModelTypes::cases() as $modelType) {
            $attributes = $modelType->matchAttributes();
            if ($attributes) {
                foreach ($attributes as $attribute) {
                    if ($attribute['required'] ?? false) {
                        $permissionName = "view_{$modelType->value}_{$attribute['key']}";
                        $permissionsToCreate[] = $permissionName;
                    }
                }
            }
        }

        foreach ($permissionsToCreate as $permissionName) {
            $permission = Permission::firstOrCreate(['name' => $permissionName]);

            foreach ($existingRoles as $role) {
                if (!$role->hasPermissionTo($permission)) {
                    $role->givePermissionTo($permission);
                    $this->info("Added permission: {$permissionName} to role: {$role->name}");
                }
            }
        }

        $this->info('Ending sync attributes permissions.');

        return Command::SUCCESS;
    }
}
