<?php

namespace App\Jobs\Import\Relations;

use App\Models\Brand;
use App\Models\DiscountGroup;
use Illuminate\Support\Facades\Log;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class ImportDiscountGroupJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public array $row
    ) {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $code = trim($this->row['code'] ?? '');

            if (empty($code)) {
                Log::warning("Skipping discount group row with empty code", ['row' => $this->row]);
                return;
            }

            // Skip if already exists
            if (DiscountGroup::where('code', $code)->exists()) {
                Log::info("Discount group already exists, skipping: {$code}");
                return;
            }

            Log::info("Processing discount group import for code: {$code}");

            $discountGroup = DiscountGroup::create([
                'code' => $code,
                'discount' => $this->row['discount'] ?? 0,
                'description' => $this->row['description'] ?? null,
            ]);

            // Associate with brand if specified
            $brandPrefix = trim($this->row['brand_prefix'] ?? '');
            if (!empty($brandPrefix)) {
                $brand = Brand::where('prefix', $brandPrefix)->first();

                if ($brand) {
                    $discountGroup->brand()->associate($brand);
                    $discountGroup->save();
                    Log::info("Associated discount group {$code} with brand {$brandPrefix}");
                } else {
                    Log::warning("Brand not found for prefix: {$brandPrefix}", ['discount_group_code' => $code]);
                }
            }

            Log::info("Successfully imported discount group: {$code}");

        } catch (\Exception $e) {
            Log::error("Failed to import discount group from Excel row", [
                'row' => $this->row,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("ImportDiscountGroupJob failed after all retries", [
            'row' => $this->row,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
