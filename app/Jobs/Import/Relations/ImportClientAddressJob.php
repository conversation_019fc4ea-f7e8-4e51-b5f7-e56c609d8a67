<?php

namespace App\Jobs\Import\Relations;

use App\Models\Client;
use App\Models\Address;
use Illuminate\Support\Facades\Log;
use App\Jobs\Import\Concerns\LogsImportProgress;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class ImportClientAddressJob implements ShouldQueue
{
    use Queueable, LogsImportProgress;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public array $row
    ) {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $clientId = trim($this->row['client_id'] ?? '');

            if (empty($clientId)) {
                Log::warning("Skipping client address row with empty client_id", ['row' => $this->row]);
                return;
            }

            Log::info("Processing client address import for client_id: {$clientId}");

            $client = Client::find($clientId);

            if (!$client) {
                Log::warning("Client not found for ID: {$clientId}");
                return;
            }

            Address::create([
                'addressable_id' => $client->id,
                'addressable_type' => Client::class,
                'name' => $this->row['name'] ?? null,
                'type' => $this->getValidAddressType($this->row['type'] ?? null),
                'parent_id' => $this->row['parent_id'] ?? null,
                'code_invoicing' => $this->row['code_invoicing'] ?? null,
                'code_shipping' => $this->row['code_shipping'] ?? null,
                'company' => $this->row['company'] ?? null,
                'vat_number' => $this->row['vat_number'] ?? null,
                'fiscal_code' => $this->row['fiscal_code'] ?? null,
                'sdi_code' => $this->row['sdi_code'] ?? null,
                'street' => $this->row['street'] ?? null,
                'city' => $this->row['city'] ?? null,
                'zip' => $this->row['zip'] ?? null,
                'state' => $this->row['state'] ?? null,
                'country' => $this->getValidCountry($this->row['country'] ?? null),
            ]);

            Log::info("Successfully imported client address for client_id: {$clientId}");

        
            // Log success for import tracking
            $this->logSuccess();

        
        } catch (\Exception $e) {
            Log::error("Failed to import client address from Excel row", [
                'row' => $this->row,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Get valid address type
     */
    private function getValidAddressType(?string $type): string
    {
        if (empty($type)) {
            return \App\Enums\AddressTypes::Invoicing->value;
        }

        $validTypes = array_map(fn($type) => $type->value, \App\Enums\AddressTypes::cases());
        return in_array(strtolower($type), $validTypes) ? strtolower($type) : \App\Enums\AddressTypes::Invoicing->value;
    }

    /**
     * Get valid country
     */
    private function getValidCountry(?string $country): ?string
    {
        if (empty($country)) {
            return null;
        }

        $validCountries = array_map(fn($country) => $country->value, \PrinsFrank\Standards\Country\CountryAlpha3::cases());
        return in_array(strtoupper($country), $validCountries) ? strtoupper($country) : null;
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        // Log the failure for import tracking
        $this->logFailure($exception);

        Log::error("ImportClientAddressJob failed after all retries", [
            'row' => $this->row,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
