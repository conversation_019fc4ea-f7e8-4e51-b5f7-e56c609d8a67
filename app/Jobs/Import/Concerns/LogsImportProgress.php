<?php

namespace App\Jobs\Import\Concerns;

use App\Services\ImportLoggerService;
use Illuminate\Support\Facades\Log;

trait LogsImportProgress
{

    /**
     * Log successful completion of the job.
     */
    protected function logSuccess(): void
    {
        $importId = $this->importId ?? null;

        if ($importId) {
            try {
                app(ImportLoggerService::class)->incrementProcessedJobs($importId);
                Log::info("Import progress logged", [
                    'import_id' => $importId,
                    'job_class' => static::class,
                ]);
            } catch (\Exception $e) {
                Log::error("Failed to log import progress", [
                    'import_id' => $importId,
                    'job_class' => static::class,
                    'error' => $e->getMessage(),
                ]);
            }
        } else {
            Log::warning("No import ID found for job", [
                'job_class' => static::class,
            ]);
        }
    }

    /**
     * Log job failure with import context.
     */
    protected function logFailure(\Throwable $exception): void
    {
        if ($this->importId) {
            try {
                app(ImportLoggerService::class)->logError(
                    $this->importId,
                    static::class,
                    $this->row ?? [],
                    $exception->getMessage(),
                    $exception->getTraceAsString()
                );
            } catch (\Exception $e) {
                Log::error("Failed to log import error", [
                    'import_id' => $this->importId,
                    'job_class' => static::class,
                    'original_error' => $exception->getMessage(),
                    'logging_error' => $e->getMessage(),
                ]);
            }
        }
    }

    /**
     * Handle a job failure with import logging.
     */
    public function failed(\Throwable $exception): void
    {
        $this->logFailure($exception);

        // Call parent failed method if it exists
        if (method_exists(parent::class, 'failed')) {
            parent::failed($exception);
        }
    }
}
