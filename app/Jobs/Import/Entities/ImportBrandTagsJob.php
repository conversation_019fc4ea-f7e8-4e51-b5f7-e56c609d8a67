<?php

namespace App\Jobs\Import\Entities;

use App\Models\Brand;
use Illuminate\Support\Facades\Log;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class ImportBrandTagsJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public array $row
    ) {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $brandPrefix = trim($this->row['BRAND_PREFIX'] ?? '');

            if (empty($brandPrefix)) {
                Log::warning("Skipping row with empty BRAND_PREFIX", ['row' => $this->row]);
                return;
            }

            // Find brand by prefix
            $brand = Brand::where('prefix', $brandPrefix)->first();

            if (!$brand) {
                Log::warning("Brand not found for prefix: {$brandPrefix}", ['row' => $this->row]);
                return;
            }

            Log::info("Processing brand tags import for: {$brand->name} (prefix: {$brandPrefix})");

            // Process each tag type
            $this->attachTagsFromColumn($brand, 'TYPE', 'type');
            $this->attachTagsFromColumn($brand, 'MATERIAL', 'material');
            $this->attachTagsFromColumn($brand, 'DESTINATION_ROOM', 'destination_room');
            $this->attachTagsFromColumn($brand, 'STYLE', 'style');

            Log::info("Successfully imported tags for brand: {$brand->name} (prefix: {$brandPrefix})");

        } catch (\Exception $e) {
            Log::error("Failed to import brand tags from Excel row", [
                'row' => $this->row,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Attach tags from a specific column to the brand
     */
    private function attachTagsFromColumn(Brand $brand, string $columnName, string $tagType): void
    {
        $columnValue = trim($this->row[$columnName] ?? '');

        if (empty($columnValue)) {
            return;
        }

        // Split by comma and clean up
        $tags = array_map('trim', explode(',', $columnValue));
        $tags = array_filter($tags); // Remove empty values

        if (empty($tags)) {
            return;
        }

        // Attach tags (Spatie handles duplicates automatically)
        $brand->attachTags($tags, $tagType);

        Log::info("Attached {$tagType} tags to brand {$brand->name}", [
            'tags' => $tags,
            'count' => count($tags)
        ]);
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("ImportBrandTagsJob failed after all retries", [
            'row' => $this->row,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
