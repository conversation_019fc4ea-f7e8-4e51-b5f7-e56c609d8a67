<?php

namespace App\Jobs\Import\Entities;

use App\Models\Brand;
use App\Models\Supplier;
use Illuminate\Support\Facades\Log;
use App\Jobs\Import\Concerns\LogsImportProgress;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class ImportSupplierJob implements ShouldQueue
{
    use Queueable, LogsImportProgress;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public array $row,
        public ?int $importId = null
    ) {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $code = trim($this->row['code'] ?? '');

            if (empty($code)) {
                Log::warning("Skipping supplier row with empty code", ['row' => $this->row]);
                return;
            }

            Log::info("Processing supplier import for code: {$code}");

            // Upsert supplier
            Supplier::upsert([
                'code' => $code,
                'name' => $this->row['name'] ?? null,
                'payment_conditions' => $this->row['payment_conditions'] ?? null,
            ], ['code']);

            // Get the supplier
            $supplier = Supplier::where('code', $code)->first();

            // Attach brand if specified
            $brandPrefix = trim($this->row['brand_prefix'] ?? '');
            if (!empty($brandPrefix)) {
                $brand = Brand::where('prefix', $brandPrefix)->first();

                if ($brand) {
                    if (!$supplier->brands()->where('brand_id', $brand->id)->exists()) {
                        $supplier->brands()->attach($brand);
                        Log::info("Attached brand {$brandPrefix} to supplier {$code}");
                    }
                } else {
                    Log::warning("Brand not found for prefix: {$brandPrefix}", ['supplier_code' => $code]);
                }
            }

            Log::info("Successfully imported supplier: {$code}");

        
            // Log success for import tracking
            $this->logSuccess();

        
        } catch (\Exception $e) {
            Log::error("Failed to import supplier from Excel row", [
                'row' => $this->row,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        // Log the failure for import tracking
        $this->logFailure($exception);

        Log::error("ImportSupplierJob failed after all retries", [
            'row' => $this->row,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
