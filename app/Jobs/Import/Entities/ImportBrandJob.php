<?php

namespace App\Jobs\Import\Entities;

use Carbon\Carbon;
use App\Models\Brand;
use App\Jobs\Import\Concerns\LogsImportProgress;
use Illuminate\Support\Facades\Log;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class ImportBrandJob implements ShouldQueue
{
    use Queueable, LogsImportProgress;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public array $row,
        public ?int $importId = null
    ) {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $prefix = trim($this->row['prefix'] ?? '');

            if (empty($prefix)) {
                Log::warning("Skipping brand row with empty prefix", ['row' => $this->row]);
                return;
            }

            Log::info("Processing brand import for prefix: {$prefix}");

            Brand::upsert([
                'prefix' => $prefix,
                'name' => $this->row['name'] ?? null,
                'price_range' => $this->row['price_range'] ?? null,
                'rating' => $this->row['rating'] ?? null,
                'partnership_level' => $this->row['parternship_level'] ?? null,
                'lead_time' => $this->row['lead_time'] ?? null,
                'purchase_price_list' => $this->row['purchase_price_list'] ?? null,
                'purchase_conditions' => $this->row['purchase_conditions'] ?? null,
                'minimum_orderable' => isset($this->row['minimum_orderable']) && $this->row['minimum_orderable'] ? ($this->row['minimum_orderable'] * 100) : 0,
                'extra_costs' => $this->row['extra_costs'] ?? null,
                'delivery_terms' => $this->getValidDeliveryTerm($this->row['delivery_terms'] ?? null),
                'notes' => $this->row['notes'] ?? null,
                'yearly_bonus_info' => $this->row['yearly_bonus_info'] ?? null,
                'catalogs' => $this->row['catalogs'] ?? null,
                'pricelist' => $this->row['pricelist'] ?? null,
                'valid_from' => $this->parseDate($this->row['valid_from'] ?? null),
                'expected_pricelist_update' => $this->parseDate($this->row['expected_pricelist_update'] ?? null),
                'social_link' => $this->row['social_link'] ?? null,
                'supplier_media_link' => $this->row['supplier_media_link'] ?? null,
                'supplier_media_link_user' => $this->row['supplier_media_link_user'] ?? null,
                'supplier_media_link_password' => $this->row['supplier_media_link_password'] ?? null,
                'image' => isset($this->row['image']) && $this->row['image'] ? 'brands/' . $this->row['image'] : null,
            ], ['prefix']);

            Log::info("Successfully imported brand: {$prefix}");

            // Log success for import tracking
            $this->logSuccess();

        } catch (\Exception $e) {
            Log::error("Failed to import brand from Excel row", [
                'row' => $this->row,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Parse date from string format
     */
    private function parseDate(?string $date): ?Carbon
    {
        if (empty($date)) {
            return null;
        }

        try {
            return Carbon::createFromFormat('d/m/Y', $date);
        } catch (\Exception $e) {
            Log::warning("Failed to parse date: {$date}", ['error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * Get valid delivery term
     */
    private function getValidDeliveryTerm(?string $term): ?string
    {
        if (empty($term)) {
            return null;
        }

        $validTerms = array_map(fn($term) => $term->value, \App\Enums\DeliveryTerms::cases());
        return in_array(strtolower($term), $validTerms) ? strtolower($term) : null;
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        // Log the failure for import tracking
        $this->logFailure($exception);

        Log::error("ImportBrandJob failed after all retries", [
            'row' => $this->row,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
