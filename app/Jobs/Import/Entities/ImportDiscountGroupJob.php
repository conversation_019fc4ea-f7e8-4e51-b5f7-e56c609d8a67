<?php

namespace App\Jobs\Import\Entities;

use App\Models\Brand;
use App\Models\DiscountGroup;
use Illuminate\Bus\Queueable;
use App\Jobs\Import\Concerns\LogsImportProgress;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ImportDiscountGroupJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, LogsImportProgress;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 120;

    /**
     * The discount group row data.
     *
     * @var array
     */
    public $row;

    /**
     * Create a new job instance.
     */
    public function __construct(array $row, ?int $importId = null)
    {
        $this->row = $row;
        $this->importId = $importId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $code = trim($this->row['code'] ?? '');

            if (empty($code)) {
                Log::warning("Skipping discount group row with empty code", ['row' => $this->row]);
                return;
            }

            Log::info("Processing discount group import for code: {$code}");

            // Create or update discount group
            $discountGroup = DiscountGroup::updateOrCreate(
                ['code' => $code],
                [
                    'discount' => $this->row['discount'] ?? 0,
                    'description' => $this->row['description'] ?? null,
                ]
            );

            // Associate with brand if specified
            $brandPrefix = trim($this->row['brand_prefix'] ?? '');
            if (!empty($brandPrefix)) {
                $brand = Brand::where('prefix', $brandPrefix)->first();

                if ($brand) {
                    $discountGroup->brand()->associate($brand);
                    $discountGroup->save();
                    Log::info("Associated discount group {$code} with brand {$brandPrefix}");
                } else {
                    Log::warning("Brand not found for prefix: {$brandPrefix}", ['discount_group_code' => $code]);
                }
            }

            Log::info("Successfully imported discount group: {$code}");

        
            // Log success for import tracking
            $this->logSuccess();

        
        } catch (\Exception $e) {
            Log::error("Failed to import discount group from Excel row", [
                'row' => $this->row,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }
}
