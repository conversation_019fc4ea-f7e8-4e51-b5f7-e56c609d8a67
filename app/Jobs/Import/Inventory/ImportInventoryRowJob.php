<?php

namespace App\Jobs\Import\Inventory;

use Carbon\Carbon;
use App\Models\Product;
use App\Models\Inventory;
use App\Enums\WarehouseNames;
use App\Jobs\Import\Concerns\LogsImportProgress;
use Illuminate\Support\Facades\Log;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class ImportInventoryRowJob implements ShouldQueue
{
    use Queueable, LogsImportProgress;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public array $row,
        public ?int $importId = null
    ) {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $adhocSku = trim($this->row['CODICE_RICERCA_ARTICOLO'] ?? '');
            $warehouseCode = trim($this->row['MAGAZZINO'] ?? '');
            $giacenza = $this->row['GIACENZA'] ?? null;
            $qtaInevasa = $this->row['QTA_INEVASA'] ?? null;
            $dataArrivo = $this->row['DATA_ARRIVO_PREVISTA'] ?? null;

            if (empty($adhocSku)) {
                Log::warning("Skipping inventory row with empty CODICE_RICERCA_ARTICOLO", ['row' => $this->row]);
                return;
            }

            if (empty($warehouseCode)) {
                Log::warning("Skipping inventory row with empty MAGAZZINO", ['row' => $this->row]);
                return;
            }

            Log::info("Processing inventory import for adhoc_sku: {$adhocSku}, warehouse: {$warehouseCode}");

            // Find product by adhoc_sku to get the actual SKU
            $product = Product::where('adhoc_sku', $adhocSku)->first();
            if (!$product) {
                Log::warning("Product not found for adhoc_sku: {$adhocSku}", ['row' => $this->row]);
                return;
            }

            $sku = $product->sku;

            // Map warehouse code to enum
            $warehouse = $this->mapWarehouseCode($warehouseCode);
            if (!$warehouse) {
                Log::warning("Invalid warehouse code: {$warehouseCode}", ['row' => $this->row]);
                return;
            }

            // Process GIACENZA (present stock) - only if not already exists
            if (!empty($giacenza) && is_numeric($giacenza)) {
                $existingStock = Inventory::where('sku', $sku)
                    ->where('warehouse', $warehouse)
                    ->whereNull('date')
                    ->exists();

                if (!$existingStock) {
                    Inventory::create([
                        'sku' => $sku,
                        'warehouse' => $warehouse,
                        'quantity' => (int) $giacenza,
                        'date' => null,
                    ]);

                    Log::info("Created present stock record for SKU: {$sku}, warehouse: {$warehouse->value}, quantity: {$giacenza}");
                }
            }

            // Process QTA_INEVASA (incoming stock) - always create if populated
            if (!empty($qtaInevasa) && is_numeric($qtaInevasa) && !empty($dataArrivo)) {
                $arrivalDate = $this->parseDate($dataArrivo);
                
                if ($arrivalDate) {
                    Inventory::create([
                        'sku' => $sku,
                        'warehouse' => $warehouse,
                        'quantity' => (int) $qtaInevasa,
                        'date' => $arrivalDate,
                    ]);

                    Log::info("Created incoming stock record for SKU: {$sku}, warehouse: {$warehouse->value}, quantity: {$qtaInevasa}, date: {$arrivalDate->format('Y-m-d')}");
                }
            }

            Log::info("Successfully processed inventory row for SKU: {$sku}");

            // Log success for import tracking
            $this->logSuccess();

        } catch (\Exception $e) {
            Log::error("Failed to import inventory from Excel row", [
                'row' => $this->row,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Map warehouse code to WarehouseNames enum
     */
    private function mapWarehouseCode(string $code): ?WarehouseNames
    {
        foreach (WarehouseNames::cases() as $warehouse) {
            if ($warehouse->adhocCode() === $code) {
                return $warehouse;
            }
        }
        return null;
    }

    /**
     * Parse date from d-m-Y format
     */
    private function parseDate(?string $date): ?Carbon
    {
        if (empty($date)) {
            return null;
        }

        try {
            return Carbon::createFromFormat('d-m-Y', $date);
        } catch (\Exception $e) {
            Log::warning("Failed to parse date: {$date}", ['error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        // Log the failure for import tracking
        $this->logFailure($exception);

        Log::error("ImportInventoryRowJob failed after all retries", [
            'row' => $this->row,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
