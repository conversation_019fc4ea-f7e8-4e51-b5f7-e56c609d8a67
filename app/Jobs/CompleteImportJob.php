<?php

namespace App\Jobs;

use App\Services\ImportLoggerService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;

class CompleteImportJob implements ShouldQueue
{
    use Queueable;

    /**
     * The import ID to complete.
     */
    public int $importId;

    /**
     * Create a new job instance.
     */
    public function __construct(int $importId)
    {
        $this->importId = $importId;
    }

    /**
     * Execute the job.
     */
    public function handle(ImportLoggerService $importLogger): void
    {
        try {
            Log::info("Completing import", ['import_id' => $this->importId]);

            $importLogger->completeImport($this->importId);

            Log::info("Import completion job finished", ['import_id' => $this->importId]);
        } catch (\Exception $e) {
            Log::error("Failed to complete import", [
                'import_id' => $this->importId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("CompleteImportJob failed", [
            'import_id' => $this->importId,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);
    }
}
