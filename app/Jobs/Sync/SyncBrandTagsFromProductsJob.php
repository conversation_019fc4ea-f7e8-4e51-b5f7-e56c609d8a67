<?php

namespace App\Jobs\Sync;

use App\Models\Brand;
use Illuminate\Support\Facades\Log;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SyncBrandTagsFromProductsJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public Brand $brand,
        public bool $force = false
    ) {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info("Starting tag sync for brand: {$this->brand->name} (ID: {$this->brand->id})");

            $result = $this->syncBrandTags();

            if ($result) {
                Log::info("Successfully synced tags for brand: {$this->brand->name} (ID: {$this->brand->id})");
            } else {
                Log::info("Skipped tag sync for brand: {$this->brand->name} (ID: {$this->brand->id}) - already has tags or no tags to sync");
            }
        } catch (\Exception $e) {
            Log::error("Failed to sync tags for brand: {$this->brand->name} (ID: {$this->brand->id})", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Sync tags for the brand from its products
     */
    private function syncBrandTags(): bool
    {
        // Load products with tags
        $this->brand->load(['products.tags']);

        // Remove existing tags of these types if force is enabled
        if ($this->force) {
            $this->brand->tags()->whereIn('type', ['type', 'material', 'destination_room', 'style'])->detach();
        }

        // Collect all unique values for each type
        $typeValues = collect();
        $materialTags = collect();
        $destinationRoomTags = collect();
        $styleTags = collect();

        foreach ($this->brand->products as $product) {
            // For 'type' - get from product field (not tags)
            if (!empty($product->type)) {
                $typeValues->push($product->type);
            }

            // For the other types - get from tags
            $materialTags = $materialTags->merge($product->tags->where('type', 'material'));
            $destinationRoomTags = $destinationRoomTags->merge($product->tags->where('type', 'destination_room'));
            $styleTags = $styleTags->merge($product->tags->where('type', 'style'));
        }

        $initialTagCount = $this->brand->tags()->whereIn('type', ['type', 'material', 'destination_room', 'style'])->count();

        // Attach 'type' tags from product type field (Spatie handles duplicates automatically)
        $uniqueTypes = $typeValues->unique()->filter();
        if ($uniqueTypes->isNotEmpty()) {
            $this->brand->attachTags($uniqueTypes->toArray(), 'type');
        }

        // Attach existing material tags (Spatie handles duplicates automatically)
        $uniqueMaterialTags = $materialTags->unique('id');
        foreach ($uniqueMaterialTags as $tag) {
            $this->brand->attachTag($tag);
        }

        // Attach existing destination_room tags (Spatie handles duplicates automatically)
        $uniqueDestinationRoomTags = $destinationRoomTags->unique('id');
        foreach ($uniqueDestinationRoomTags as $tag) {
            $this->brand->attachTag($tag);
        }

        // Attach existing style tags (Spatie handles duplicates automatically)
        $uniqueStyleTags = $styleTags->unique('id');
        foreach ($uniqueStyleTags as $tag) {
            $this->brand->attachTag($tag);
        }

        $finalTagCount = $this->brand->tags()->whereIn('type', ['type', 'material', 'destination_room', 'style'])->count();

        return $finalTagCount > $initialTagCount || $this->force;
    }
}
