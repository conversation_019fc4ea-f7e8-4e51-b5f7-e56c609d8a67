<?php

namespace App\Jobs;

use App\Services\ExportLoggerService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;

class CompleteExportJob implements ShouldQueue
{
    use Queueable;

    /**
     * The export ID to complete.
     */
    public int $exportId;

    /**
     * Create a new job instance.
     */
    public function __construct(int $exportId)
    {
        $this->exportId = $exportId;
    }

    /**
     * Execute the job.
     */
    public function handle(ExportLoggerService $exportLogger): void
    {
        try {
            Log::info("Completing export", ['export_id' => $this->exportId]);

            $exportLogger->completeExport($this->exportId);

            Log::info("Export completion job finished", ['export_id' => $this->exportId]);
        } catch (\Exception $e) {
            Log::error("Failed to complete export", [
                'export_id' => $this->exportId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("CompleteExportJob failed", [
            'export_id' => $this->exportId,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);
    }
}
