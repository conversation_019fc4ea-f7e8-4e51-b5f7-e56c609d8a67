<?php

namespace App\Jobs\Export;

use App\Enums\ExportSources;
use App\Enums\ExportTypes;
use App\Jobs\CompleteExportJob;
use App\Models\Order\Order;
use App\Services\ExportLoggerService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;

class ExportOrderFilesJob implements ShouldQueue
{
    use Queueable;

    /**
     * The number of times the job may be attempted.
     */
    public $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     */
    public $timeout = 300;

    /**
     * The order to export.
     */
    public Order $order;

    /**
     * The export source.
     */
    public ExportSources $source;

    /**
     * The user ID who initiated the export.
     */
    public ?int $userId;

    /**
     * Create a new job instance.
     */
    public function __construct(Order $order, ExportSources $source = ExportSources::OrderSubmit, ?int $userId = null)
    {
        $this->order = $order;
        $this->source = $source;
        $this->userId = $userId;
    }

    /**
     * Execute the job.
     */
    public function handle(ExportLoggerService $exportLogger): void
    {
        try {
            Log::info("Starting order files export", [
                'order_id' => $this->order->id,
                'source' => $this->source->value,
            ]);

            // Start the export logging
            $exportId = $exportLogger->startExport(
                ExportTypes::OrderFiles,
                $this->source,
                $this->order->id,
                get_class($this->order),
                config('filesystems.exchange', 'exchange'),
                $this->userId
            );

            // Set total files to export (3: invoicing, shipping, order_rows)
            $exportLogger->setTotalFiles($exportId, 3);

            // Generate timestamp for file naming
            $timestamp = now()->format('YmdHis');

            // Dispatch individual file export jobs in sequence
            // 1. Invoicing Address
            ExportInvoicingAddressJob::dispatch(
                $this->order,
                $exportId,
                $timestamp
            )->onQueue('default');

            // 2. Shipping Address  
            ExportShippingAddressJob::dispatch(
                $this->order,
                $exportId,
                $timestamp
            )->onQueue('default');

            // 3. Order Rows
            ExportOrderRowsJob::dispatch(
                $this->order,
                $exportId,
                $timestamp
            )->onQueue('default');

            // Dispatch completion job to run after all files are processed
            CompleteExportJob::dispatch($exportId)
                ->delay(now()->addMinutes(2)) // Give time for file jobs to complete
                ->onQueue('default');

            Log::info("Order files export jobs dispatched", [
                'order_id' => $this->order->id,
                'export_id' => $exportId,
            ]);

        } catch (\Exception $e) {
            Log::error("Failed to start order files export", [
                'order_id' => $this->order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // If we have an export ID, mark it as failed
            if (isset($exportId)) {
                $exportLogger->failExport($exportId, $e->getMessage());
            }

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("ExportOrderFilesJob failed", [
            'order_id' => $this->order->id,
            'source' => $this->source->value,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);

        // TODO: Add alert/notification logic here for future use
        // This is where we can add email notifications, Slack alerts, etc.
    }
}
