<?php

namespace App\Jobs\Export\Concerns;

use App\Services\ExportLoggerService;
use Illuminate\Support\Facades\Log;

trait LogsExportProgress
{
    /**
     * Log successful completion of the file export.
     */
    protected function logSuccess(string $filePath): void
    {
        $exportId = $this->exportId ?? null;

        if ($exportId) {
            try {
                $exportLogger = app(ExportLoggerService::class);
                $exportLogger->incrementProcessedFiles($exportId);
                $exportLogger->addFilePath($exportId, $filePath);
                
                Log::info("Export progress logged", [
                    'export_id' => $exportId,
                    'file_type' => $this->fileType ?? 'unknown',
                    'file_path' => $filePath,
                ]);
            } catch (\Exception $e) {
                Log::error("Failed to log export progress", [
                    'export_id' => $exportId,
                    'file_type' => $this->fileType ?? 'unknown',
                    'error' => $e->getMessage(),
                ]);
            }
        } else {
            Log::warning("No export ID found for job", [
                'file_type' => $this->fileType ?? 'unknown',
            ]);
        }
    }

    /**
     * Handle a job failure and log the error.
     */
    public function failed(\Throwable $exception): void
    {
        $exportId = $this->exportId ?? null;

        if ($exportId) {
            try {
                app(ExportLoggerService::class)->logError(
                    $exportId,
                    $this->fileType ?? 'unknown',
                    $exception->getMessage(),
                    $exception->getTraceAsString()
                );

                Log::error("Export job failed", [
                    'export_id' => $exportId,
                    'file_type' => $this->fileType ?? 'unknown',
                    'error' => $exception->getMessage(),
                ]);
            } catch (\Exception $e) {
                Log::error("Failed to log export failure", [
                    'export_id' => $exportId,
                    'file_type' => $this->fileType ?? 'unknown',
                    'original_error' => $exception->getMessage(),
                    'logging_error' => $e->getMessage(),
                ]);
            }
        } else {
            Log::error("Export job failed without export ID", [
                'file_type' => $this->fileType ?? 'unknown',
                'error' => $exception->getMessage(),
            ]);
        }
    }
}
