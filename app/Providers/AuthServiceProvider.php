<?php

namespace App\Providers;

use App\Models\Brand;
use App\Models\Client;
use App\Models\Collection;
use App\Models\Contact;
use App\Models\DiscountGroup;
use App\Models\Order\Order;
use App\Models\Partner;
use App\Models\Product;
use App\Models\Project\Project;
use App\Models\Supplier;
use App\Models\Ticket;
use App\Models\User;
use App\Policies\BrandPolicy;
use App\Policies\ClientPolicy;
use App\Policies\CollectionPolicy;
use App\Policies\ContactPolicy;
use App\Policies\DiscountGroupPolicy;
use App\Policies\OrderPolicy;
use App\Policies\PartnerPolicy;
use App\Policies\ProductPolicy;
use App\Policies\ProjectPolicy;
use App\Policies\RolePolicy;
use App\Policies\SupplierPolicy;
use App\Policies\TicketPolicy;
use App\Policies\UserPolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;
use Spatie\Permission\Models\Role;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array
     */
    protected $policies = [
        Brand::class => BrandPolicy::class,
        Client::class => ClientPolicy::class,
        Collection::class => CollectionPolicy::class,
        Contact::class => ContactPolicy::class,
        DiscountGroup::class => DiscountGroupPolicy::class,
        Order::class => OrderPolicy::class,
        Partner::class => PartnerPolicy::class,
        Product::class => ProductPolicy::class,
        Project::class => ProjectPolicy::class,
        Role::class => RolePolicy::class,
        Supplier::class => SupplierPolicy::class,
        Ticket::class => TicketPolicy::class,
        User::class => UserPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();

        // Bypass @can if user has 'admin' role
        Gate::before(function ($user, $ability) {
            return $user->isAdmin() ? true : null;
        });
    }
}
