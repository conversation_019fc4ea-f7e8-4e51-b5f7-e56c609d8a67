<?php

namespace App\Observers;

use App\Models\Partner;

class PartnerObserver
{
    /**
     * Handle the Partner "created" event.
     */
    public function created(Partner $partner): void
    {

        // Attach creator to the partner
        $partner->users()->attach(auth()->id());

    }

    /**
     * Handle the Partner "deleted" event.
     */
    public function deleted(Partner $partner): void
    {
        $partner->users()->detach();
    }
}
