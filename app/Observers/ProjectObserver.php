<?php

namespace App\Observers;

use App\Models\Project\Project;

class ProjectObserver
{
    /**
     * Handle the Project "created" event.
     */
    public function created(Project $project): void
    {
        $region = is_object($project->project_code_region) ? $project->project_code_region->code() : '';
        $date = $project->project_code_date ? $project->project_code_date->format('y') : '';
        $progressive = str_pad($project->project_code_progressive, 6, '0', STR_PAD_LEFT);

        $project->project_code = $project->project_code_progressive
            ? "PRJ_{$region}{$date}-{$progressive}"
            : null;

        $project->saveQuietly();

        // Attach creator to the project
        $project->users()->attach(auth()->id());

    }

    /**
     * Handle the Project "updated" event.
     */
    public function updated(Project $project): void
    {
        $region = is_object($project->project_code_region) ? $project->project_code_region->code() : '';
        $date = $project->project_code_date ? $project->project_code_date->format('y') : '';
        $progressive = str_pad($project->project_code_progressive, 6, '0', STR_PAD_LEFT);

        $project->project_code = $project->project_code_progressive
            ? "PRJ_{$region}{$date}-{$progressive}"
            : null;

        $project->saveQuietly();
    }

    /**
     * Handle the Project "deleted" event.
     */
    public function deleted(Project $project): void
    {
        $project->users()->detach();
    }

    /**
     * Handle the Project "restored" event.
     */
    public function restored(Project $project): void
    {
        //
    }

    /**
     * Handle the Project "force deleted" event.
     */
    public function forceDeleted(Project $project): void
    {
        //
    }
}
