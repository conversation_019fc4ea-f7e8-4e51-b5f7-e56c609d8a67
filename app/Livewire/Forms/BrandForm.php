<?php

namespace App\Livewire\Forms;

use Livewire\Form;
use App\Models\Brand;
use Livewire\WithFileUploads;
use Illuminate\Http\UploadedFile;

class BrandForm extends Form
{
    use WithFileUploads;

    public $isEditMode = false;
    public ?Brand $brand;
    public $image;
    public $prefix;
    public $name;
    public $price_range;
    public $rating;
    public $partnership_level;
    public $lead_time;
    public $purchase_price_list;
    public $purchase_conditions;
    public $minimum_orderable;
    public $extra_costs;
    public $delivery_terms;
    public $yearly_bonus_info;
    public $catalogs;
    public $pricelist;
    public $valid_from;
    public $expected_pricelist_update;
    public $social_link;
    public $supplier_media_link;
    public $supplier_media_link_user;
    public $supplier_media_link_password;
    public $notes;

    public function rules()
    {
        return [
            'prefix' => $this->isEditMode && $this->brand ? 'required|unique:brands,prefix,' . $this->brand->id : 'required|unique:brands,prefix',
            'name' => 'required|min:3',
            'image' => $this->image instanceof UploadedFile ? 'nullable|image|max:1024' : 'nullable|string',

            'price_range' => 'nullable',
            'rating' => 'nullable',
            'partnership_level' => 'nullable',
            
            'lead_time' => 'nullable',
            'purchase_conditions' => 'nullable|string',
            'minimum_orderable' => 'nullable|numeric',
            'extra_costs' => 'nullable|string',
            'delivery_terms' => 'nullable',
            
            'purchase_price_list' => 'nullable|string',
            'yearly_bonus_info' => 'nullable|string',
            'catalogs' => 'nullable|string',
            'pricelist' => 'nullable|string',

            'valid_from' => 'nullable|date',
            'expected_pricelist_update' => 'nullable|date',

            'social_link' => 'nullable|string',
            'supplier_media_link' => 'nullable|string',
            'supplier_media_link_user' => 'nullable|string',
            'supplier_media_link_password' => 'nullable|string',

            'notes' => 'nullable|string',
        ];
    }

    public function setBrand(Brand $brand)
    {
        $this->isEditMode = true;
        $this->brand = $brand;
        $this->prefix = $brand->prefix;
        $this->name = $brand->name;
        $this->image = $brand->image;
        $this->price_range = $brand->price_range;
        $this->rating = $brand->rating;
        $this->partnership_level = $brand->partnership_level;
        $this->lead_time = $brand->lead_time;
        $this->purchase_price_list = $brand->purchase_price_list;
        $this->purchase_conditions = $brand->purchase_conditions;
        $this->minimum_orderable = $brand->minimum_orderable;
        $this->extra_costs = $brand->extra_costs;
        $this->delivery_terms = $brand->delivery_terms;
        $this->yearly_bonus_info = $brand->yearly_bonus_info;
        $this->catalogs = $brand->catalogs;
        $this->pricelist = $brand->pricelist;
        $this->valid_from = $brand->valid_from ? $brand->valid_from->format('Y-m-d') : null;
        $this->expected_pricelist_update = $brand->expected_pricelist_update ? $brand->expected_pricelist_update->format('Y-m-d') : ($brand->valid_from ? $brand->valid_from->addMonths(12)->format('Y-m-d') : null);
        $this->social_link = $brand->social_link;
        $this->supplier_media_link = $brand->supplier_media_link;
        $this->supplier_media_link_user = $brand->supplier_media_link_user;
        $this->supplier_media_link_password = $brand->supplier_media_link_password;
        $this->notes = $brand->notes;
    }

    public function store()
    {
        $this->validate();

        $brand = new Brand();
        $brand->prefix = $this->prefix;
        $brand->name = $this->name;
        $brand->price_range = $this->price_range;
        $brand->rating = $this->rating;
        $brand->partnership_level = $this->partnership_level;
        $brand->lead_time = $this->lead_time;
        $brand->purchase_price_list = $this->purchase_price_list;
        $brand->purchase_conditions = $this->purchase_conditions;
        $brand->minimum_orderable = $this->minimum_orderable ?: NULL;
        $brand->extra_costs = $this->extra_costs;
        $brand->delivery_terms = $this->delivery_terms;
        $brand->yearly_bonus_info = $this->yearly_bonus_info;
        $brand->catalogs = $this->catalogs;
        $brand->pricelist = $this->pricelist;
        $brand->valid_from = $this->valid_from ?: NULL;
        $brand->expected_pricelist_update = $this->expected_pricelist_update ?: NULL;
        $brand->social_link = $this->social_link;
        $brand->supplier_media_link = $this->supplier_media_link;
        $brand->supplier_media_link_user = $this->supplier_media_link_user;
        $brand->supplier_media_link_password = $this->supplier_media_link_password;
        $brand->notes = $this->notes;

        if ($this->image instanceof UploadedFile) {
            $brand->image = $this->image->store('brands', config('filesystems.public'));
        }

        $brand->save();

        $this->reset();
    }

    public function update()
    {
        $this->validate();

        $this->brand->prefix = $this->prefix;
        $this->brand->name = $this->name;
        $this->brand->price_range = $this->price_range;
        $this->brand->rating = $this->rating;
        $this->brand->partnership_level = $this->partnership_level;
        $this->brand->lead_time = $this->lead_time;
        $this->brand->purchase_price_list = $this->purchase_price_list;
        $this->brand->purchase_conditions = $this->purchase_conditions;
        $this->brand->minimum_orderable = $this->minimum_orderable ?: NULL;
        $this->brand->extra_costs = $this->extra_costs;
        $this->brand->delivery_terms = $this->delivery_terms;
        $this->brand->yearly_bonus_info = $this->yearly_bonus_info;
        $this->brand->catalogs = $this->catalogs;
        $this->brand->pricelist = $this->pricelist;
        $this->brand->valid_from = $this->valid_from ?: NULL;
        $this->brand->expected_pricelist_update = $this->expected_pricelist_update ?: NULL;
        $this->brand->social_link = $this->social_link;
        $this->brand->supplier_media_link = $this->supplier_media_link;
        $this->brand->supplier_media_link_user = $this->supplier_media_link_user;
        $this->brand->supplier_media_link_password = $this->supplier_media_link_password;
        $this->brand->notes = $this->notes;

        if ($this->image instanceof UploadedFile) {
            $this->brand->image = $this->image->store('brands', config('filesystems.public'));
        }
        elseif ($this->image == null) {
            $this->brand->image = null;
        }

        $this->brand->save();

        $this->reset();
    }
}
