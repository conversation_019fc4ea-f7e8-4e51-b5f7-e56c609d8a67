<?php

namespace App\Livewire\Forms;

use Livewire\Form;
use App\Models\DiscountGroup;

class DiscountGroupForm extends Form
{
    public $isEditMode = false;

    public ?DiscountGroup $discountGroup;

    public $code;
    public $discount;
    public $description;
    public $brand_id;

    public function rules()
    {
        return [
            'code' => $this->isEditMode && $this->discountGroup ? 'required|unique:discount_groups,code,' . $this->discountGroup->id : 'required|unique:discount_groups,code',
            'discount' => 'required|numeric|min:0|max:100',
            'description' => 'nullable',
            'brand_id' => 'nullable|exists:brands,id',
        ];
    }

    public function setDiscountGroup(DiscountGroup $discountGroup)
    {
        $this->isEditMode = true;
        $this->discountGroup = $discountGroup;

        $this->code = $discountGroup->code;
        $this->discount = $discountGroup->discount;
        $this->description = $discountGroup->description;
        $this->brand_id = $discountGroup->brand_id;
    }

    public function store()
    {
        $this->validate();

        $discountGroup = new DiscountGroup();
        $discountGroup->code = $this->code;
        $discountGroup->discount = $this->discount;
        $discountGroup->description = $this->description;
        $discountGroup->brand_id = $this->brand_id;
        $discountGroup->save();

        $this->reset();
    }

    public function update()
    {
        $this->validate();

        $this->discountGroup->code = $this->code;
        $this->discountGroup->discount = $this->discount;
        $this->discountGroup->description = $this->description;
        $this->discountGroup->brand_id = $this->brand_id;
        $this->discountGroup->save();
    }
}
