<?php

namespace App\Livewire\Forms;

use Livewire\Form;
use App\Models\CustomProduct;
use Livewire\WithFileUploads;
use Illuminate\Http\UploadedFile;

class CustomProductForm extends Form
{
    use WithFileUploads;

    public $isEditMode = false;

    public ?CustomProduct $customProduct;    
    public $user_id;

    public $sku;
    public $description;
    public $brand_name;
    public $supplier_color;
    public $dimensions;
    public $image;

    public function rules()
    {
        return [
            'user_id' => 'required|exists:users,id',

            'sku' => 'required|string|min:2|max:255',
            'description' => 'required|string|min:2|max:255',
            'brand_name' => 'nullable|string|min:2|max:255',
            'supplier_color' => 'nullable|string|min:2|max:255',
            'dimensions' => 'nullable|string|min:2|max:255',
            'image' => $this->image instanceof UploadedFile ? 'nullable|image|max:1024' : 'nullable|string',
        ];
    }

    public function setCustomProduct(CustomProduct $customProduct)
    {
        $this->isEditMode = true;
        $this->customProduct = $customProduct;

        $this->user_id = $customProduct->user_id;
        
        $this->sku = $customProduct->sku;
        $this->description = $customProduct->description;
        $this->brand_name = $customProduct->brand_name;
        $this->supplier_color = $customProduct->supplier_color;
        $this->dimensions = $customProduct->dimensions;
        $this->image = $customProduct->image;
    }

    public function store()
    {
        $this->validate();

        $customProduct = new CustomProduct();

        $customProduct->user_id = $this->user_id;
        
        $customProduct->sku = $this->sku;
        $customProduct->description = $this->description;
        $customProduct->brand_name = $this->brand_name;
        $customProduct->supplier_color = $this->supplier_color;
        $customProduct->dimensions = $this->dimensions;

        if ($this->image instanceof UploadedFile) {
            $customProduct->image = $this->image->store('custom-products', config('filesystems.public'));
        }

        $customProduct->save();

        $this->reset();

        return $customProduct;
    }

    public function update()
    {
        $this->validate();

        $this->customProduct->user_id = $this->user_id;
        
        $this->customProduct->sku = $this->sku;
        $this->customProduct->description = $this->description;
        $this->customProduct->brand_name = $this->brand_name;
        $this->customProduct->supplier_color = $this->supplier_color;
        $this->customProduct->dimensions = $this->dimensions;

        if ($this->image instanceof UploadedFile) {
            $this->customProduct->image = $this->image->store('custom-products', config('filesystems.public'));
        }
        elseif ($this->image == null) {
            $this->customProduct->image = null;
        }

        $this->customProduct->save();

        $this->reset();
    }
}
