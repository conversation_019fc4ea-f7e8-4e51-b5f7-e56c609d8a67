<?php

namespace App\Livewire\Forms;

use Livewire\Form;
use App\Models\Asset;
use Illuminate\Http\UploadedFile;

class AssetForm extends Form
{
    public $isEditMode = false;

    public ?Asset $asset;

    public $assetable_id;
    public $assetable_type;
    public $relatable_id;
    public $relatable_type;

    public $type;
    public $name;
    public $link;
    public $file;
    public $status;
    
    public function rules()
    {
        return [
            'assetable_id' => 'required|integer',
            'assetable_type' => 'required|string',
            'relatable_id' => 'nullable|integer',
            'relatable_type' => 'nullable|string',

            'type' => 'required',
            'name' => 'required|min:3',
            'link' => 'nullable|url',
            'file' => $this->file instanceof UploadedFile ? 'nullable|max:10240' : 'nullable|string',
            'status' => 'nullable',
        ];
    }

    public function setAsset(Asset $asset)
    {
        $this->isEditMode = true;
        $this->asset = $asset;

        $this->assetable_id = $asset->assetable_id;
        $this->assetable_type = $asset->assetable_type;
        $this->relatable_id = $asset->relatable_id;
        $this->relatable_type = $asset->relatable_type;

        $this->type = $asset->type;
        $this->name = $asset->name;
        $this->link = $asset->link;
        $this->file = $asset->file;
        $this->status = $asset->status;
    }

    public function store()
    {
        $this->validate();

        $asset = new Asset();

        $asset->assetable_id = $this->assetable_id;
        $asset->assetable_type = $this->assetable_type;
        $asset->relatable_id = $this->relatable_id ?: NULL;
        $asset->relatable_type = $this->relatable_id ? $this->relatable_type : NULL;

        $asset->type = $this->type;
        $asset->name = $this->name;
        $asset->link = $this->link;
        $asset->status = $this->status;

        if ($this->file instanceof UploadedFile) {
            $asset->file = $this->file->store('assets', config('filesystems.private'));
        }

        $asset->save();

        $this->reset();
    }

    public function update()
    {
        $this->validate();

        $this->asset->assetable_id = $this->assetable_id;
        $this->asset->assetable_type = $this->assetable_type;
        $this->asset->relatable_id = $this->relatable_id ?: NULL;
        $this->asset->relatable_type = $this->relatable_id ? $this->relatable_type : NULL;

        $this->asset->type = $this->type;
        $this->asset->name = $this->name;
        $this->asset->link = $this->link;
        $this->asset->file = $this->file;
        $this->asset->status = $this->status;

        if ($this->file instanceof UploadedFile) {
            $this->asset->file = $this->file->store('assets', config('filesystems.private'));
        }
        elseif ($this->file == null) {
            $this->asset->file = null;
        }

        $this->asset->save();

        $this->reset();
    }
}
