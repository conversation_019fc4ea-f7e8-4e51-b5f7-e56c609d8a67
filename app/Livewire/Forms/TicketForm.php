<?php

namespace App\Livewire\Forms;

use App\Models\Ticket;
use Livewire\Form;

class TicketForm extends Form
{

    public ?Ticket $ticket;

    public $title;
    public $description;
    public $ticketable_type;
    public $ticketable_id;
    public $user_id;


    public function rules()
    {
        return [
            'ticketable_id' => 'nullable|integer',
            'ticketable_type' => 'required|string',
            'user_id' => 'nullable|integer',
            'title' => 'required',
            'description' => 'nullable|min:3',
        ];
    }

    public function store()
    {
        $this->validate();

        $ticket = new Ticket();
        $ticket->title = $this->title;
        $ticket->description = $this->description;
        $ticket->ticketable_type = $this->ticketable_type;
        $ticket->ticketable_id = $this->ticketable_id;
        $ticket->user_id = $this->user_id;

        $ticket->save();

        $this->reset();
    }

}
