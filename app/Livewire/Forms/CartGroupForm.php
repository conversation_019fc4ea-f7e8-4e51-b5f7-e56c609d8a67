<?php

namespace App\Livewire\Forms;

use App\Models\Order\CartGroup;
use Livewire\Attributes\Validate;
use Livewire\Form;

class CartGroupForm extends Form
{
    public $isEditMode = false;

    public ?CartGroup $cartGroup;

    public $order_id;
    public $sort;
    public $name;

    public function rules()
    {
        return [
            'order_id' => 'required|integer',
            'sort' => 'required|integer',
            'name' => 'required|string|min:2|max:255',
        ];
    }

    public function setCartGroup(CartGroup $cartGroup)
    {
        $this->isEditMode = true;
        $this->cartGroup = $cartGroup;

        $this->order_id = $cartGroup->order_id;
        $this->sort = $cartGroup->sort;
        $this->name = $cartGroup->name;
    }

    public function store()
    {
        $this->validate();

        $cartGroup = new CartGroup();

        $cartGroup->order_id = $this->order_id;
        $cartGroup->sort = $this->sort;
        $cartGroup->name = $this->name;

        $cartGroup->save();

        $this->reset();
    }

    public function update()
    {
        $this->validate();

        $this->cartGroup->order_id = $this->order_id;
        $this->cartGroup->sort = $this->sort;
        $this->cartGroup->name = $this->name;
        $this->cartGroup->save();

        $this->reset();
    }
}
