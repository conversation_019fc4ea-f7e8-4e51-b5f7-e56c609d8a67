<?php

namespace App\Livewire\Forms;

use App\Models\Supplier;
use Livewire\Form;

class SupplierForm extends Form
{
    public $isEditMode = false;

    public ?Supplier $supplier;

    public $code;
    public $name;
    public $payment_conditions;
    public $price_range;
    public $type;
    public $notes;

    public $materials = [];
    public $productTypes = [];
    public $destinationRooms = [];

    public function rules()
    {
        return [
            'code' => $this->isEditMode && $this->supplier ? 'nullable|unique:suppliers,code,' . $this->supplier->id : 'nullable|unique:suppliers,code',
            'name' => 'required|min:3',
            'payment_conditions' => 'nullable|string',
            'price_range' => 'nullable',
            'type' => 'required',
            'notes' => 'nullable|min:3',
            'materials' => 'nullable|array',
            'productTypes' => 'nullable|array',
            'destinationRooms' => 'nullable|array',
        ];
    }

    public function setSupplier(Supplier $supplier)
    {
        $this->isEditMode = true;
        $this->supplier = $supplier;

        $this->code = $supplier->code;
        $this->name = $supplier->name;
        $this->payment_conditions = $supplier->payment_conditions;
        $this->price_range = $supplier->price_range;
        $this->type = $supplier->type;
        $this->notes = $supplier->notes;

        $this->materials = $supplier->tags->where('type', 'material')->pluck('name')->toArray();
        $this->productTypes = $supplier->tags->where('type', 'product_type')->pluck('name')->toArray();
        $this->destinationRooms = $supplier->tags->where('type', 'destination_room')->pluck('name')->toArray();
    }

    public function store()
    {
        $this->validate();

        $supplier = new Supplier();
        $supplier->code = $this->code;
        $supplier->name = $this->name;
        $supplier->payment_conditions = $this->payment_conditions;
        $supplier->price_range = $this->price_range;
        $supplier->type = $this->type;
        $supplier->notes = $this->notes;
        $supplier->save();

        $supplier->syncTagsWithType($this->materials, 'material');
        $supplier->syncTagsWithType($this->productTypes, 'product_type');
        $supplier->syncTagsWithType($this->destinationRooms, 'destination_room');

        $this->reset();
    }

    public function update()
    {
        $this->validate();

        $this->supplier->code = $this->code;
        $this->supplier->name = $this->name;
        $this->supplier->payment_conditions = $this->payment_conditions;
        $this->supplier->price_range = $this->price_range;
        $this->supplier->type = $this->type;
        $this->supplier->notes = $this->notes;
        $this->supplier->save();


        $this->supplier->syncTagsWithType($this->materials, 'material');
        $this->supplier->syncTagsWithType($this->productTypes, 'product_type');
        $this->supplier->syncTagsWithType($this->destinationRooms, 'destination_room');

        $this->reset();
    }
}
