<?php

namespace App\Livewire\Forms;

use App\Models\Contact;
use Livewire\Form;

class ContactForm extends Form
{
    public $isEditMode = false;

    public ?Contact $contact;

    public $name = '';

    public $departments = [];

    public $position = '';

    public $email = '';

    public $phone = '';

    public $country = null;

    public $languages = [];

    public $notes = '';

    public function rules()
    {
        // Normalize email before validation
        if ($this->email) {
            $this->email = strtolower(trim($this->email));
        }

        return [
            'name' => 'required|min:3',
            'departments' => 'nullable|array',
            'position' => 'nullable|min:3',
            'email' => $this->isEditMode && $this->contact ? 'required|email|unique:contacts,email,'.$this->contact->id : 'required|email|unique:contacts,email',
            'phone' => 'min:3',
            'country' => 'nullable|min:3',
            'languages' => 'nullable|array',
            'notes' => 'nullable|min:3',
        ];
    }

    public function messages()
    {
        return [
            'email.unique' => $this->emailIsOwn() ? 'This email is already taken.' : 'This email is already taken by another user. Contact your administrator for request to assign it to you.',
        ];
    }

    public function emailIsOwn()
    {
        if ($this->email) {
            $normalizedEmail = strtolower(trim($this->email));
            $existingContact = Contact::where('email', $normalizedEmail)->first();
            if ($existingContact) {
                if ($existingContact->users()->whereKey(auth()->id())->exists()) {
                    return true;
                }
            }
        }

        return false;
    }

    public function setContact(Contact $contact)
    {
        $this->isEditMode = true;
        $this->contact = $contact;

        $this->name = $contact->name;
        $this->departments = $contact->departments ?? [];
        $this->position = $contact->position;
        $this->email = $contact->email;
        $this->phone = $contact->phone;
        $this->country = $contact->country->value ?? null;
        $this->languages = $contact->languages ?? [];
        $this->notes = $contact->notes;
    }

    public function store()
    {
        $this->validate();

        $contact = new Contact;
        $contact->name = $this->name;
        $contact->departments = $this->departments;
        $contact->position = $this->position;
        $contact->email = $this->email;
        $contact->phone = $this->phone;
        $contact->country = $this->country;
        $contact->languages = $this->languages;
        $contact->notes = $this->notes;
        $contact->save();

        $this->reset();
    }

    public function update()
    {
        $this->validate();

        $this->contact->name = $this->name;
        $this->contact->departments = $this->departments;
        $this->contact->position = $this->position;
        $this->contact->email = $this->email;
        $this->contact->phone = $this->phone;
        $this->contact->country = $this->country;
        $this->contact->languages = $this->languages;
        $this->contact->notes = $this->notes;
        $this->contact->save();

        $this->reset();
    }
}
