<?php

namespace App\Livewire\Forms;

use Livewire\Form;
use App\Models\Order\OrderRow;

class OrderRowForm extends Form
{
    public $isEditMode = false;

    public ?OrderRow $orderRow;
    
    public $position_id;

    public $quantity;
    public $shipped_quantity;
    public $stocked_quantity;
    public $ordered_quantity;

    public $price_override;
    public $discount_override;
    public $variation;

    public $expected_delivery_date;
    public $required_delivery_date;

    public function rules()
    {
        return [
            'position_id' => 'nullable|string|min:2|max:255',

            'quantity' => [
                'required', 'numeric', 'min:1',
                function ($attribute, $value, $fail) {
                    $multiple = $this->orderRow->product->purchase_units ?? 1;
                    if ($multiple > 0 && $value % $multiple !== 0) {
                        $fail($attribute.' must be a multiple of '.$multiple.'.');
                    }
                },
            ],
            'shipped_quantity' => [
                'nullable', 'numeric', 'min:0',
            ],
            'stocked_quantity' => [
                'nullable', 'numeric', 'min:0',
            ],
            'ordered_quantity' => [
                'nullable', 'numeric', 'min:0',
            ],

            'price_override' => [
                'nullable', 'numeric', 'min:0',
            ],
            'discount_override' => [
                'nullable', 'numeric', 'min:0', 'max:100',
            ],
            'variation' => [
                'nullable', 'numeric', 'min:-100', 'max:100',
            ],

            'expected_delivery_date' => [
                'nullable', 'date',
            ],
            'required_delivery_date' => [
                'nullable', 'date',
            ],
        ];
    }

    public function setOrderRow(OrderRow $orderRow)
    {
        $this->isEditMode = true;
        $this->orderRow = $orderRow;

        $this->position_id = $orderRow->position_id;

        $this->quantity = $orderRow->quantity;
        $this->shipped_quantity = $orderRow->shipped_quantity;
        $this->stocked_quantity = $orderRow->stocked_quantity;
        $this->ordered_quantity = $orderRow->ordered_quantity;
        
        $this->price_override = $orderRow->selling_price_override;
        $this->discount_override = $orderRow->discount_override;
        $this->variation = $orderRow->variation;
        
        $this->expected_delivery_date = $orderRow->expected_delivery_date ? $orderRow->expected_delivery_date->format('Y-m-d') : null;
        $this->required_delivery_date = $orderRow->required_delivery_date ? $orderRow->required_delivery_date->format('Y-m-d') : null;
    }

    public function update()
    {
        $this->validate();

        $this->orderRow->position_id = $this->position_id;

        $this->orderRow->quantity = $this->quantity;
        $this->orderRow->shipped_quantity = $this->shipped_quantity ?: 0;
        $this->orderRow->stocked_quantity = $this->stocked_quantity ?: 0;
        $this->orderRow->ordered_quantity = $this->ordered_quantity ?: 0;

        $this->orderRow->selling_price_override = $this->price_override ?: null;
        $this->orderRow->discount_override = $this->discount_override === '' ? null : $this->discount_override;
        $this->orderRow->variation = $this->variation ?: null;

        $this->orderRow->expected_delivery_date = $this->expected_delivery_date ?: null;
        $this->orderRow->required_delivery_date = $this->required_delivery_date ?: null;

        $this->orderRow->save();
        
        $this->reset();
    }
}