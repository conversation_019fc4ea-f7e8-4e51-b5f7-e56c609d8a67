<?php

namespace App\Livewire\Forms;

use App\Models\Collaborator;
use Livewire\Form;

class CollaboratorForm extends Form
{
    public $isEditMode = false;

    public ?Collaborator $collaborator;

    public $collaboratable_id;
    public $collaboratable_type;

    public $user_id;
    public $contact_id;

    public $name;
    public $position;
    
    public function rules()
    {
        return [
            'collaboratable_id' => 'required|integer',
            'collaboratable_type' => 'required|string',

            'user_id' => 'nullable|integer',
            'contact_id' => 'nullable|integer',
            
            'name' => 'required|min:3',
            'position' => 'nullable|min:3',
        ];
    }

    public function setCollaborator(Collaborator $collaborator)
    {
        $this->isEditMode = true;
        $this->collaborator = $collaborator;

        $this->collaboratable_id = $collaborator->collaboratable_id;
        $this->collaboratable_type = $collaborator->collaboratable_type;

        $this->user_id = $collaborator->user_id;
        $this->contact_id = $collaborator->contact_id;

        $this->name = $collaborator->name;
        $this->position = $collaborator->position;
    }

    public function store()
    {
        $this->validate();

        $collaborator = new Collaborator();

        $collaborator->collaboratable_id = $this->collaboratable_id;
        $collaborator->collaboratable_type = $this->collaboratable_type;

        $collaborator->user_id = $this->user_id ?: NULL;
        $collaborator->contact_id = $this->contact_id ?: NULL;

        $collaborator->name = $this->name;
        $collaborator->position = $this->position;

        $collaborator->save();

        $this->reset();
    }

    public function update()
    {
        $this->validate();

        $this->collaborator->collaboratable_id = $this->collaboratable_id;
        $this->collaborator->collaboratable_type = $this->collaboratable_type;

        $this->collaborator->user_id = $this->user_id ?: NULL;
        $this->collaborator->contact_id = $this->contact_id ?: NULL;

        $this->collaborator->name = $this->name;
        $this->collaborator->position = $this->position;
        $this->collaborator->save();

        $this->reset();
    }
}
