<?php

namespace App\Livewire\Forms;

use Livewire\Form;
use App\Models\Address;

class AddressForm extends Form
{
    public $isEditMode = false;

    public ?Address $address;

    public $addressable_id;
    public $addressable_type;

    public $parent_id;

    public $name;
    public $type;
    public $code_invoicing;
    public $code_shipping;
    public $company;
    public $vat_number;
    public $fiscal_code;
    public $sdi_code;
    public $street;
    public $city;
    public $state;
    public $zip;
    public $country = NULL;

    public function rules()
    {
        return [
            'addressable_id' => 'required|integer',
            'addressable_type' => 'required|string',

            'parent_id' => 'nullable|integer',

            'name' => 'required|min:3',
            'type' => 'required',
            'code_invoicing' => 'nullable|min:3',
            'code_shipping' => 'nullable|min:3',
            'company' => 'required|min:3',
            'vat_number' => 'nullable|min:3',
            'fiscal_code' => 'nullable|min:3',
            'sdi_code' => 'nullable|min:3',
            'street' => 'nullable|min:3',
            'city' => 'nullable|min:3',
            'state' => 'nullable|min:3',
            'zip' => 'nullable|min:3',
            'country' => 'nullable|min:3',
        ];
    }

    public function setAddress(Address $address)
    {
        $this->isEditMode = true;
        $this->address = $address;

        $this->addressable_id = $address->addressable_id;
        $this->addressable_type = $address->addressable_type;

        $this->parent_id = $address->parent_id ?? NULL;

        $this->name = $address->name;
        $this->type = $address->type->value ?? NULL;
        $this->code_invoicing = $address->code_invoicing;
        $this->code_shipping = $address->code_shipping;
        $this->company = $address->company;
        $this->vat_number = $address->vat_number;
        $this->fiscal_code = $address->fiscal_code;
        $this->sdi_code = $address->sdi_code;
        $this->street = $address->street;
        $this->city = $address->city;
        $this->state = $address->state;
        $this->zip = $address->zip;
        $this->country = $address->country->value ?? NULL;
    }

    public function store()
    {
        $this->validate();

        $address = new Address();

        $address->addressable_id = $this->addressable_id;
        $address->addressable_type = $this->addressable_type;

        $address->parent_id = $this->parent_id ?? NULL;

        $address->name = $this->name;
        $address->type = $this->type;
        $address->code_invoicing = $this->code_invoicing;
        $address->code_shipping = $this->code_shipping;
        $address->company = $this->company;
        $address->vat_number = $this->vat_number;
        $address->fiscal_code = $this->fiscal_code;
        $address->sdi_code = $this->sdi_code;
        $address->street = $this->street;
        $address->city = $this->city;
        $address->state = $this->state;
        $address->zip = $this->zip;
        $address->country = $this->country;
        $address->save();

        $this->reset();
    }

    public function update()
    {
        $this->validate();

        $this->address->addressable_id = $this->addressable_id;
        $this->address->addressable_type = $this->addressable_type;

        $this->address->parent_id = $this->parent_id ?? NULL;

        $this->address->name = $this->name;
        $this->address->type = $this->type;
        $this->address->code_invoicing = $this->code_invoicing;
        $this->address->code_shipping = $this->code_shipping;
        $this->address->company = $this->company;
        $this->address->vat_number = $this->vat_number;
        $this->address->fiscal_code = $this->fiscal_code;
        $this->address->sdi_code = $this->sdi_code;
        $this->address->street = $this->street;
        $this->address->city = $this->city;
        $this->address->state = $this->state;
        $this->address->zip = $this->zip;
        $this->address->country = $this->country;
        $this->address->save();

        $this->reset();
    }
}
