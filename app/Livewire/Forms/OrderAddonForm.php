<?php

namespace App\Livewire\Forms;

use Livewire\Form;
use App\Models\Order\OrderAddon;
use Livewire\Attributes\Validate;

class OrderAddonForm extends Form
{
    public $isEditMode = false;

    public ?OrderAddon $orderAddon;

    public $order_id;
    public $code;
    public $description;
    public $percent;

    public function rules()
    {
        return [
            'order_id' => 'required|integer',
            'code' => 'required|string|min:2|max:255',
            'description' => 'required|string|min:2|max:255',
            'percent' => 'required|numeric|min:0|max:100',
        ];
    }

    public function setOrderAddon(OrderAddon $orderAddon)
    {
        $this->isEditMode = true;
        $this->orderAddon = $orderAddon;

        $this->order_id = $orderAddon->order_id;
        $this->code = $orderAddon->code;
        $this->description = $orderAddon->description;
        $this->percent = $orderAddon->percent;
    }

    public function store()
    {
        $this->validate();

        $orderAddon = new OrderAddon();

        $orderAddon->order_id = $this->order_id;
        $orderAddon->code = $this->code;
        $orderAddon->description = $this->description;
        $orderAddon->percent = $this->percent;

        $orderAddon->save();

        $this->reset();
    }

    public function update()
    {
        $this->validate();

        $this->orderAddon->order_id = $this->order_id;
        $this->orderAddon->code = $this->code;
        $this->orderAddon->description = $this->description;
        $this->orderAddon->percent = $this->percent;
        $this->orderAddon->save();

        $this->reset();
    }
}
