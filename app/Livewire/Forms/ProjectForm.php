<?php

namespace App\Livewire\Forms;

use App\Models\Project\Project;
use Livewire\Form;

class ProjectForm extends Form
{
    public $isEditMode = false;

    public ?Project $project;

    public $date;
    public $name;
    
    public $project_code_progressive;
    public $project_code_date;
    public $project_code_region;

    public $internal_referent_id;
    public $area_manager_id;
    public $client_id;
    public $partner_id;
    public $invoicing_address_id;
    public $shipping_address_id;
    public $payment_term_id;

    public $description;
    public $type;
    public $type_name;
    public $type_link;
    
    public function rules()
    {
        return [
            'date' => 'required|date',
            'name' => 'required|string',
            'project_code_date' => 'nullable|date',
            'project_code_region' => 'nullable|string',

            'internal_referent_id' => 'nullable|exists:users,id',
            'area_manager_id' => 'nullable|exists:users,id',
            'client_id' => 'nullable|exists:clients,id',
            'partner_id' => 'nullable|exists:partners,id',
            'invoicing_address_id' => 'nullable|exists:addresses,id',
            'shipping_address_id' => 'nullable|exists:addresses,id',
            'payment_term_id' => 'nullable|exists:payment_terms,id',

            'description' => 'nullable|string',
            'type' => 'nullable',
            'type_name' => 'nullable|string',
            'type_link' => 'nullable|string',
        ];
    }

    public function setProject(Project $project)
    {
        $this->isEditMode = true;
        $this->project = $project;

        $this->date = $project->date ? $project->date->format('Y-m-d') : null;
        $this->name = $project->name;

        $this->project_code_progressive = $project->project_code_progressive;
        $this->project_code_date = $project->project_code_date ? $project->project_code_date->format('Y-m-d') : null;
        $this->project_code_region = $project->project_code_region;

        $this->internal_referent_id = $project->internal_referent_id ?: NULL;
        $this->area_manager_id = $project->area_manager_id ?: NULL;
        $this->client_id = $project->client_id ?: NULL;
        $this->partner_id = $project->partner_id ?: NULL;
        $this->invoicing_address_id = $project->invoicing_address_id ?: NULL;
        $this->shipping_address_id = $project->shipping_address_id ?: NULL;
        $this->payment_term_id = $project->payment_term_id ?: NULL;

        $this->description = $project->description;
        $this->type = $project->type;
        $this->type_name = $project->type_name;
        $this->type_link = $project->type_link;
    }

    public function store()
    {
        $this->validate();

        $project = new Project();
        $project->date = $this->date;
        $project->name = $this->name;

        $project->project_code_progressive = $this->project_code_progressive;
        $project->project_code_date = $this->project_code_date;
        $project->project_code_region = $this->project_code_region;

        $project->internal_referent_id = $this->internal_referent_id ?: NULL;
        $project->area_manager_id = $this->area_manager_id ?: NULL;
        $project->client_id = $this->client_id ?: NULL;
        $project->partner_id = $this->partner_id ?: NULL;
        $project->invoicing_address_id = $this->invoicing_address_id ?: NULL;
        $project->shipping_address_id = $this->shipping_address_id ?: NULL;
        $project->payment_term_id = $this->payment_term_id ?: NULL;

        $project->description = $this->description;
        $project->type = $this->type;
        $project->type_name = $this->type_name;
        $project->type_link = $this->type_link;

        $project->save();

        $this->reset();
    }

    public function update()
    {
        $this->validate();

        $this->project->date = $this->date;
        $this->project->name = $this->name;

        $this->project->project_code_progressive = $this->project_code_progressive;
        $this->project->project_code_date = $this->project_code_date;
        $this->project->project_code_region = $this->project_code_region;

        $this->project->internal_referent_id = $this->internal_referent_id ?: NULL;
        $this->project->area_manager_id = $this->area_manager_id ?: NULL;
        $this->project->client_id = $this->client_id ?: NULL;
        $this->project->partner_id = $this->partner_id ?: NULL;
        $this->project->invoicing_address_id = $this->invoicing_address_id ?: NULL;
        $this->project->shipping_address_id = $this->shipping_address_id ?: NULL;
        $this->project->payment_term_id = $this->payment_term_id ?: NULL;

        $this->project->description = $this->description;
        $this->project->type = $this->type;
        $this->project->type_name = $this->type_name;
        $this->project->type_link = $this->type_link;

        $this->project->save();

        $this->reset();
    }
}
