<?php

namespace App\Livewire\Forms;

use Livewire\Form;
use App\Models\Collection;
use Livewire\WithFileUploads;
use Illuminate\Http\UploadedFile;

class CollectionForm extends Form
{
    use WithFileUploads;

    public $isEditMode = false;

    public ?Collection $collection;

    public $code;
    public $name;
    public $image;

    public function rules()
    {
        return [
            'code' => $this->isEditMode && $this->collection ? 'required|unique:collections,code,' . $this->collection->id : 'required|unique:collections,code',
            
            'name' => 'required|min:3',

            'image' => $this->image instanceof UploadedFile ? 'nullable|image|max:1024' : 'nullable|string',
        ];
    }

    public function setCollection(Collection $collection)
    {
        $this->isEditMode = true;
        $this->collection = $collection;

        $this->code = $collection->code;
        $this->name = $collection->name;
        $this->image = $collection->image;
    }

    public function store()
    {
        $this->validate();

        $collection = new Collection();

        $collection->code = $this->code;
        $collection->name = $this->name;

        if ($this->image instanceof UploadedFile) {
            $collection->image = $this->image->store('collections', config('filesystems.public'));
        }

        $collection->save();

        $this->reset();
    }

    public function update()
    {
        $this->validate();

        $this->collection->code = $this->code;
        $this->collection->name = $this->name;

        if ($this->image instanceof UploadedFile) {
            $this->collection->image = $this->image->store('collections', config('filesystems.public'));
        }
        elseif ($this->image == null) {
            $this->collection->image = null;
        }

        $this->collection->save();

        $this->reset();
    }
}
