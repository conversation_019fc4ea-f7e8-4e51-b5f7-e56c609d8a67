<?php

namespace App\Livewire\Auth;

use Flux\Flux;
use Livewire\Component;
use Livewire\Attributes\Validate;
use Illuminate\Support\Facades\Auth;

class Login extends Component
{
    #[Validate('required')]
    public $email = '';

    #[Validate('required')]
    public $password = '';

    public function render()
    {
        return view('livewire.auth.login')->layout('components.layouts.guest');
    }

    public function login()
    {
        $this->validate();

        if (Auth::attempt(['email' => $this->email, 'password' => $this->password])) {
            return redirect()->intended('/');
        } else {
            
            $this->reset('password');

            Flux::toast(
                variant: 'danger',
                text: 'Error! Invalid email or password.'
            );
        }
    }
}
