<?php

namespace App\Livewire\Pages\Users;

use Flux\Flux;
use App\Models\User;
use Livewire\Component;
use App\Enums\UserTypes;
use App\Livewire\Forms\UserForm;
use Spatie\Permission\Models\Role;

class Edit extends Component
{
    public User $user;
    public UserForm $form;

    public function mount(): void
    {
        $this->form->setUser($this->user);
    }

    public function render()
    {
        return view('livewire.pages.users.edit', [
            'userTypes' => UserTypes::cases(),
            'userRoles' => Role::all(),
        ]);
    }

    public function save(): void
    {
        if (auth()->user()->cannot('update', $this->user)) {
            abort(403);
        }

        $this->form->update();

        Flux::toast(
            variant: 'success',
            text: 'The USER has been updated.'
        );

        $this->redirectRoute('users.index', navigate: true);
    }
}
