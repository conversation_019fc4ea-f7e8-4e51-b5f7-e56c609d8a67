<?php

namespace App\Livewire\Pages\Users;

use Flux\Flux;
use App\Models\User;
use App\Models\Client;
use App\Models\Partner;
use Livewire\Component;
use App\Enums\UserTypes;
use Illuminate\View\View;
use App\Models\Order\Order;
use App\Models\Project\Project;
use App\Livewire\Forms\UserForm;
use Spatie\Permission\Models\Role;

class Show extends Component
{
    public User $user;
    public UserForm $form;

    public function mount(): void
    {
        $this->form->setUser($this->user);
    }

    public function render(): View
    {
        return view('livewire.pages.users.show', [
            'userTypes' => UserTypes::cases(),
            'userRoles' => Role::all(),
        ]);
    }

    public function edit($id): void
    {
        $this->redirectRoute('users.edit', ['user' => $id], navigate: true);
    }

    public function delete(): void
    {
        $user = $this->user;

        // Check if the user has any Order, Project, Client or Partner associated as internal referent or area manager
        if ($user->hasResources()) {
            Flux::toast(
                variant: 'danger',
                text: 'You cannot delete this USER because it is associated to one or more orders, projects, clients or partners.'
            );
        }
        else {
            $user->delete();

            Flux::toast(
                variant: 'success',
                text: 'The USER has been deleted.'
            );

            $this->redirectRoute('users.index', navigate: true);
        }
    }
}
