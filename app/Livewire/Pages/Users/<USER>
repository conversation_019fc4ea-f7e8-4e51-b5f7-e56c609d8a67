<?php

namespace App\Livewire\Pages\Users;

use Flux\Flux;
use App\Models\User;
use Livewire\Component;
use Illuminate\View\View;
use Livewire\WithPagination;
use Livewire\Attributes\Computed;

class Index extends Component
{
    use WithPagination;

    public $search = '';
    public $itemsPerPage = 10;

    public $sortBy = 'id';
    public $sortDirection = 'desc';

    public function render(): View
    {
        return view('livewire.pages.users.index');
    }

    #[Computed]
    public function users()
    {
        return User::query()
            ->where(function ($query) {
                $query->where('first_name', 'ilike', '%' . $this->search . '%')
                      ->orWhere('last_name', 'ilike', '%' . $this->search . '%')
                      ->orWhere('phone', 'ilike', '%' . $this->search . '%')
                      ->orWhere('email', 'ilike', '%' . $this->search . '%');
            })
            ->tap(fn ($query) => $this->sortBy ? $query->orderBy($this->sortBy, $this->sortDirection) : $query)
            ->paginate($this->itemsPerPage);
    }

    public function sort($column) {
        if ($this->sortBy === $column) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $column;
            $this->sortDirection = 'asc';
        }
    }

    public function updatedSearch()
    {
        $this->resetPage();
        $this->sortBy = 'id';
        $this->sortDirection = 'desc';
    }

    public function view($id): void
    {
        $this->redirectRoute('users.show', ['user' => $id], navigate: true);
    }

    public function edit($id): void
    {
        $this->redirectRoute('users.edit', ['user' => $id], navigate: true);
    }

    public function delete($id): void
    {
        $user = User::findOrFail($id);

        // Check if the user has any Order, Project, Client or Partner associated as internal referent or area manager
        if ($user->hasResources()) {
            Flux::toast(
                variant: 'danger',
                text: 'You cannot delete this USER because it is associated to one or more orders, projects, clients or partners.'
            );
        }
        else {
            $user->delete();

            Flux::toast(
                variant: 'success',
                text: 'The USER has been deleted.'
            );
        }
    }
}
