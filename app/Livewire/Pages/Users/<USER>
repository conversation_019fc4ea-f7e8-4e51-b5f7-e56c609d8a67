<?php

namespace App\Livewire\Pages\Users;

use Flux\Flux;
use Livewire\Component;
use App\Enums\UserTypes;
use App\Livewire\Forms\UserForm;
use Spatie\Permission\Models\Role;

class Create extends Component
{
    public UserForm $form;

    public function render()
    {
        return view('livewire.pages.users.create', [
            'userTypes' => UserTypes::cases(),
            'userRoles' => Role::all(),
        ]);
    }

    public function save(): void
    {
        $this->form->store();

        Flux::toast(
            variant: 'success',
            text: 'The USER has been created.'
        );

        $this->redirectRoute('users.index', navigate: true);
    }
}
