<?php

namespace App\Livewire\Pages\Partners;

use App\Enums\ClientPartnerPriorities;
use App\Enums\CommercialCategories;
use App\Enums\PartnerTypes;
use App\Models\Partner;
use Flux\Flux;
use Livewire\Attributes\Computed;
use Livewire\Component;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;

    public $search = '';

    public $itemsPerPage = 10;

    public $sortBy = 'id';

    public $sortDirection = 'desc';

    // Filtri
    public $selectedType = null;

    public $selectedCommercialCategory = null;

    public $selectedPriority = null;

    public function render()
    {
        return view('livewire.pages.partners.index', [
            'types' => PartnerTypes::cases(),
            'commercialCategories' => CommercialCategories::cases(),
            'priorities' => ClientPartnerPriorities::cases(),
        ]);
    }

    #[Computed]
    public function partners()
    {
        return Partner::query()
            ->when($this->search, function ($query) {
                $query->where(function ($subQuery) {
                    $subQuery->where('company', 'ilike', '%'.$this->search.'%')
                        ->orWhere('type', 'ilike', '%'.$this->search.'%')
                        ->orWhere('commercial_category', 'ilike', '%'.$this->search.'%')
                        ->orWhere('priority', 'ilike', '%'.$this->search.'%')
                        ->orWhereHas('internalReferent', function ($query) {
                            $query->where('first_name', 'ilike', '%'.$this->search.'%')
                                ->orWhere('last_name', 'ilike', '%'.$this->search.'%');
                        })
                        ->orWhereHas('addresses', function ($query) {
                            $query->where('name', 'ilike', '%'.$this->search.'%')
                                ->orWhere('company', 'ilike', '%'.$this->search.'%')
                                ->orWhere('vat_number', 'ilike', '%'.$this->search.'%')
                                ->orWhere('fiscal_code', 'ilike', '%'.$this->search.'%')
                                ->orWhere('sdi_code', 'ilike', '%'.$this->search.'%')
                                ->orWhere('street', 'ilike', '%'.$this->search.'%')
                                ->orWhere('city', 'ilike', '%'.$this->search.'%')
                                ->orWhere('state', 'ilike', '%'.$this->search.'%')
                                ->orWhere('zip', 'ilike', '%'.$this->search.'%')
                                ->orWhere('code_invoicing', 'ilike', '%'.$this->search.'%')
                                ->orWhere('code_shipping', 'ilike', '%'.$this->search.'%');
                        });
                });
            })
            ->when($this->selectedType, function ($query) {
                $query->where('type', $this->selectedType);
            })
            ->when($this->selectedCommercialCategory, function ($query) {
                $query->where('commercial_category', $this->selectedCommercialCategory);
            })
            ->when($this->selectedPriority, function ($query) {
                $query->where('priority', $this->selectedPriority);
            })
            ->tap(fn ($query) => $this->sortBy ? $query->orderBy($this->sortBy, $this->sortDirection) : $query)
            ->paginate($this->itemsPerPage);
    }

    public function sort($column)
    {
        if ($this->sortBy === $column) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $column;
            $this->sortDirection = 'asc';
        }
    }

    public function updatedSearch()
    {
        $this->resetPage();
        $this->sortBy = 'id';
        $this->sortDirection = 'desc';
    }

    public function updatedSelectedType()
    {
        $this->resetPage();
    }

    public function updatedSelectedCommercialCategory()
    {
        $this->resetPage();
    }

    public function updatedSelectedPriority()
    {
        $this->resetPage();
    }

    public function view($id): void
    {
        $this->redirectRoute('partners.show', ['partner' => $id], navigate: true);
    }

    public function edit($id): void
    {
        $this->redirectRoute('partners.edit', ['partner' => $id], navigate: true);
    }

    public function delete($id): void
    {
        $partner = Partner::findOrFail($id);

        // Check if the partner is associated to any clients
        if ($partner->clients()->count() > 0) {
            Flux::toast(
                variant: 'danger',
                text: 'You cannot delete this PARTNER because it is associated to one or more clients.'
            );
        } else {
            $partner->delete();

            Flux::toast(
                variant: 'success',
                text: 'The PARTNER has been deleted.'
            );
        }
    }

    public function areFiltersActive(): bool
    {
        return ! empty($this->search) ||
               ! empty($this->selectedType) ||
               ! empty($this->selectedCommercialCategory) ||
               ! empty($this->selectedPriority);
    }

    public function clearSearchAndFilters(): void
    {
        $this->search = '';
        $this->selectedType = null;
        $this->selectedCommercialCategory = null;
        $this->selectedPriority = null;
        $this->resetPage();
    }
}
