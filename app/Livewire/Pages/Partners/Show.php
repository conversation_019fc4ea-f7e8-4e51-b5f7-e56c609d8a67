<?php

namespace App\Livewire\Pages\Partners;

use Flux\Flux;
use App\Models\User;
use App\Models\Brand;
use App\Models\Contact;
use App\Models\Partner;
use Livewire\Component;
use Illuminate\View\View;
use App\Enums\PartnerTypes;
use App\Models\PaymentTerm;
use App\Enums\DeliveryTerms;
use Livewire\WithPagination;
use Livewire\Attributes\Computed;
use App\Enums\CommercialCategories;
use App\Livewire\Forms\PartnerForm;
use App\Enums\ClientPartnerPriorities;
use PrinsFrank\Standards\Country\CountryAlpha3;

class Show extends Component
{
    use WithPagination;
    
    public Partner $partner;
    public PartnerForm $form;

    public function mount(): void
    {
        $this->form->setPartner($this->partner);
    }

    public function render(): View
    {
        return view('livewire.pages.partners.show', [
            'internalReferents' => User::query()->where('is_employee', TRUE)->orderBy('id', 'desc')->get(),
            'areaManagers' => User::query()->where('is_employee', TRUE)->orderBy('id', 'desc')->get(),
            'commercialCategories' => CommercialCategories::cases(),
            'deliveryTerms' => DeliveryTerms::cases(),
            'partnerPriorities' => ClientPartnerPriorities::cases(),
            'countries' => CountryAlpha3::cases(),
            'countries_of_expertise' => CountryAlpha3::cases(),
            'paymentTerms' => PaymentTerm::orderBy('id', 'desc')->get(),
            'partnerTypes' => PartnerTypes::cases(),
        ]);
    }

    public function edit($id): void
    {
        $this->redirectRoute('partners.edit', ['partner' => $id], navigate: true);
    }

    public function delete($id): void
    {
        $partner = Partner::findOrFail($id);

        // Check if the partner is associated to any clients
        if ($partner->clients()->count() > 0) {
            Flux::toast(
                variant: 'danger',
                text: 'You cannot delete this PARTNER because it is associated to one or more clients.'
            );
        }
        else {
            $partner->delete();

            Flux::toast(
                variant: 'success',
                text: 'The PARTNER has been deleted.'
            );

            $this->redirectRoute('partners.index', navigate: true);
        }
    }

    /**
     * Inner table listing of contacts
     */
    #[Computed]
    public function contacts()
    {
        return $this->partner->contacts()->paginate(5);
    }

    public function viewContact($id): void
    {
        $this->redirectRoute('contacts.show', ['contact' => $id], navigate: true);
    }

    public function editContact($id): void
    {
        $this->redirectRoute('contacts.edit', ['contact' => $id], navigate: true);
    }

    public function deleteContact($id): void
    {
        Contact::findOrFail($id)->delete();

        Flux::toast(
            variant: 'success',
            text: 'The CONTACT has been deleted.'
        );
    }

    /**
     * Inner table listing of brands
     */
    #[Computed]
    public function brands()
    {
        return $this->partner->brands()->paginate(5);
    }

    public function viewBrand($id): void
    {
        $this->redirectRoute('brands.show', ['brand' => $id], navigate: true);
    }

    public function editBrand($id): void
    {
        $this->redirectRoute('brands.edit', ['brand' => $id], navigate: true);
    }

    public function deleteBrand($id): void
    {
        Brand::findOrFail($id)->delete();

        Flux::toast(
            variant: 'success',
            text: 'The BRAND has been deleted.'
        );
    }
}
