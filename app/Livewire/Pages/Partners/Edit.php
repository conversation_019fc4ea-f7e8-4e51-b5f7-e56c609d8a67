<?php

namespace App\Livewire\Pages\Partners;

use Flux\Flux;
use App\Models\User;
use App\Models\Partner;
use Livewire\Component;
use App\Enums\PartnerTypes;
use App\Models\PaymentTerm;
use App\Enums\DeliveryTerms;
use App\Enums\CommercialCategories;
use App\Livewire\Forms\PartnerForm;
use App\Enums\ClientPartnerPriorities;
use PrinsFrank\Standards\Country\CountryAlpha3;

class Edit extends Component
{
    public Partner $partner;
    public PartnerForm $form;

    public function mount(): void
    {
        $this->form->setPartner($this->partner);
    }

    public function render()
    {
        return view('livewire.pages.partners.edit', [
            'internalReferents' => User::query()->where('is_employee', TRUE)->orderBy('id', 'desc')->get(),
            'areaManagers' => User::query()->where('is_employee', TRUE)->orderBy('id', 'desc')->get(),
            'commercialCategories' => CommercialCategories::cases(),
            'deliveryTerms' => DeliveryTerms::cases(),
            'partnerPriorities' => ClientPartnerPriorities::cases(),
            'countries' => CountryAlpha3::cases(),
            'countries_of_expertise' => CountryAlpha3::cases(),
            'paymentTerms' => PaymentTerm::orderBy('id', 'desc')->get(),
            'partnerTypes' => PartnerTypes::cases(),
        ]);
    }

    public function save(): void
    {
        $this->form->update();

        Flux::toast(
            variant: 'success',
            text: 'The PARTNER has been updated.'
        );

        $this->redirectRoute('partners.index', navigate: true);
    }
}
