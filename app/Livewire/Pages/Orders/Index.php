<?php

namespace App\Livewire\Pages\Orders;

use App\Enums\OrderStatuses;
use App\Models\Order\Order;
use Flux\Flux;
use Livewire\Attributes\Computed;
use Livewire\Component;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;

    public $search = '';

    public $itemsPerPage = 10;

    public $sortBy = 'id';

    public $sortDirection = 'desc';

    // Filtri
    public $hasConfirmationFile = null;

    public $selectedStatus = null;

    public $hasCustomProducts = null;

    public function render()
    {
        return view('livewire.pages.orders.index', [
            'statuses' => OrderStatuses::cases(),
            'confirmationFileOptions' => [
                '1' => 'Yes',
                '0' => 'No',
            ],
            'customProductsOptions' => [
                '1' => 'Yes',
                '0' => 'No',
            ],
        ]);
    }

    #[Computed]
    public function orders()
    {
        return Order::query()
            ->when($this->search, function ($query) {
                $query->where(function ($subQuery) {
                    $subQuery->where('code', 'ilike', '%'.$this->search.'%')
                        ->orWhere('order_code', 'ilike', '%'.$this->search.'%')
                        ->orWhere('order_code_progressive', 'ilike', '%'.$this->search.'%')
                        ->orWhere('order_code_date', 'ilike', '%'.$this->search.'%')
                        ->orWhere('order_code_type', 'ilike', '%'.$this->search.'%')
                        ->orWhere('order_code_region', 'ilike', '%'.$this->search.'%')
                        ->orWhere('description', 'ilike', '%'.$this->search.'%')
                        ->orWhereHas('internalReferent', function ($query) {
                            $query->where('first_name', 'ilike', '%'.$this->search.'%')
                                ->orWhere('last_name', 'ilike', '%'.$this->search.'%');
                        })
                        ->orWhereHas('client', function ($query) {
                            $query->where('company', 'ilike', '%'.$this->search.'%');
                        })
                        ->orWhereHas('partner', function ($query) {
                            $query->where('company', 'ilike', '%'.$this->search.'%');
                        });
                });
            })
            ->when($this->hasConfirmationFile !== null && $this->hasConfirmationFile !== '', function ($query) {
                if ($this->hasConfirmationFile === '1') {
                    $query->whereNotNull('confirmation_file');
                } else {
                    $query->whereNull('confirmation_file');
                }
            })
            ->when($this->selectedStatus, function ($query) {
                $query->where('status', $this->selectedStatus);
            })
            ->when($this->hasCustomProducts !== null && $this->hasCustomProducts !== '', function ($query) {
                if ($this->hasCustomProducts === '1') {
                    $query->whereHas('cartGroups.orderRows', function ($subQuery) {
                        $subQuery->whereNotNull('custom_product_id');
                    });
                } else {
                    $query->whereDoesntHave('cartGroups.orderRows', function ($subQuery) {
                        $subQuery->whereNotNull('custom_product_id');
                    });
                }
            })
            ->tap(fn ($query) => $this->sortBy ? $query->orderBy($this->sortBy, $this->sortDirection) : $query)
            ->paginate($this->itemsPerPage);
    }

    public function sort($column)
    {
        if ($this->sortBy === $column) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $column;
            $this->sortDirection = 'asc';
        }
    }

    public function updatedSearch()
    {
        $this->resetPage();
        $this->sortBy = 'id';
        $this->sortDirection = 'desc';
    }

    public function updatedHasConfirmationFile()
    {
        $this->resetPage();
    }

    public function updatedSelectedStatus()
    {
        $this->resetPage();
    }

    public function updatedHasCustomProducts()
    {
        $this->resetPage();
    }

    public function view($id): void
    {
        $this->redirectRoute('orders.show', ['order' => $id], navigate: true);
    }

    public function edit($id): void
    {
        $this->redirectRoute('orders.edit', ['order' => $id], navigate: true);
    }

    public function delete($id): void
    {
        Order::findOrFail($id)->delete();

        Flux::toast(
            variant: 'success',
            text: 'The ORDER has been deleted.'
        );
    }

    public function areFiltersActive(): bool
    {
        return ! empty($this->search) ||
               ($this->hasConfirmationFile !== null && $this->hasConfirmationFile !== '') ||
               ! empty($this->selectedStatus) ||
               ($this->hasCustomProducts !== null && $this->hasCustomProducts !== '');
    }

    public function clearSearchAndFilters(): void
    {
        $this->search = '';
        $this->hasConfirmationFile = null;
        $this->selectedStatus = null;
        $this->hasCustomProducts = null;
        $this->resetPage();
    }
}
