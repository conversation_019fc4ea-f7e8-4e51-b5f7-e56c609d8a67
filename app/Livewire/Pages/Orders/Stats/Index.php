<?php

namespace App\Livewire\Pages\Orders\Stats;

use Flux\Flux;
use Livewire\Component;
use App\Models\Order\Order;
use Livewire\WithPagination;
use App\Models\DiscountGroup;
use Livewire\Attributes\Computed;

class Index extends Component
{
    use WithPagination;

    public Order $order;

    public $search = '';
    public $itemsPerPage = 10;

    public $sortBy = 'id';
    public $sortDirection = 'desc';

    // Edit Discount Group value form
    public $discountGroup;
    public $discountOverride;

    // Cache for optimized calculations
    protected $cachedOrderRowsByDiscountGroup = null;
    protected $cachedStatsData = null;

    public function mount(Order $order)
    {
        // Optimization: Eager load all necessary relationships
        $this->order = $order->load([
            'orderRows.product.discountGroup.brand',
            'orderRows.product.discountGroup',
        ]);
    }

    public function render()
    {
        return view('livewire.pages.orders.stats.index');
    }

    /**
     * Optimization: Pre-calculate and group all necessary data
     */
    protected function getOrderRowsByDiscountGroup()
    {
        if ($this->cachedOrderRowsByDiscountGroup === null) {
            $this->cachedOrderRowsByDiscountGroup = $this->order->orderRows
                ->filter(function ($orderRow) {
                    return $orderRow->product && $orderRow->product->discountGroup;
                })
                ->groupBy('product.discountGroup.id');
        }

        return $this->cachedOrderRowsByDiscountGroup;
    }

    /**
     * Optimization: Pre-calculate all statistics in a single pass
     */
    protected function getStatsData()
    {
        if ($this->cachedStatsData === null) {
            $orderRowsByDiscountGroup = $this->getOrderRowsByDiscountGroup();
            $this->cachedStatsData = [];

            foreach ($orderRowsByDiscountGroup as $discountGroupId => $rows) {
                $discountGroup = $rows->first()->product->discountGroup;

                $sellingPrice = $rows->sum(function ($orderRow) {
                    if ($orderRow->product->hasModules()) {
                        return $orderRow->quantity * ($orderRow->selling_price ?? $orderRow->product->getModularPrice($orderRow->options));
                    }
                    return $orderRow->quantity * ($orderRow->selling_price ?? $orderRow->product->selling_price);
                });

                $netValue = $rows->sum(function ($orderRow) use ($discountGroup) {
                    if ($orderRow->product->hasModules()) {
                        return $orderRow->quantity * ($orderRow->selling_price ?? $orderRow->product->getModularPrice($orderRow->options)) * (1 - $discountGroup->discount / 100);
                    }
                    $price = $orderRow->quantity * ($orderRow->selling_price ?? $orderRow->product->selling_price);
                    return $price * (1 - $discountGroup->discount / 100);
                });

                $clientNetValue = $rows->sum('rowFinalPrice');
                $marginValue = $clientNetValue - $netValue;
                $marginPercentage = $clientNetValue > 0 ? ($marginValue * 100 / $clientNetValue) : 0;

                $this->cachedStatsData[$discountGroupId] = (object) [
                    'selling_price' => $sellingPrice,
                    'discount' => $discountGroup->discount,
                    'net_value' => $netValue,
                    'client_discount' => $rows->avg(function ($orderRow) {
                        return $orderRow->discount_override ?? $orderRow->discount ?? $orderRow->getDiscountForClient();
                    }),
                    'variation' => $rows->avg(function ($orderRow) {
                        return $orderRow->variation ?? 0;
                    }),
                    'client_net_value' => $clientNetValue,
                    'margin_percentage' => number_format($marginPercentage, 2, '.', ''),
                    'margin_value' => $marginValue,
                ];
            }
        }

        return $this->cachedStatsData;
    }

    #[Computed]
    public function discountGroups()
    {
        $discountGroupIds = array_keys($this->getOrderRowsByDiscountGroup()->toArray());

        return DiscountGroup::query()
            ->whereIn('id', $discountGroupIds)
            ->with('brand')
            ->tap(fn ($query) => $this->sortBy ? $query->orderBy($this->sortBy, $this->sortDirection) : $query)
            ->paginate($this->itemsPerPage);
    }

    #[Computed]
    public function amountData()
    {
        $orderRowsByDiscountGroup = $this->getOrderRowsByDiscountGroup();

        return $orderRowsByDiscountGroup
            ->map(function ($rows) {
                return [
                    'discount_group' => $rows->first()->product->discountGroup->code,
                    'amount' => $rows->sum('rowFinalPrice')
                ];
            })
            ->values()
            ->toArray();
    }

    #[Computed]
    public function discountData()
    {
        $orderRowsByDiscountGroup = $this->getOrderRowsByDiscountGroup();

        return $orderRowsByDiscountGroup
            ->map(function ($rows) {
                return [
                    'discount_group' => $rows->first()->product->discountGroup->code,
                    'client_discount' => $rows->avg(function ($orderRow) {
                        return ($orderRow->discount_override ?? $orderRow->discount ?? $orderRow->getDiscountForClient()) / 100;
                    }),
                ];
            })
            ->values()
            ->toArray();
    }

    /**
     * Optimization: Use pre-calculated data instead of recalculating each time
     */
    public function stats(DiscountGroup $discountGroup)
    {
        $statsData = $this->getStatsData();
        return $statsData[$discountGroup->id] ?? (object) [
            'selling_price' => 0,
            'discount' => $discountGroup->discount,
            'net_value' => 0,
            'client_discount' => 0,
            'variation' => 0,
            'client_net_value' => 0,
            'margin_percentage' => '0.00',
            'margin_value' => 0,
        ];
    }

    public function sort($column) {
        if ($this->sortBy === $column) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $column;
            $this->sortDirection = 'asc';
        }
    }

    public function updatedSearch()
    {
        $this->resetPage();
        $this->sortBy = 'id';
        $this->sortDirection = 'desc';
    }

    public function backToOrder($id)
    {
        $this->redirectRoute('orders.show', ['order' => $id], navigate: true);
    }

    public function editDiscountGroup($id): void
    {
        $this->discountGroup = DiscountGroup::findOrFail($id);

        Flux::modal('edit-discount-group')->show();
    }

    public function updateDiscountGroup(): void
    {
        // Validate discount override
        $this->validate([
            'discountOverride' => 'required|numeric|min:0|max:100', // MEMO, add check if discount is not higher than the max discount for the client
        ]);

        // Update discount group override for all order rows that have the discount group
        $this->order->orderRows
            ->filter(function ($orderRow) {
                return $orderRow->product && $orderRow->product->discountGroup->id === $this->discountGroup->id;
            })
            ->each(function ($orderRow) {
                $orderRow->update([
                    'discount_override' => $this->discountOverride,
                ]);
            });

        // Reset cache after update
        $this->resetCache();

        Flux::toast(
            variant: 'success',
            text: 'The DISCOUNT GROUP has been updated for all rows.'
        );

        Flux::modals()->close();
        $this->resetForm();
    }

    public function resetForm(): void
    {
        $this->discountGroup = null;
        $this->discountOverride = null;
        $this->resetValidation();
    }

    /**
     * Reset cache when necessary
     */
    protected function resetCache(): void
    {
        $this->cachedOrderRowsByDiscountGroup = null;
        $this->cachedStatsData = null;
    }

    /**
     * Reset cache when order is updated
     */
    public function updatedOrder(): void
    {
        $this->resetCache();
    }
}
