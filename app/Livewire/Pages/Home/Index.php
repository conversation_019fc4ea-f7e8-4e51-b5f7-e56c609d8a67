<?php

namespace App\Livewire\Pages\Home;

use App\Models\User;
use App\Models\Brand;
use App\Models\Client;
use App\Models\Contact;
use App\Models\Partner;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Auth;

class Index extends Component
{
    use WithPagination;
    
    public User $user;

    public function mount()
    {
        $this->user = Auth::user();
    }

    public function render()
    {
        $clients = Client::query()
            ->where('updated_at', '>=', now()->subDays(7))
            ->orderBy('updated_at', 'desc')
            ->paginate(5, pageName: 'clients-page');

        $brands = Brand::query()
            ->where('updated_at', '>=', now()->subDays(7))
            ->orderBy('updated_at', 'desc')
            ->paginate(5, pageName: 'brands-page');

        return view('livewire.pages.home.index', [
            'clientsCount' => Client::count(),
            'clientsGrowth' => $this->calculateClientsGrowth(),
            'partnersCount' => Partner::count(),
            'partnersGrowth' => $this->calculatePartnersGrowth(),
            'brandsCount' => Brand::count(),
            'brandsGrowth' => $this->calculateBrandsGrowth(),
            'contactsCount' => Contact::count(),
            'contactsGrowth' => $this->calculateContactsGrowth(),
            'clients' => $clients,
            'brands' => $brands,
        ]);
    }

    public function calculateClientsGrowth()
    {
        $currentPeriodCount = Client::query()
            ->where('created_at', '>=', now()->subDays(7))
            ->count();

        $previousPeriodCount = Client::query()
            ->whereBetween('created_at', [now()->subDays(14), now()->subDays(7)])
            ->count();

        if ($previousPeriodCount > 0) {
            return intval((($currentPeriodCount - $previousPeriodCount) / $previousPeriodCount) * 100);
        } elseif ($previousPeriodCount == 0 && $currentPeriodCount > 0) {
            return 100; // 100% growth if there were no clients in the previous period but there are in the current period
        } elseif ($previousPeriodCount > 0 && $currentPeriodCount < $previousPeriodCount) {
            return intval((($currentPeriodCount - $previousPeriodCount) / $previousPeriodCount) * 100); // Negative growth if clients decreased
        } else {
            return 0; // No growth if there were no clients in both periods
        }
    }

    public function calculatePartnersGrowth()
    {
        $currentPeriodCount = Partner::query()
            ->where('created_at', '>=', now()->subDays(7))
            ->count();

        $previousPeriodCount = Partner::query()
            ->whereBetween('created_at', [now()->subDays(14), now()->subDays(7)])
            ->count();

        if ($previousPeriodCount > 0) {
            return intval((($currentPeriodCount - $previousPeriodCount) / $previousPeriodCount) * 100);
        } elseif ($previousPeriodCount == 0 && $currentPeriodCount > 0) {
            return 100; // 100% growth if there were no partners in the previous period but there are in the current period
        } elseif ($previousPeriodCount > 0 && $currentPeriodCount < $previousPeriodCount) {
            return intval((($currentPeriodCount - $previousPeriodCount) / $previousPeriodCount) * 100); // Negative growth if partners decreased
        } else {
            return 0; // No growth if there were no partners in both periods
        }
    }

    public function calculateBrandsGrowth()
    {
        $currentPeriodCount = Brand::query()
            ->where('created_at', '>=', now()->subDays(7))
            ->count();

        $previousPeriodCount = Brand::query()
            ->whereBetween('created_at', [now()->subDays(14), now()->subDays(7)])
            ->count();

        if ($previousPeriodCount > 0) {
            return intval((($currentPeriodCount - $previousPeriodCount) / $previousPeriodCount) * 100);
        } elseif ($previousPeriodCount == 0 && $currentPeriodCount > 0) {
            return 100; // 100% growth if there were no brands in the previous period but there are in the current period
        } elseif ($previousPeriodCount > 0 && $currentPeriodCount < $previousPeriodCount) {
            return intval((($currentPeriodCount - $previousPeriodCount) / $previousPeriodCount) * 100); // Negative growth if brands decreased
        } else {
            return 0; // No growth if there were no brands in both periods
        }
    }

    public function calculateContactsGrowth()
    {
        $currentPeriodCount = Contact::query()
            ->where('created_at', '>=', now()->subDays(7))
            ->count();

        $previousPeriodCount = Contact::query()
            ->whereBetween('created_at', [now()->subDays(14), now()->subDays(7)])
            ->count();

        if ($previousPeriodCount > 0) {
            return intval((($currentPeriodCount - $previousPeriodCount) / $previousPeriodCount) * 100);
        } elseif ($previousPeriodCount == 0 && $currentPeriodCount > 0) {
            return 100; // 100% growth if there were no contacts in the previous period but there are in the current period
        } elseif ($previousPeriodCount > 0 && $currentPeriodCount < $previousPeriodCount) {
            return intval((($currentPeriodCount - $previousPeriodCount) / $previousPeriodCount) * 100); // Negative growth if contacts decreased
        } else {
            return 0; // No growth if there were no contacts in both periods
        }
    }
}
