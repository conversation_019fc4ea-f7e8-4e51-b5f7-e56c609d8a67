<?php

namespace App\Livewire\Pages\Products;

use App\Models\Product;
use Livewire\Component;
use Meilisearch\Client;
use App\Models\Collection;
use Livewire\Attributes\On;
use Livewire\WithPagination;
use Livewire\Attributes\Computed;

class Index extends Component
{
    use WithPagination;

    public $search = '';
    public $itemsPerPage = 20;

    public $selectedBrands = [];
    public $selectedTypes = [];
    public $selectedDestinationRooms = [];

    public $selectedStyles = [];
    public $selectedMaterials = [];
    public $selectedColors = [];
    public $selectedCollections = [];

    public $availableBrands = [];
    public $availableTypes = [];
    public $availableDestinationRooms = [];

    public $availableStyles = [];
    public $availableMaterials = [];
    public $availableColors = [];

    public $availableCollections = [];

    private Client $msClient;

    public function __construct()
    {
        $this->msClient = new Client(config('scout.host'), config('scout.key'));
    }

    public function render()
    {
        return view('livewire.pages.products.index', [
            'availableBrands' => $this->availableBrands,
            'availableTypes' => $this->availableTypes,
            'availableDestinationRooms' => $this->availableDestinationRooms,
            'availableStyles' => $this->availableStyles,
            'availableMaterials' => $this->availableMaterials,
            'availableColors' => $this->availableColors,
            'availableCollections' => $this->availableCollections,
        ]);
    }

    #[Computed]
    public function products()
    {
        // Get all products
        return Product::search($this->search ?: '*')
            ->when(count($this->selectedBrands), fn($q) => $q->whereIn('brand_name', $this->selectedBrands))
            ->when(count($this->selectedTypes), fn($q) => $q->whereIn('type', $this->selectedTypes))
            ->when(count($this->selectedDestinationRooms), fn($q) => $q->whereIn('destination_room', $this->selectedDestinationRooms))
            ->when(count($this->selectedStyles), fn($q) => $q->whereIn('style', $this->selectedStyles))
            ->when(count($this->selectedMaterials), fn($q) => $q->whereIn('material', $this->selectedMaterials))
            ->when(count($this->selectedColors), fn($q) => $q->whereIn('color', $this->selectedColors))
            ->when(count($this->selectedCollections), fn($q) => $q->whereIn('collection_name', $this->selectedCollections))
            ->orderBy('id', 'desc')
            ->paginate($this->itemsPerPage, pageName: 'products-page');
    }

    #[Computed]
    public function collections()
    {
        return Collection::whereIn('name', $this->availableCollections)
            ->orderBy('name')
            ->paginate($this->itemsPerPage, pageName: 'collections-page');
    }

    #[On('toggleCollection')]
    public function toggleCollection(string $collectionName): void
    {
        if (in_array($collectionName, $this->selectedCollections)) {
            $this->selectedCollections = array_diff($this->selectedCollections, [$collectionName]);
        } else {
            $this->selectedCollections[] = $collectionName;
        }

        $this->updatedSelectedCollections();
    }

    public function areFiltersActive(): bool
    {
        return  count($this->selectedBrands) ||
                count($this->selectedTypes) ||
                count($this->selectedDestinationRooms) ||
                count($this->selectedStyles) ||
                count($this->selectedMaterials) ||
                count($this->selectedColors) ||
                count($this->selectedCollections);
    }

    public function fetchAvailableBrands(): void
    {
        // Convert selected filters to MeiliSearch filters
        $filters = array_filter([
            count($this->selectedTypes) ? 'type IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedTypes)) . ']' : null,
            count($this->selectedDestinationRooms) ? 'destination_room IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedDestinationRooms)) . ']' : null,
            count($this->selectedStyles) ? 'style IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedStyles)) . ']' : null,
            count($this->selectedMaterials) ? 'material IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedMaterials)) . ']' : null,
            count($this->selectedColors) ? 'color IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedColors)) . ']' : null,
            count($this->selectedCollections) ? 'collection_name IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedCollections)) . ']' : null,
        ]);

        // Get facets from MeiliSearch
        $facets = $this->msClient->index('products')->search($this->search ?: '*', [
            'facets' => ['brand_name'],
            'filter' => implode(' AND ', $filters),
        ]);

        // Get available brands from facets
        $this->availableBrands = array_keys($facets->getRaw()['facetDistribution']['brand_name'] ?? []);
    }

    public function fetchAvailableTypes(): void
    {
        // Convert selected filters to MeiliSearch filters
        $filters = array_filter([
            count($this->selectedBrands) ? 'brand_name IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedBrands)) . ']' : null,
            count($this->selectedDestinationRooms) ? 'destination_room IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedDestinationRooms)) . ']' : null,
            count($this->selectedStyles) ? 'style IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedStyles)) . ']' : null,
            count($this->selectedMaterials) ? 'material IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedMaterials)) . ']' : null,
            count($this->selectedColors) ? 'color IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedColors)) . ']' : null,
            count($this->selectedCollections) ? 'collection_name IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedCollections)) . ']' : null,
        ]);

        // Get facets from MeiliSearch
        $facets = $this->msClient->index('products')->search($this->search ?: '*', [
            'facets' => ['type'],
            'filter' => implode(' AND ', $filters),
        ]);

        // Get available types from facets
        $this->availableTypes = array_keys($facets->getRaw()['facetDistribution']['type'] ?? []);
    }

    public function fetchAvailableDestinationRooms(): void
    {
        // Convert selected filters to MeiliSearch filters
        $filters = array_filter([
            count($this->selectedBrands) ? 'brand_name IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedBrands)) . ']' : null,
            count($this->selectedTypes) ? 'type IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedTypes)) . ']' : null,
            count($this->selectedStyles) ? 'style IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedStyles)) . ']' : null,
            count($this->selectedMaterials) ? 'material IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedMaterials)) . ']' : null,
            count($this->selectedColors) ? 'color IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedColors)) . ']' : null,
            count($this->selectedCollections) ? 'collection_name IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedCollections)) . ']' : null,
        ]);

        // Get facets from MeiliSearch
        $facets = $this->msClient->index('products')->search($this->search ?: '*', [
            'facets' => ['destination_room'],
            'filter' => implode(' AND ', $filters),
        ]);

        // Get available destination rooms from facets
        $this->availableDestinationRooms = array_keys($facets->getRaw()['facetDistribution']['destination_room'] ?? []);
    }

    public function fetchAvailableStyles(): void
    {
        // Convert selected filters to MeiliSearch filters
        $filters = array_filter([
            count($this->selectedBrands) ? 'brand_name IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedBrands)) . ']' : null,
            count($this->selectedTypes) ? 'type IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedTypes)) . ']' : null,
            count($this->selectedDestinationRooms) ? 'destination_room IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedDestinationRooms)) . ']' : null,
            count($this->selectedMaterials) ? 'material IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedMaterials)) . ']' : null,
            count($this->selectedColors) ? 'color IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedColors)) . ']' : null,
            count($this->selectedCollections) ? 'collection_name IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedCollections)) . ']' : null,
        ]);

        // Get facets from MeiliSearch
        $facets = $this->msClient->index('products')->search($this->search ?: '*', [
            'facets' => ['style'],
            'filter' => implode(' AND ', $filters),
        ]);

        // Get available styles from facets
        $this->availableStyles = array_keys($facets->getRaw()['facetDistribution']['style'] ?? []);
    }

    public function fetchAvailableMaterials(): void
    {
        // Convert selected filters to MeiliSearch filters
        $filters = array_filter([
            count($this->selectedBrands) ? 'brand_name IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedBrands)) . ']' : null,
            count($this->selectedTypes) ? 'type IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedTypes)) . ']' : null,
            count($this->selectedDestinationRooms) ? 'destination_room IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedDestinationRooms)) . ']' : null,
            count($this->selectedStyles) ? 'style IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedStyles)) . ']' : null,
            count($this->selectedColors) ? 'color IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedColors)) . ']' : null,
            count($this->selectedCollections) ? 'collection_name IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedCollections)) . ']' : null,
        ]);

        // Get facets from MeiliSearch
        $facets = $this->msClient->index('products')->search($this->search ?: '*', [
            'facets' => ['material'],
            'filter' => implode(' AND ', $filters),
        ]);

        // Get available materials from facets
        $this->availableMaterials = array_keys($facets->getRaw()['facetDistribution']['material'] ?? []);
    }

    public function fetchAvailableColors(): void
    {
        // Convert selected filters to MeiliSearch filters
        $filters = array_filter([
            count($this->selectedBrands) ? 'brand_name IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedBrands)) . ']' : null,
            count($this->selectedTypes) ? 'type IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedTypes)) . ']' : null,
            count($this->selectedDestinationRooms) ? 'destination_room IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedDestinationRooms)) . ']' : null,
            count($this->selectedStyles) ? 'style IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedStyles)) . ']' : null,
            count($this->selectedMaterials) ? 'material IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedMaterials)) . ']' : null,
            count($this->selectedCollections) ? 'collection_name IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedCollections)) . ']' : null,
        ]);

        // Get facets from MeiliSearch
        $facets = $this->msClient->index('products')->search($this->search ?: '*', [
            'facets' => ['color'],
            'filter' => implode(' AND ', $filters),
        ]);

        // Get available colors from facets
        $this->availableColors = array_keys($facets->getRaw()['facetDistribution']['color'] ?? []);
    }

    public function fetchAvailableCollections(): void
    {
        // Convert selected filters to MeiliSearch filters
        $filters = array_filter([
            count($this->selectedBrands) ? 'brand_name IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedBrands)) . ']' : null,
            count($this->selectedTypes) ? 'type IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedTypes)) . ']' : null,
            count($this->selectedDestinationRooms) ? 'destination_room IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedDestinationRooms)) . ']' : null,
            count($this->selectedStyles) ? 'style IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedStyles)) . ']' : null,
            count($this->selectedMaterials) ? 'material IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedMaterials)) . ']' : null,
            count($this->selectedColors) ? 'color IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedColors)) . ']' : null,
        ]);

        // Get facets from MeiliSearch
        $facets = $this->msClient->index('products')->search($this->search ?: '*', [
            'facets' => ['collection_name'],
            'filter' => implode(' AND ', $filters),
        ]);

        // Get available collections from facets
        $this->availableCollections = array_keys($facets->getRaw()['facetDistribution']['collection_name'] ?? []);
    }

    /*
     * Clear search and filters and reset pagination
     */
    public function clearSearchAndFilters(): void
    {
        $this->search = '';
        $this->selectedBrands = [];
        $this->selectedTypes = [];
        $this->selectedDestinationRooms = [];

        $this->selectedStyles = [];
        $this->selectedMaterials = [];
        $this->selectedColors = [];

        $this->selectedCollections = [];

        $this->fetchAvailableCollections();

        $this->resetPage('products-page');
        $this->resetPage('collections-page');
    }

    public function updatedSearch()
    {        
        $this->fetchAvailableCollections();

        if (count($this->selectedBrands) > 0) {
            $this->fetchAvailableBrands();
        }
        if (count($this->selectedTypes) > 0) {
            $this->fetchAvailableTypes();
        }
        if (count($this->selectedDestinationRooms) > 0) {
            $this->fetchAvailableDestinationRooms();
        }
        if (count($this->selectedStyles) > 0) {
            $this->fetchAvailableStyles();
        }
        if (count($this->selectedMaterials) > 0) {
            $this->fetchAvailableMaterials();
        }
        if (count($this->selectedColors) > 0) {
            $this->fetchAvailableColors();
        }

        $this->selectedCollections = array_values(array_intersect($this->selectedCollections, $this->availableCollections));
        $this->selectedBrands = array_values(array_intersect($this->selectedBrands, $this->availableBrands));
        $this->selectedTypes = array_values(array_intersect($this->selectedTypes, $this->availableTypes));
        $this->selectedDestinationRooms = array_values(array_intersect($this->selectedDestinationRooms, $this->availableDestinationRooms));
        $this->selectedStyles = array_values(array_intersect($this->selectedStyles, $this->availableStyles));
        $this->selectedMaterials = array_values(array_intersect($this->selectedMaterials, $this->availableMaterials));
        $this->selectedColors = array_values(array_intersect($this->selectedColors, $this->availableColors));

        $this->resetPage('products-page');
        $this->resetPage('collections-page');
    }

    public function updatedSelectedBrands()
    {
        $this->fetchAvailableCollections();

        if (count($this->selectedTypes) > 0) {
            $this->fetchAvailableTypes();
        }
        if (count($this->selectedDestinationRooms) > 0) {
            $this->fetchAvailableDestinationRooms();
        }
        if (count($this->selectedStyles) > 0) {
            $this->fetchAvailableStyles();
        }
        if (count($this->selectedMaterials) > 0) {
            $this->fetchAvailableMaterials();
        }
        if (count($this->selectedColors) > 0) {
            $this->fetchAvailableColors();
        }

        $this->selectedCollections = array_values(array_intersect($this->selectedCollections, $this->availableCollections));
        $this->selectedTypes = array_values(array_intersect($this->selectedTypes, $this->availableTypes));
        $this->selectedDestinationRooms = array_values(array_intersect($this->selectedDestinationRooms, $this->availableDestinationRooms));
        $this->selectedStyles = array_values(array_intersect($this->selectedStyles, $this->availableStyles));
        $this->selectedMaterials = array_values(array_intersect($this->selectedMaterials, $this->availableMaterials));
        $this->selectedColors = array_values(array_intersect($this->selectedColors, $this->availableColors));

        $this->resetPage('products-page');
        $this->resetPage('collections-page');
    }

    public function updatedSelectedTypes()
    {
        $this->fetchAvailableCollections();

        if (count($this->selectedBrands) > 0) {
            $this->fetchAvailableBrands();
        }
        if (count($this->selectedDestinationRooms) > 0) {
            $this->fetchAvailableDestinationRooms();
        }
        if (count($this->selectedStyles) > 0) {
            $this->fetchAvailableStyles();
        }
        if (count($this->selectedMaterials) > 0) {
            $this->fetchAvailableMaterials();
        }
        if (count($this->selectedColors) > 0) {
            $this->fetchAvailableColors();
        }

        $this->selectedCollections = array_values(array_intersect($this->selectedCollections, $this->availableCollections));
        $this->selectedBrands = array_values(array_intersect($this->selectedBrands, $this->availableBrands));
        $this->selectedDestinationRooms = array_values(array_intersect($this->selectedDestinationRooms, $this->availableDestinationRooms));
        $this->selectedStyles = array_values(array_intersect($this->selectedStyles, $this->availableStyles));
        $this->selectedMaterials = array_values(array_intersect($this->selectedMaterials, $this->availableMaterials));
        $this->selectedColors = array_values(array_intersect($this->selectedColors, $this->availableColors));

        $this->resetPage('products-page');
        $this->resetPage('collections-page');
    }

    public function updatedSelectedDestinationRooms()
    {
        $this->fetchAvailableCollections();

        if (count($this->selectedBrands) > 0) {
            $this->fetchAvailableBrands();
        }
        if (count($this->selectedTypes) > 0) {
            $this->fetchAvailableTypes();
        }
        if (count($this->selectedStyles) > 0) {
            $this->fetchAvailableStyles();
        }
        if (count($this->selectedMaterials) > 0) {
            $this->fetchAvailableMaterials();
        }
        if (count($this->selectedColors) > 0) {
            $this->fetchAvailableColors();
        }

        $this->selectedCollections = array_values(array_intersect($this->selectedCollections, $this->availableCollections));
        $this->selectedBrands = array_values(array_intersect($this->selectedBrands, $this->availableBrands));
        $this->selectedTypes = array_values(array_intersect($this->selectedTypes, $this->availableTypes));
        $this->selectedStyles = array_values(array_intersect($this->selectedStyles, $this->availableStyles));
        $this->selectedMaterials = array_values(array_intersect($this->selectedMaterials, $this->availableMaterials));
        $this->selectedColors = array_values(array_intersect($this->selectedColors, $this->availableColors));

        $this->resetPage('products-page');
        $this->resetPage('collections-page');
    }

    public function updatedSelectedStyles()
    {
        $this->fetchAvailableCollections();

        if (count($this->selectedBrands) > 0) {
            $this->fetchAvailableBrands();
        }
        if (count($this->selectedTypes) > 0) {
            $this->fetchAvailableTypes();
        }
        if (count($this->selectedDestinationRooms) > 0) {
            $this->fetchAvailableDestinationRooms();
        }
        if (count($this->selectedMaterials) > 0) {
            $this->fetchAvailableMaterials();
        }
        if (count($this->selectedColors) > 0) {
            $this->fetchAvailableColors();
        }

        $this->selectedCollections = array_values(array_intersect($this->selectedCollections, $this->availableCollections));
        $this->selectedBrands = array_values(array_intersect($this->selectedBrands, $this->availableBrands));
        $this->selectedTypes = array_values(array_intersect($this->selectedTypes, $this->availableTypes));
        $this->selectedDestinationRooms = array_values(array_intersect($this->selectedDestinationRooms, $this->availableDestinationRooms));
        $this->selectedMaterials = array_values(array_intersect($this->selectedMaterials, $this->availableMaterials));
        $this->selectedColors = array_values(array_intersect($this->selectedColors, $this->availableColors));

        $this->resetPage('products-page');
        $this->resetPage('collections-page');
    }

    public function updatedSelectedMaterials()
    {
        $this->fetchAvailableCollections();

        if (count($this->selectedBrands) > 0) {
            $this->fetchAvailableBrands();
        }
        if (count($this->selectedTypes) > 0) {
            $this->fetchAvailableTypes();
        }
        if (count($this->selectedDestinationRooms) > 0) {
            $this->fetchAvailableDestinationRooms();
        }
        if (count($this->selectedStyles) > 0) {
            $this->fetchAvailableStyles();
        }
        if (count($this->selectedColors) > 0) {
            $this->fetchAvailableColors();
        }

        $this->selectedCollections = array_values(array_intersect($this->selectedCollections, $this->availableCollections));
        $this->selectedBrands = array_values(array_intersect($this->selectedBrands, $this->availableBrands));
        $this->selectedTypes = array_values(array_intersect($this->selectedTypes, $this->availableTypes));
        $this->selectedDestinationRooms = array_values(array_intersect($this->selectedDestinationRooms, $this->availableDestinationRooms));
        $this->selectedStyles = array_values(array_intersect($this->selectedStyles, $this->availableStyles));
        $this->selectedColors = array_values(array_intersect($this->selectedColors, $this->availableColors));

        $this->resetPage('products-page');
        $this->resetPage('collections-page');
    }

    public function updatedSelectedColors()
    {
        $this->fetchAvailableCollections();

        if (count($this->selectedBrands) > 0) {
            $this->fetchAvailableBrands();
        }
        if (count($this->selectedTypes) > 0) {
            $this->fetchAvailableTypes();
        }
        if (count($this->selectedDestinationRooms) > 0) {
            $this->fetchAvailableDestinationRooms();
        }
        if (count($this->selectedStyles) > 0) {
            $this->fetchAvailableStyles();
        }
        if (count($this->selectedMaterials) > 0) {
            $this->fetchAvailableMaterials();
        }

        $this->selectedCollections = array_values(array_intersect($this->selectedCollections, $this->availableCollections));
        $this->selectedBrands = array_values(array_intersect($this->selectedBrands, $this->availableBrands));
        $this->selectedTypes = array_values(array_intersect($this->selectedTypes, $this->availableTypes));
        $this->selectedDestinationRooms = array_values(array_intersect($this->selectedDestinationRooms, $this->availableDestinationRooms));
        $this->selectedStyles = array_values(array_intersect($this->selectedStyles, $this->availableStyles));
        $this->selectedMaterials = array_values(array_intersect($this->selectedMaterials, $this->availableMaterials));

        $this->resetPage('products-page');
        $this->resetPage('collections-page');
    }

    public function updatedSelectedCollections()
    {
        if (count($this->selectedBrands) > 0) {
            $this->fetchAvailableBrands();
        }
        if (count($this->selectedTypes) > 0) {
            $this->fetchAvailableTypes();
        }
        if (count($this->selectedDestinationRooms) > 0) {
            $this->fetchAvailableDestinationRooms();
        }
        if (count($this->selectedStyles) > 0) {
            $this->fetchAvailableStyles();
        }
        if (count($this->selectedMaterials) > 0) {
            $this->fetchAvailableMaterials();
        }
        if (count($this->selectedColors) > 0) {
            $this->fetchAvailableColors();
        }

        $this->selectedBrands = array_values(array_intersect($this->selectedBrands, $this->availableBrands));
        $this->selectedTypes = array_values(array_intersect($this->selectedTypes, $this->availableTypes));
        $this->selectedDestinationRooms = array_values(array_intersect($this->selectedDestinationRooms, $this->availableDestinationRooms));
        $this->selectedStyles = array_values(array_intersect($this->selectedStyles, $this->availableStyles));
        $this->selectedMaterials = array_values(array_intersect($this->selectedMaterials, $this->availableMaterials));
        $this->selectedColors = array_values(array_intersect($this->selectedColors, $this->availableColors));

        $this->resetPage('products-page');
        $this->resetPage('collections-page');
    }

    public function updatedItemsPerPage()
    {
        $this->resetPage('products-page');
        $this->resetPage('collections-page');
    }
}
