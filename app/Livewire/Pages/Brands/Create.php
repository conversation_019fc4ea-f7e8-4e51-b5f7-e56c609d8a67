<?php

namespace App\Livewire\Pages\Brands;

use Flux\Flux;
use Livewire\Component;
use App\Enums\BrandRating;
use App\Enums\BrandLeadTime;
use App\Enums\DeliveryTerms;
use Livewire\WithFileUploads;
use App\Enums\BrandPriceRange;
use App\Livewire\Forms\BrandForm;
use App\Enums\BrandPartnershipLevel;

class Create extends Component
{
    use WithFileUploads;
    
    public BrandForm $form;

    public function render()
    {
        return view('livewire.pages.brands.create', [
            'brandPriceRanges' => BrandPriceRange::cases(),
            'brandRatings' => BrandRating::cases(),
            'brandPartnershipLevels' => BrandPartnershipLevel::cases(),
            'brandLeadTimes' => BrandLeadTime::cases(),
            'deliveryTerms' => DeliveryTerms::cases(),
        ]);
    }

    public function save(): void
    {
        $this->form->store();

        Flux::toast(
            variant: 'success',
            text: 'The BRAND has been created.'
        );

        $this->redirectRoute('brands.index', navigate: true);
    }

    public function unsetImage(): void
    {
        $this->form->image = null;
    }
}
