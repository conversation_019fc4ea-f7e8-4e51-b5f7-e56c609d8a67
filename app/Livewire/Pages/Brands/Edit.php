<?php

namespace App\Livewire\Pages\Brands;

use Flux\Flux;
use App\Models\Brand;
use Livewire\Component;
use App\Enums\BrandRating;
use App\Enums\BrandLeadTime;
use App\Enums\DeliveryTerms;
use Livewire\WithFileUploads;
use App\Enums\BrandPriceRange;
use App\Livewire\Forms\BrandForm;
use App\Enums\BrandPartnershipLevel;

class Edit extends Component
{
    use WithFileUploads;
    
    public Brand $brand;
    public BrandForm $form;

    public function mount(Brand $brand)
    {
        $this->form->setBrand($brand);
    }

    public function render()
    {
        return view('livewire.pages.brands.edit', [
            'brandPriceRanges' => BrandPriceRange::cases(),
            'brandRatings' => BrandRating::cases(),
            'brandPartnershipLevels' => BrandPartnershipLevel::cases(),
            'brandLeadTimes' => BrandLeadTime::cases(),
            'deliveryTerms' => DeliveryTerms::cases(),
        ]);
    }

    public function update(): void
    {
        $this->form->update();

        Flux::toast(
            variant: 'success',
            text: 'The BRAND has been updated.'
        );

        $this->redirectRoute('brands.index', navigate: true);
    }

    public function unsetImage(): void
    {
        $this->form->image = null;
    }
}
