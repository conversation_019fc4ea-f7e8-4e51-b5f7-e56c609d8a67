<?php

namespace App\Livewire\Pages\Clients;

use App\Enums\ClientPartnerPriorities;
use App\Enums\ClientTypes;
use App\Enums\CommercialCategories;
use App\Models\Client;
use Flux\Flux;
use Livewire\Attributes\Computed;
use Livewire\Component;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;

    public $search = '';

    public $itemsPerPage = 10;

    public $sortBy = 'id';

    public $sortDirection = 'desc';

    // Filtri
    public $selectedType = null;

    public $selectedCommercialCategory = null;

    public $selectedPriority = null;

    public function render()
    {
        return view('livewire.pages.clients.index', [
            'types' => ClientTypes::cases(),
            'commercialCategories' => CommercialCategories::cases(),
            'priorities' => ClientPartnerPriorities::cases(),
        ]);
    }

    #[Computed]
    public function clients()
    {
        return Client::query()
            ->when($this->search, function ($query) {
                $query->where(function ($subQuery) {
                    $subQuery->where('company', 'ilike', '%'.$this->search.'%')
                        ->orWhere('type', 'ilike', '%'.$this->search.'%')
                        ->orWhere('commercial_category', 'ilike', '%'.$this->search.'%')
                        ->orWhere('priority', 'ilike', '%'.$this->search.'%')
                        ->orWhereHas('internalReferent', function ($query) {
                            $query->where('first_name', 'ilike', '%'.$this->search.'%')
                                ->orWhere('last_name', 'ilike', '%'.$this->search.'%');
                        })
                        ->orWhereHas('partner', function ($query) {
                            $query->where('company', 'ilike', '%'.$this->search.'%');
                        })
                        ->orWhereHas('addresses', function ($query) {
                            $query->where('name', 'ilike', '%'.$this->search.'%')
                                ->orWhere('company', 'ilike', '%'.$this->search.'%')
                                ->orWhere('vat_number', 'ilike', '%'.$this->search.'%')
                                ->orWhere('fiscal_code', 'ilike', '%'.$this->search.'%')
                                ->orWhere('sdi_code', 'ilike', '%'.$this->search.'%')
                                ->orWhere('street', 'ilike', '%'.$this->search.'%')
                                ->orWhere('city', 'ilike', '%'.$this->search.'%')
                                ->orWhere('state', 'ilike', '%'.$this->search.'%')
                                ->orWhere('zip', 'ilike', '%'.$this->search.'%')
                                ->orWhere('code_invoicing', 'ilike', '%'.$this->search.'%')
                                ->orWhere('code_shipping', 'ilike', '%'.$this->search.'%');
                        });
                });
            })
            ->when($this->selectedType, function ($query) {
                $query->where('type', $this->selectedType);
            })
            ->when($this->selectedCommercialCategory, function ($query) {
                $query->where('commercial_category', $this->selectedCommercialCategory);
            })
            ->when($this->selectedPriority, function ($query) {
                $query->where('priority', $this->selectedPriority);
            })
            ->tap(fn ($query) => $this->sortBy ? $query->orderBy($this->sortBy, $this->sortDirection) : $query)
            ->paginate($this->itemsPerPage);
    }

    public function sort($column)
    {
        if ($this->sortBy === $column) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $column;
            $this->sortDirection = 'asc';
        }
    }

    public function updatedSearch()
    {
        $this->resetPage();
        $this->sortBy = 'id';
        $this->sortDirection = 'desc';
    }

    public function updatedSelectedType()
    {
        $this->resetPage();
    }

    public function updatedSelectedCommercialCategory()
    {
        $this->resetPage();
    }

    public function updatedSelectedPriority()
    {
        $this->resetPage();
    }

    public function view($id): void
    {
        $this->redirectRoute('clients.show', ['client' => $id], navigate: true);
    }

    public function edit($id): void
    {
        $this->redirectRoute('clients.edit', ['client' => $id], navigate: true);
    }

    public function delete($id): void
    {
        $client = Client::findOrFail($id);

        // Check if the client is associated to any orders
        if ($client->orders()->count() > 0) {
            Flux::toast(
                variant: 'danger',
                text: 'You cannot delete this CLIENT because it is associated to one or more orders.'
            );
        } else {
            $client->delete();

            Flux::toast(
                variant: 'success',
                text: 'The CLIENT has been deleted.'
            );
        }
    }

    public function areFiltersActive(): bool
    {
        return ! empty($this->search) ||
               ! empty($this->selectedType) ||
               ! empty($this->selectedCommercialCategory) ||
               ! empty($this->selectedPriority);
    }

    public function clearSearchAndFilters(): void
    {
        $this->search = '';
        $this->selectedType = null;
        $this->selectedCommercialCategory = null;
        $this->selectedPriority = null;
        $this->resetPage();
    }
}
