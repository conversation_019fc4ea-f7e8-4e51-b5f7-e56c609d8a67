<?php

namespace App\Livewire\Pages\Clients;

use Flux\Flux;
use App\Models\User;
use App\Models\Partner;
use Livewire\Component;
use App\Enums\ClientTypes;
use App\Models\PaymentTerm;
use App\Enums\DeliveryTerms;
use App\Livewire\Forms\ClientForm;
use App\Enums\CommercialCategories;
use App\Enums\ClientPartnerPriorities;
use PrinsFrank\Standards\Country\CountryAlpha3;

class Create extends Component
{
    public ClientForm $form;
    
    public function render()
    {
        return view('livewire.pages.clients.create', [
            'partners' => Partner::orderBy('id', 'desc')->get(),
            'internalReferents' => User::query()->where('is_employee', TRUE)->orderBy('id', 'desc')->get(),
            'areaManagers' => User::query()->where('is_employee', TRUE)->orderBy('id', 'desc')->get(),
            'clientTypes' => ClientTypes::cases(),
            'commercialCategories' => CommercialCategories::cases(),
            'deliveryTerms' => DeliveryTerms::cases(),
            'clientPriorities' => ClientPartnerPriorities::cases(),
            'countries' => CountryAlpha3::cases(),
            'countries_of_expertise' => CountryAlpha3::cases(),
            'paymentTerms' => PaymentTerm::orderBy('id', 'desc')->get(),
        ]);
    }

    public function save(): void
    {
        $this->form->store();

        Flux::toast(
            variant: 'success',
            text: 'The CLIENT has been created.'
        );

        $this->redirectRoute('clients.index', navigate: true);
    }
}
