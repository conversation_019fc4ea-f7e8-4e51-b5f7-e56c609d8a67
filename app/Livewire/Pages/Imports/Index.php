<?php

namespace App\Livewire\Pages\Imports;

use App\Models\Import;
use Livewire\Component;
use Illuminate\View\View;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;

    /**
     * Search term for filtering imports
     */
    public string $search = '';

    /**
     * Modal state for showing errors
     */
    public bool $showErrorModal = false;

    /**
     * Current import for error display
     */
    public ?Import $selectedImport = null;

    /**
     * Get imports with pagination and search
     */
    public function getImportsProperty()
    {
        return Import::with(['user', 'errors'])
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('type', 'ilike', '%' . $this->search . '%')
                      ->orWhere('file_path', 'ilike', '%' . $this->search . '%')
                      ->orWhereHas('user', function ($userQuery) {
                          $userQuery->where('name', 'ilike', '%' . $this->search . '%');
                      });
                });
            })
            ->orderBy('created_at', 'desc')
            ->paginate(10);
    }

    /**
     * Get active imports count
     */
    public function getActiveImportsCountProperty(): int
    {
        return Import::active()->count();
    }

    /**
     * Clear search
     */
    public function clearSearch(): void
    {
        $this->search = '';
        $this->resetPage();
    }

    /**
     * Updated search property
     */
    public function updatedSearch(): void
    {
        $this->resetPage();
    }

    /**
     * Show errors modal for an import
     */
    public function showErrors(int $importId): void
    {
        $this->selectedImport = Import::with('errors')->find($importId);
        $this->showErrorModal = true;
    }

    /**
     * Close errors modal
     */
    public function closeErrorModal(): void
    {
        $this->showErrorModal = false;
        $this->selectedImport = null;
    }

    /**
     * Render the component
     */
    public function render(): View
    {
        return view('livewire.pages.imports.index', [
            'imports' => $this->imports,
            'activeImportsCount' => $this->activeImportsCount,
        ]);
    }
}
