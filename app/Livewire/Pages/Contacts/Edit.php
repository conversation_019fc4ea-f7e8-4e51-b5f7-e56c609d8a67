<?php

namespace App\Livewire\Pages\Contacts;

use Flux\Flux;
use App\Models\Contact;
use Livewire\Component;
use App\Enums\ContactPositions;
use App\Livewire\Forms\ContactForm;
use PrinsFrank\Standards\Country\CountryAlpha3;
use PrinsFrank\Standards\Language\LanguageAlpha2;

class Edit extends Component
{
    public Contact $contact;
    public ContactForm $form;

    public function mount(Contact $contact): void
    {
        $this->contact = $contact;
        $this->form->setContact($this->contact);
    }

    public function render()
    {
        return view('livewire.pages.contacts.edit', [
            'contact' => $this->contact,
            'departments' => ContactPositions::cases(),
            'countries' => CountryAlpha3::cases(),
            'languages' => LanguageAlpha2::cases(),
        ]);
    }

    public function save(): void
    {
        $this->form->update();

        Flux::toast(
            variant: 'success',
            text: 'The CONTACT has been updated.'
        );

        $this->redirectRoute('contacts.index', navigate: true);
    }
}
