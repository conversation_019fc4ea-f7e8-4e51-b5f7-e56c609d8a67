<?php

namespace App\Livewire\Pages\Contacts;

use App\Enums\ContactPositions;
use Flux\Flux;
use Livewire\Component;
use App\Livewire\Forms\ContactForm;
use PrinsFrank\Standards\Country\CountryAlpha3;
use PrinsFrank\Standards\Language\LanguageAlpha2;

class Create extends Component
{
    public ContactForm $form;

    public function render()
    {
        return view('livewire.pages.contacts.create', [
            'departments' => ContactPositions::cases(),
            'countries' => CountryAlpha3::cases(),
            'languages' => LanguageAlpha2::cases(),
        ]);
    }

    public function save(): void
    {
        $this->form->store();

        Flux::toast(
            variant: 'success',
            text: 'The CONTACT has been created.'
        );

        $this->redirectRoute('contacts.index', navigate: true);
    }
}
