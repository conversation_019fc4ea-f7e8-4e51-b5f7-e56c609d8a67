<?php

namespace App\Livewire\Pages\Contacts;

use Flux\Flux;
use App\Models\Contact;
use Livewire\Component;
use Illuminate\View\View;
use Livewire\WithPagination;
use App\Enums\ContactPositions;
use App\Livewire\Forms\ContactForm;
use PrinsFrank\Standards\Country\CountryAlpha3;
use PrinsFrank\Standards\Language\LanguageAlpha2;

class Show extends Component
{
    use WithPagination;
    
    public Contact $contact;
    public ContactForm $form;

    public function mount(): void
    {
        $this->form->setContact($this->contact);
    }

    public function render(): View
    {
        return view('livewire.pages.contacts.show', [
            'departments' => ContactPositions::cases(),
            'countries' => CountryAlpha3::cases(),
            'languages' => LanguageAlpha2::cases(),
        ]);
    }

    public function edit($id): void
    {
        $this->redirectRoute('contacts.edit', ['contact' => $id], navigate: true);
    }

    public function delete($id): void
    {
        Contact::findOrFail($id)->delete();
        Flux::toast(
            variant: 'success',
            text: 'The CONTACT has been deleted.'
        );

        $this->redirectRoute('contacts.index', navigate: true);
    }
}
