<?php

namespace App\Livewire\Pages\Tickets;

use App\Enums\TicketStatus;
use App\Models\Brand;
use App\Models\Client;
use App\Models\Collection;
use App\Models\Contact;
use App\Models\DiscountGroup;
use App\Models\Order\Order;
use App\Models\Partner;
use App\Models\Product;
use App\Models\Project\Project;
use App\Models\Supplier;
use App\Models\Ticket;
use App\Models\User;
use Flux\Flux;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithPagination;
use Spatie\Permission\Models\Role;

class Index extends Component
{
    use WithPagination;

    public $search = '';

    public $itemsPerPage = 10;

    public $sortBy = 'created_at';

    public $sortDirection = 'desc';

    public $statuses;

    public $selectedStatus;

    public $modelTypes = [
        Brand::class => 'Brands',
        Client::class => 'Clients',
        Collection::class => 'Collections',
        Contact::class => 'Contacts',
        DiscountGroup::class => 'Discount Groups',
        Order::class => 'Orders',
        Partner::class => 'Partners',
        Project::class => 'Projects',
        Supplier::class => 'Suppliers',
        Role::class => 'Roles',
        User::class => 'Users',
        Ticket::class => 'Tickets',
        Product::class => 'Products',
    ];

    public $selectedModelType;

    public $dateRanges = [
        'today' => 'Today',
        'last_week' => 'Last Week',
        'last_month' => 'Last Month',
    ];

    public $selectedDateRange;

    public $selectedTicket = null;

    public $selectedTicketModalStatus = null;

    public function mount()
    {
        $this->statuses = TicketStatus::cases();
        $this->selectedStatus = null;
    }

    public function render()
    {
        return view('livewire.pages.tickets.index', [
            'statuses' => $this->statuses,
            'modelTypes' => $this->modelTypes,
            'dateRanges' => $this->dateRanges,
        ]);
    }

    #[On('refresh-tickets')]
    #[Computed]
    public function tickets()
    {
        return Ticket::query()
            ->with(['user'])
            ->when($this->search, function ($query) {
                $query->where(function ($subQuery) {
                    $subQuery->where('tickets.title', 'ilike', '%'.$this->search.'%')
                             ->orWhere('tickets.description', 'ilike', '%'.$this->search.'%')
                             ->orWhereHas('user', function ($userQuery) {
                                 $userQuery->where('email', 'ilike', '%'.$this->search.'%')
                                          ->orWhere('first_name', 'ilike', '%'.$this->search.'%')
                                          ->orWhere('last_name', 'ilike', '%'.$this->search.'%');
                             });
                });
            })
            ->when($this->selectedStatus, function ($query) {
                $query->where('tickets.status', $this->selectedStatus);
            })
            ->when($this->selectedModelType, function ($query) {
                $query->where('tickets.ticketable_type', $this->selectedModelType);
            })
            ->when($this->selectedDateRange, function ($query) {
                switch ($this->selectedDateRange) {
                    case 'today':
                        $query->whereDate('tickets.created_at', today());
                        break;
                    case 'last_week':
                        $query->whereBetween('tickets.created_at', [now()->subWeek(), now()]);
                        break;
                    case 'last_month':
                        $query->whereBetween('tickets.created_at', [now()->subMonth(), now()]);
                        break;
                }
            })
            ->tap(fn ($query) => $this->sortBy ? $query->orderBy($this->sortBy, $this->sortDirection) : $query)
            ->paginate($this->itemsPerPage);
    }

    public function sort($column)
    {
        if ($this->sortBy === $column) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $column;
            $this->sortDirection = 'asc';
        }
    }

    public function updatedSearch()
    {
        $this->resetPage();
        $this->sortBy = 'created_at';
        $this->sortDirection = 'desc';
    }

    public function updatedSelectedStatus()
    {
        $this->resetPage();
    }

    public function updatedSelectedModelType()
    {
        $this->resetPage();
    }

    public function updatedSelectedDateRange()
    {
        $this->resetPage();
    }

    public function delete($id): void
    {
        Ticket::findOrFail($id)->delete();

        Flux::toast(
            variant: 'success',
            text: 'The Ticket has been deleted.'
        );
    }

    public function setStatus($id, $status): void
    {
        $ticket = Ticket::findOrFail($id);
        $ticket->status = $status;
        $ticket->save();

        Flux::toast(
            variant: 'success',
            text: 'The Ticket status has been updated.'
        );

        $this->dispatch('update-open-tickets');
    }

    public function selectTicket($ticketId)
    {
        $this->selectedTicket = $this->tickets->find($ticketId);
        $this->selectedTicketModalStatus = $this->selectedTicket ? $this->selectedTicket->status : null;
    }

    public function areFiltersActive(): bool
    {
        return ! empty($this->search) ||
               ! empty($this->selectedStatus) ||
               ! empty($this->selectedModelType) ||
               ! empty($this->selectedDateRange);
    }

    public function clearSearchAndFilters(): void
    {
        $this->search = '';
        $this->selectedStatus = null;
        $this->selectedModelType = null;
        $this->selectedDateRange = null;
        $this->resetPage();
    }

    public function clearSelectedTicket(): void
    {
        $this->selectedTicket = null;
        $this->selectedTicketModalStatus = null;
    }
}
