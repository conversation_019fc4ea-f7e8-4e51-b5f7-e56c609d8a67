<?php

namespace App\Livewire\Pages\Collections;

use Flux\Flux;
use Livewire\Component;
use Livewire\WithFileUploads;
use App\Livewire\Forms\CollectionForm;

class Create extends Component
{
    use WithFileUploads;

    public CollectionForm $form;

    public function render()
    {
        return view('livewire.pages.collections.create');
    }

    public function save(): void
    {
        $this->form->store();

        Flux::toast(
            variant: 'success',
            text: 'The COLLECTION has been created.'
        );

        $this->redirectRoute('collections.index', navigate: true);
    }

    public function unsetImage(): void
    {
        $this->form->image = null;
    }
}
