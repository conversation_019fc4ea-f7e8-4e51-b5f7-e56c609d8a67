<?php

namespace App\Livewire\Pages\Collections;

use Flux\Flux;
use Livewire\Component;
use App\Models\Collection;
use Livewire\WithFileUploads;
use App\Livewire\Forms\CollectionForm;

class Edit extends Component
{
    use WithFileUploads;

    public CollectionForm $form;
    public Collection $collection;
    
    public function mount(Collection $collection)
    {
        $this->form->setCollection($collection);
    }

    public function render()
    {
        return view('livewire.pages.collections.edit');
    }

    public function update(): void
    {
        $this->form->update();

        Flux::toast(
            variant: 'success',
            text: 'The COLLECTION has been updated.'
        );

        $this->redirectRoute('collections.index', navigate: true);
    }

    public function unsetImage(): void
    {
        $this->form->image = null;
    }
}
