<?php

namespace App\Livewire\Pages\Collections;

use Flux\Flux;
use Livewire\Component;
use App\Models\Collection;
use App\Livewire\Forms\CollectionForm;

class Show extends Component
{
    public CollectionForm $form;
    public Collection $collection;

    public function mount()
    {
        $this->form->setCollection($this->collection);
    }

    public function render()
    {
        return view('livewire.pages.collections.show');
    }

    public function edit($id)
    {
        $this->redirectRoute('collections.edit', ['collection' => $id], navigate: true);
    }

    public function delete($id)
    {
        Collection::findOrFail($id)->delete();
        Flux::toast(
            variant: 'success',
            text: 'The COLLECTION has been deleted.'
        );

        $this->redirectRoute('collections.index', navigate: true);
    }
}
