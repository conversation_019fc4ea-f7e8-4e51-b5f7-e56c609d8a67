<?php

namespace App\Livewire\Pages\Roles;

use App\Enums\ModelTypes;
use App\Livewire\Forms\RoleForm;
use Flux\Flux;
use Livewire\Component;
use Illuminate\View\View;
use Livewire\WithPagination;
use Spatie\Permission\Models\Role;

class Show extends Component
{
    use WithPagination;

    public Role $role;
    public RoleForm $form;

    public function mount(): void
    {
        $this->form->permissions = ModelTypes::mapPermissions();
        $this->form->setRole($this->role);
    }

    public function render(): View
    {
        $modelTypeCases = ModelTypes::cases();
        $resources = collect($modelTypeCases)->map(function ($case) {
            return [
                'name' => $case->label(),
                'value' => $case->value,
                'attributes' => $case->matchAttributes(),
            ];
        });

        return view('livewire.pages.roles.show', [
            'resources' => $resources,
        ]);
    }

    public function edit($id): void
    {
        $this->redirectRoute('roles.edit', ['role' => $id], navigate: true);
    }

    public function delete($id): void
    {
        // Check if the role is assigned to any users
        if ($this->role->users()->count() > 0) {
            Flux::toast(
                variant: 'error',
                text: 'You cannot delete this ROLE because it is assigned to one or more users.'
            );
            return;
        }

        Role::findOrFail($id)->delete();
        Flux::toast(
            variant: 'success',
            text: 'The ROLE has been deleted.'
        );

        $this->redirectRoute('roles.index', navigate: true);
    }
}
