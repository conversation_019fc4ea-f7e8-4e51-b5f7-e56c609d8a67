<?php

namespace App\Livewire\Pages\Roles;

use App\Enums\ModelTypes;
use App\Livewire\Forms\RoleForm;
use Flux\Flux;
use Livewire\Component;

class Create extends Component
{
    public RoleForm $form;

    public function mount(): void
    {
        $this->form->permissions = ModelTypes::mapPermissions();
    }

    public function render()
    {
        $modelTypeCases = ModelTypes::cases();
        $resources = collect($modelTypeCases)->map(function ($case) {
            return [
                'name' => $case->label(),
                'value' => $case->value,
                'attributes' => $case->matchAttributes(),
            ];
        });

        return view('livewire.pages.roles.create', [
            'resources' => $resources,
        ]);
    }

    public function save(): void
    {
        $this->form->store();

        Flux::toast(
            variant: 'success',
            text: 'The ROLE has been created.'
        );

        $this->redirectRoute('roles.index', navigate: true);
    }
}
