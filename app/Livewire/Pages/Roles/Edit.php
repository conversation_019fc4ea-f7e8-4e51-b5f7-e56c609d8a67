<?php

namespace App\Livewire\Pages\Roles;

use App\Enums\ModelTypes;
use App\Livewire\Forms\RoleForm;
use Flux\Flux;
use Livewire\Component;
use Spatie\Permission\Models\Role;

class Edit extends Component
{
    public Role $role;
    public RoleForm $form;

    public function mount(): void
    {
        $this->form->permissions = ModelTypes::mapPermissions();
        $this->form->setRole($this->role);
    }

    public function render()
    {
        $modelTypeCases = ModelTypes::cases();
        $resources = collect($modelTypeCases)->map(function ($case) {
            return [
                'name' => $case->label(),
                'value' => $case->value,
                'attributes' => $case->matchAttributes(),
            ];
        });

        return view('livewire.pages.roles.edit', [
            'resources' => $resources,
        ]);
    }

    public function save(): void
    {
        $this->form->update();

        Flux::toast(
            variant: 'success',
            text: 'The ROLE has been updated.'
        );

        $this->redirectRoute('roles.index', navigate: true);
    }
}
