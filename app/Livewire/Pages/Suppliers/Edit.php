<?php

namespace App\Livewire\Pages\Suppliers;

use Flux\Flux;
use Spatie\Tags\Tag;
use Livewire\Component;
use App\Models\Supplier;
use App\Enums\SupplierTypes;
use App\Enums\BrandPriceRange;
use App\Livewire\Forms\SupplierForm;

class Edit extends Component
{
    public Supplier $supplier;
    public SupplierForm $form;

    public $selectedMaterial;
    public $selectedProductType;
    public $selectedDestinationRoom;

    public function mount(): void
    {
        $this->form->setSupplier($this->supplier);
    }

    public function render()
    {
        $filteredMaterials = Tag::where('type', 'material')
            ->whereNotIn('name->en', $this->form->materials ?? [])
            ->pluck('name')->toArray();

        $filteredProductTypes = Tag::where('type', 'product_type')
            ->whereNotIn('name->en', $this->form->productTypes ?? [])
            ->pluck('name')->toArray();

        $filteredDestinationRooms = Tag::where('type', 'destination_room')
            ->whereNotIn('name->en', $this->form->destinationRooms ?? [])
            ->pluck('name')->toArray();
        
        return view('livewire.pages.suppliers.edit', [
            'priceRanges' => BrandPriceRange::cases(),
            'types' => SupplierTypes::cases(),
            'materials' => $filteredMaterials,
            'productTypes' => $filteredProductTypes,
            'destinationRooms' => $filteredDestinationRooms,
        ]);
    }

    public function save(): void
    {
        $this->form->update();

        Flux::toast(
            variant: 'success',
            text: 'The SUPPLIER has been updated.'
        );

        $this->redirectRoute('suppliers.index', navigate: true);
    }

    public function addMaterial()
    {
        $this->resetValidation();
        $this->validate([
            'selectedMaterial' => 'required|string|max:255',
        ]);
        
        $this->form->materials[] = $this->selectedMaterial;

        $this->selectedMaterial = null;
    }

    public function removeMaterial(string $material)
    {
        $index = array_search($material, $this->form->materials);
        unset($this->form->materials[$index]);
        $this->form->materials = array_values($this->form->materials);
    }

    public function addProductType()
    {
        $this->resetValidation();
        $this->validate([
            'selectedProductType' => 'required|string|max:255',
        ]);
        
        $this->form->productTypes[] = $this->selectedProductType;

        $this->selectedProductType = null;
    }

    public function removeProductType(string $productType)
    {
        $index = array_search($productType, $this->form->productTypes);
        unset($this->form->productTypes[$index]);
        $this->form->productTypes = array_values($this->form->productTypes);
    }

    public function addDestinationRoom()
    {
        $this->resetValidation();
        $this->validate([
            'selectedDestinationRoom' => 'required|string|max:255',
        ]);
        
        $this->form->destinationRooms[] = $this->selectedDestinationRoom;

        $this->selectedDestinationRoom = null;
    }

    public function removeDestinationRoom(string $destinationRoom)
    {
        $index = array_search($destinationRoom, $this->form->destinationRooms);
        unset($this->form->destinationRooms[$index]);
        $this->form->destinationRooms = array_values($this->form->destinationRooms);
    }
}