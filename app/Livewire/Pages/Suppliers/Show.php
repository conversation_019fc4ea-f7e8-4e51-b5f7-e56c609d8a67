<?php

namespace App\Livewire\Pages\Suppliers;

use Flux\Flux;
use App\Models\Brand;
use Livewire\Component;
use App\Models\Supplier;
use Illuminate\View\View;
use App\Enums\SupplierTypes;
use Livewire\WithPagination;
use App\Enums\BrandPriceRange;
use App\Livewire\Forms\SupplierForm;

class Show extends Component
{
    use WithPagination;
    
    public Supplier $supplier;
    public SupplierForm $form;

    public function mount(): void
    {
        $this->form->setSupplier($this->supplier);
    }

    public function render(): View
    {
        return view('livewire.pages.suppliers.show', [
            'priceRanges' => BrandPriceRange::cases(),
            'types' => SupplierTypes::cases(),
        ]);
    }

    public function edit($id): void
    {
        $this->redirectRoute('suppliers.edit', ['supplier' => $id], navigate: true);
    }

    public function delete($id): void
    {
        Supplier::findOrFail($id)->delete();
        Flux::toast(
            variant: 'success',
            text: 'The SUPPLIER has been deleted.'
        );

        $this->redirectRoute('suppliers.index', navigate: true);
    }
}