<?php

namespace App\Livewire\Pages\DiscountGroups;

use Flux\Flux;
use App\Models\Brand;
use Livewire\Component;
use App\Models\DiscountGroup;
use App\Livewire\Forms\DiscountGroupForm;

class Show extends Component
{
    public DiscountGroup $discountGroup;
    public DiscountGroupForm $form;

    public function mount()
    {
        $this->form->setDiscountGroup($this->discountGroup);
        $this->form->description = $this->discountGroup->description;
    }

    public function render()
    {
        return view('livewire.pages.discount-groups.show', [
            'brands' => Brand::all(),
        ]);
    }

    public function edit($id)
    {
        $this->redirectRoute('discount-groups.edit', ['discountGroup' => $id], navigate: true);
    }

    public function delete($id)
    {
        // Check if the discount group is associated to any clients or partners
        if (DiscountGroup::findOrFail($id)->clients()->count() > 0 || DiscountGroup::findOrFail($id)->partners()->count() > 0) {
            Flux::toast(
                variant: 'danger',
                text: 'You cannot delete this DISCOUNT GROUP because it is associated to one or more clients or partners.'
            );
            return;
        }

        // Check if the discount group is associated to any products
        if (DiscountGroup::findOrFail($id)->products()->count() > 0) {
            Flux::toast(
                variant: 'danger',
                text: 'You cannot delete this DISCOUNT GROUP because it is associated to one or more products.'
            );
            return;
        }

        // Delete the discount group
        DiscountGroup::findOrFail($id)->delete();

        // Show success toast
        Flux::toast(
            variant: 'success',
            text: 'The DISCOUNT GROUP has been deleted.'
        );

        $this->redirectRoute('discount-groups.index', navigate: true);
    }
}
