<?php

namespace App\Livewire\Pages\DiscountGroups;

use Flux\Flux;
use App\Models\Brand;
use Livewire\Component;
use App\Models\DiscountGroup;
use App\Livewire\Forms\DiscountGroupForm;

class Edit extends Component
{
    public DiscountGroup $discountGroup;
    public DiscountGroupForm $form;

    public function mount(): void
    {
        $this->form->setDiscountGroup($this->discountGroup);
        $this->form->description = $this->discountGroup->description;
    }

    public function render()
    {
        return view('livewire.pages.discount-groups.edit', [
            'brands' => Brand::all(),
        ]);
    }

    public function save(): void
    {
        $this->form->update();

        Flux::toast(
            variant: 'success',
            text: 'The DISCOUNT GROUP has been updated.'
        );

        $this->redirectRoute('discount-groups.index', navigate: true);
    }
}
