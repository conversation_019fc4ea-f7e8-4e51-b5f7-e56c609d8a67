<?php

namespace App\Livewire\Pages\DiscountGroups;

use Flux\Flux;
use App\Models\Brand;
use Livewire\Component;
use App\Livewire\Forms\DiscountGroupForm;

class Create extends Component
{
    public DiscountGroupForm $form;

    public function render()
    {
        return view('livewire.pages.discount-groups.create', [
            'brands' => Brand::all(),
        ]);
    }

    public function save(): void
    {
        $this->form->store();

        Flux::toast(
            variant: 'success',
            text: 'The DISCOUNT GROUP has been created.'
        );

        $this->redirectRoute('discount-groups.index', navigate: true);
    }
}
