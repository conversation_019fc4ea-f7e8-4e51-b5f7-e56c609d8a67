<?php

namespace App\Livewire\Pages\Projects;

use Flux\Flux;
use Carbon\Carbon;
use App\Models\User;
use App\Models\Client;
use App\Models\Address;
use App\Models\Partner;
use Livewire\Component;
use App\Enums\AddressTypes;
use App\Enums\ProjectTypes;
use App\Models\PaymentTerm;
use App\Models\Project\Project;
use App\Enums\ProjectCodeRegions;
use App\Livewire\Forms\ProjectForm;

class Create extends Component
{
    public ProjectForm $form;

    public function mount()
    {
        $this->form->date = $this->form->date ? $this->form->date : Carbon::now()->format('Y-m-d');
        $this->form->project_code_date = $this->form->project_code_date ? $this->form->project_code_date : Carbon::now()->format('Y-m-d');
    }

    public function render()
    {
        $client = Client::find($this->form->client_id);

        return view('livewire.pages.projects.create', [
            'internalReferents' => User::query()->where('is_employee', TRUE)->orderBy('id', 'desc')->get(),
            'areaManagers' => User::query()->where('is_employee', TRUE)->orderBy('id', 'desc')->get(),
            'clients' => Client::orderBy('id', 'desc')->get(),
            'partners' => $this->form->client_id 
                ? Partner::where('id', $client?->partner_id)->get()
                : [],
            'invoicingAddresses' => $this->form->client_id 
                ? Address::where('addressable_id', $this->form->client_id)
                    ->where('addressable_type', Client::class)
                    ->where('type', AddressTypes::Invoicing->value)
                    ->orderBy('id', 'desc')
                    ->get()
                : [],
            'shippingAddresses' => $this->form->client_id 
                ? Address::where('addressable_id', $this->form->client_id)
                    ->where('addressable_type', Client::class)
                    ->where('type', AddressTypes::Shipping->value)
                    ->orderBy('id', 'desc')
                    ->get()
                : [],
            'paymentTerms' => PaymentTerm::orderBy('id', 'desc')->get(),
            'projectCodeRegions' => ProjectCodeRegions::cases(),
            'types' => ProjectTypes::cases(),
        ]);
    }

    public function save(): void
    {
        $this->form->store();

        Flux::toast(
            variant: 'success',
            text: 'The PROJECT has been created.'
        );

        $this->redirectRoute('projects.index', navigate: true);
    }

    public function updatedFormClientId(): void
    {
        $this->form->partner_id = null;
        $this->form->invoicing_address_id = null;
        $this->form->shipping_address_id = null;
    }

    public function updatedFormProjectCodeRegion(): void
    {
        $this->form->project_code_progressive = null;
        $this->form->project_code_progressive = $this->getNextProjectCodeProgressive($this->form->project_code_region);
    }

    public function updatedFormDate(): void
    {
        $this->form->project_code_date = $this->form->date;
        $this->form->project_code_progressive = $this->getNextProjectCodeProgressive($this->form->project_code_region);
    }

    /**
     * Get the next project code progressive
     */
    private function getNextProjectCodeProgressive($region): ?int
    {
        $year = Carbon::parse($this->form->date)->format('Y');

        // Set the project code progressive based on the project code region
        if ($this->form->date && $this->form->project_code_region) {
            return (Project::whereYear('date', $year)
                ->where('project_code_region', $region)
                ->max('project_code_progressive') ?? 0) + 1;
        }
        else {
            return null;
        }
    }
}
