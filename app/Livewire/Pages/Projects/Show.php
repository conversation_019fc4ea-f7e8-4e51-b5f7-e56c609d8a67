<?php

namespace App\Livewire\Pages\Projects;

use Flux\Flux;
use App\Models\User;
use App\Models\Client;
use App\Models\Address;
use App\Models\Partner;
use Livewire\Component;
use App\Enums\AddressTypes;
use App\Enums\ProjectTypes;
use App\Models\PaymentTerm;
use App\Models\Project\Project;
use App\Enums\ProjectCodeRegions;
use App\Livewire\Forms\ProjectForm;

class Show extends Component
{
    public Project $project;
    public ProjectForm $form;

    public function mount(): void
    {
        $this->form->setProject($this->project);
    }

    public function render()
    {
        $client = Client::find($this->form->client_id);

        return view('livewire.pages.projects.show', [
            'internalReferents' => User::query()->where('is_employee', TRUE)->orderBy('id', 'desc')->get(),
            'areaManagers' => User::query()->where('is_employee', TRUE)->orderBy('id', 'desc')->get(),
            'clients' => Client::orderBy('id', 'desc')->get(),
            'partners' => $this->form->client_id 
                ? Partner::where('id', $client?->partner_id)->get()
                : [],
            'invoicingAddresses' => $this->form->client_id 
                ? Address::where('addressable_id', $this->form->client_id)
                    ->where('addressable_type', Client::class)
                    ->where('type', AddressTypes::Invoicing->value)
                    ->orderBy('id', 'desc')
                    ->get()
                : [],
            'shippingAddresses' => $this->form->client_id 
                ? Address::where('addressable_id', $this->form->client_id)
                    ->where('addressable_type', Client::class)
                    ->where('type', AddressTypes::Shipping->value)
                    ->orderBy('id', 'desc')
                    ->get()
                : [],
            'paymentTerms' => PaymentTerm::orderBy('id', 'desc')->get(),
            'projectCodeRegions' => ProjectCodeRegions::cases(),
            'types' => ProjectTypes::cases(),
        ]);
    }

    public function edit($id): void
    {
        $this->redirectRoute('projects.edit', ['project' => $id], navigate: true);
    }

    public function delete($id): void
    {
        Project::findOrFail($id)->delete();
        
        Flux::toast(
            variant: 'success',
            text: 'The PROJECT has been deleted.'
        );

        $this->redirectRoute('projects.index', navigate: true);
    }
}
