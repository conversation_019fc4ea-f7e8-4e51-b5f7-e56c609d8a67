<?php

namespace App\Livewire\Pages\Projects;

use App\Enums\ProjectStatuses;
use App\Enums\ProjectTypes;
use App\Models\Project\Project;
use Flux\Flux;
use Livewire\Attributes\Computed;
use Livewire\Component;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;

    public $search = '';

    public $itemsPerPage = 10;

    public $sortBy = 'id';

    public $sortDirection = 'desc';

    // Filtri
    public $selectedStatus = null;

    public $selectedProjectType = null;

    public $hasOrders = null;

    public $hasGraphicPresentations = null;

    public function render()
    {
        return view('livewire.pages.projects.index', [
            'statuses' => ProjectStatuses::cases(),
            'projectTypes' => ProjectTypes::cases(),
            'ordersOptions' => [
                '1' => 'Yes',
                '0' => 'No',
            ],
            'graphicPresentationsOptions' => [
                '1' => 'Yes',
                '0' => 'No',
            ],
        ]);
    }

    #[Computed]
    public function projects()
    {
        return Project::query()
            ->when($this->search, function ($query) {
                $query->where(function ($subQuery) {
                    $subQuery->where('name', 'ilike', '%'.$this->search.'%')
                        ->orWhere('project_code', 'ilike', '%'.$this->search.'%')
                        ->orWhere('project_code_progressive', 'ilike', '%'.$this->search.'%')
                        ->orWhere('project_code_date', 'ilike', '%'.$this->search.'%')
                        ->orWhere('project_code_region', 'ilike', '%'.$this->search.'%')
                        ->orWhere('description', 'ilike', '%'.$this->search.'%')
                        ->orWhereHas('internalReferent', function ($query) {
                            $query->where('first_name', 'ilike', '%'.$this->search.'%')
                                ->orWhere('last_name', 'ilike', '%'.$this->search.'%');
                        })
                        ->orWhereHas('client', function ($query) {
                            $query->where('company', 'ilike', '%'.$this->search.'%');
                        })
                        ->orWhereHas('partner', function ($query) {
                            $query->where('company', 'ilike', '%'.$this->search.'%');
                        });
                });
            })
            ->when($this->selectedStatus, function ($query) {
                $query->where('status', $this->selectedStatus);
            })
            ->when($this->selectedProjectType, function ($query) {
                $query->where('type', $this->selectedProjectType);
            })
            ->when($this->hasOrders !== null && $this->hasOrders !== '', function ($query) {
                if ($this->hasOrders === '1') {
                    $query->whereHas('orders');
                } else {
                    $query->whereDoesntHave('orders');
                }
            })
            ->when($this->hasGraphicPresentations !== null && $this->hasGraphicPresentations !== '', function ($query) {
                if ($this->hasGraphicPresentations === '1') {
                    $query->whereHas('graphicPresentations');
                } else {
                    $query->whereDoesntHave('graphicPresentations');
                }
            })
            ->tap(fn ($query) => $this->sortBy ? $query->orderBy($this->sortBy, $this->sortDirection) : $query)
            ->paginate($this->itemsPerPage);
    }

    public function sort($column)
    {
        if ($this->sortBy === $column) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $column;
            $this->sortDirection = 'asc';
        }
    }

    public function updatedSearch()
    {
        $this->resetPage();
        $this->sortBy = 'id';
        $this->sortDirection = 'desc';
    }

    public function updatedSelectedStatus()
    {
        $this->resetPage();
    }

    public function updatedSelectedProjectType()
    {
        $this->resetPage();
    }

    public function updatedHasOrders()
    {
        $this->resetPage();
    }

    public function updatedHasGraphicPresentations()
    {
        $this->resetPage();
    }

    public function view($id): void
    {
        $this->redirectRoute('projects.show', ['project' => $id], navigate: true);
    }

    public function edit($id): void
    {
        $this->redirectRoute('projects.edit', ['project' => $id], navigate: true);
    }

    public function delete($id): void
    {
        Project::findOrFail($id)->delete();

        Flux::toast(
            variant: 'success',
            text: 'The PROJECT has been deleted.'
        );
    }

    public function areFiltersActive(): bool
    {
        return ! empty($this->search) ||
               ! empty($this->selectedStatus) ||
               ! empty($this->selectedProjectType) ||
               ($this->hasOrders !== null && $this->hasOrders !== '') ||
               ($this->hasGraphicPresentations !== null && $this->hasGraphicPresentations !== '');
    }

    public function clearSearchAndFilters(): void
    {
        $this->search = '';
        $this->selectedStatus = null;
        $this->selectedProjectType = null;
        $this->hasOrders = null;
        $this->hasGraphicPresentations = null;
        $this->resetPage();
    }
}
