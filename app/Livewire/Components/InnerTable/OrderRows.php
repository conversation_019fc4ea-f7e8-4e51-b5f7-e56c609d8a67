<?php

namespace App\Livewire\Components\InnerTable;

use Flux\Flux;
use Livewire\Component;
use Illuminate\Support\Str;
use Livewire\WithPagination;
use Livewire\Attributes\Lazy;
use App\Models\Order\OrderRow;
use Livewire\Attributes\Computed;
use App\Livewire\Forms\OrderRowForm;

#[Lazy]
class OrderRows extends Component
{
    use WithPagination;

    public ?OrderRowForm $form;

    public $resourceType;
    public $resourceNameSingular;
    public $resourceNamePlural;
    public $resourceValue;

    public $selectedGroup;

    public $itemsPerPage = 25; // Increased default to reduce pagination requests

    public function mount($resourceType, $resourceValue): void
    {
        $this->resourceType = $resourceType;
        $this->resourceNameSingular = strtolower($resourceType);
        $this->resourceNamePlural = Str::plural(strtolower($resourceType));
        $this->resourceValue = $resourceValue;
    }

    public function render()
    {
        return view('livewire.components.inner-table.order-rows', [
            'groups' => $this->resourceValue->cartGroups,
        ]);
    }

    public function placeholder()
    {
        return view('components.inner-table.lazy-placeholder');
    }

    #[Computed]
    public function orderRows()
    {
        // Optimize query with comprehensive eager loading to prevent N+1 queries
        $query = $this->resourceValue->orderRows()
            ->with([
                'product' => function($query) {
                    $query->withTrashed()
                        ->with([
                            'brand:id,name', // Only load necessary fields
                            'collection:id,name', // Only load necessary fields
                            'modules:id,product_id,name' // Only load necessary fields
                        ]);
                },
                'customProduct:id,sku,description,brand_name,image', // Only load necessary fields
                'cartGroup:id,name,order_id' // Add cart group relation with order_id for potential future use
            ]);

        if ($this->selectedGroup) {
            $query->where('cart_group_id', $this->selectedGroup);
        }

        return $query->orderBy('sort')->paginate($this->itemsPerPage, pageName: 'order-rows-page');
    }

    public function view($id): void
    {
        $asset = OrderRow::findOrFail($id);

        $this->form->setOrderRow($asset);

        Flux::modal('view-order-row')->show();
    }

    public function edit($id): void
    {
        $asset = OrderRow::findOrFail($id);

        $this->form->setOrderRow($asset);

        Flux::modal('edit-order-row')->show();
    }

    public function update(): void
    {
        $this->form->update();

        Flux::toast(
            variant: 'success',
            text: 'The ORDER ROW has been updated.'
        );
        
        Flux::modals()->close();
        $this->resetPage();
    }

    public function resetForm(): void
    {
        $this->form->reset();
        $this->resetValidation();
    }
}
