<?php

namespace App\Livewire\Components\InnerTable;

use App\Models\Supplier;
use Flux\Flux;
use Illuminate\Support\Str;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Lazy;
use Livewire\Component;
use Livewire\WithPagination;

#[Lazy]
class Suppliers extends Component
{
    use WithPagination;

    public $resourceType;

    public $resourceNameSingular;

    public $resourceNamePlural;

    public $resourceValue;

    public $search = '';

    public function mount($resourceType, $resourceValue): void
    {
        $this->resourceType = $resourceType;
        $this->resourceNameSingular = strtolower($resourceType);
        $this->resourceNamePlural = Str::plural(strtolower($resourceType));
        $this->resourceValue = $resourceValue;
    }

    public function render()
    {
        return view('livewire.components.inner-table.suppliers', [
            'suppliers' => Supplier::whereDoesntHave($this->resourceNamePlural, function ($query) {
                $query->where($this->resourceNameSingular.'_id', $this->resourceValue->id);
            })->get(),
        ]);
    }

    public function placeholder()
    {
        return view('components.inner-table.lazy-placeholder');
    }

    public function updatedSearch(): void
    {
        $this->resetPage();
    }

    #[Computed]
    public function suppliers()
    {
        return $this->resourceValue->suppliers()
            ->when($this->search, function ($query) {
                $searchTerm = '%'.$this->search.'%';
                $query->where(function ($q) use ($searchTerm) {
                    $q->where('code', 'ilike', $searchTerm)
                        ->orWhere('name', 'ilike', $searchTerm);
                });
            })
            ->orderBy('id', 'desc')
            ->paginate(5, pageName: 'suppliers-page');
    }

    public function attach($id): void
    {
        $this->resourceValue->suppliers()->attach($id);

        Flux::toast(
            variant: 'success',
            text: 'The SUPPLIER has been attached.'
        );

        Flux::modals()->close();
        $this->resetPage();
    }

    public function detach($id): void
    {
        $this->resourceValue->suppliers()->detach($id);

        Flux::toast(
            variant: 'success',
            text: 'The SUPPLIER has been detached.'
        );

        $this->resetPage();
    }

    public function view($id): void
    {
        $this->redirectRoute('suppliers.show', ['supplier' => $id], navigate: true);
    }

    public function edit($id): void
    {
        $this->redirectRoute('suppliers.edit', ['supplier' => $id], navigate: true);
    }

    public function delete($id): void
    {
        Supplier::findOrFail($id)->delete();

        Flux::toast(
            variant: 'success',
            text: 'The SUPPLIER has been deleted.'
        );
    }
}
