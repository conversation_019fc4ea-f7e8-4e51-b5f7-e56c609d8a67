<?php

namespace App\Livewire\Components\InnerTable;

use App\Livewire\Forms\DiscountGroupForm;
use App\Models\DiscountGroup;
use Flux\Flux;
use Illuminate\Support\Str;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Lazy;
use Livewire\Component;
use Livewire\WithPagination;

#[Lazy]
class DiscountGroups extends Component
{
    use WithPagination;

    public ?DiscountGroupForm $form;

    public $resourceType;

    public $resourceNameSingular;

    public $resourceNamePlural;

    public $resourceValue;

    public $search = '';

    public function mount($resourceType, $resourceValue): void
    {
        $this->resourceType = $resourceType;
        $this->resourceNameSingular = strtolower($resourceType);
        $this->resourceNamePlural = Str::plural(strtolower($resourceType));
        $this->resourceValue = $resourceValue;
    }

    public function render()
    {
        return view('livewire.components.inner-table.discount-groups');
    }

    public function placeholder()
    {
        return view('components.inner-table.lazy-placeholder');
    }

    public function updatedSearch(): void
    {
        $this->resetPage();
    }

    #[Computed]
    public function discountGroups()
    {
        return $this->resourceValue->discountGroups()
            ->when($this->search, function ($query) {
                $searchTerm = '%'.$this->search.'%';
                $query->where(function ($q) use ($searchTerm) {
                    $q->where('code', 'ilike', $searchTerm)
                        ->orWhere('description', 'ilike', $searchTerm);
                });
            })
            ->orderBy('id', 'desc')
            ->paginate(5, pageName: 'discount-groups-page');
    }

    public function view($id): void
    {
        $this->redirectRoute('discount-groups.show', ['discountGroup' => $id], navigate: true);
    }

    public function edit($id): void
    {
        $this->redirectRoute('discount-groups.edit', ['discountGroup' => $id], navigate: true);
    }

    public function delete($id): void
    {
        // Check if the discount group is associated to any clients or partners
        if (DiscountGroup::findOrFail($id)->clients()->count() > 0 || DiscountGroup::findOrFail($id)->partners()->count() > 0) {
            Flux::toast(
                variant: 'danger',
                text: 'You cannot delete this DISCOUNT GROUP because it is associated to one or more clients or partners.'
            );
            return;
        }
        
        // Check if the discount group is associated to any products
        if (DiscountGroup::findOrFail($id)->products()->count() > 0) {
            Flux::toast(
                variant: 'danger',
                text: 'You cannot delete this DISCOUNT GROUP because it is associated to one or more products.'
            );
            return;
        }

        // Delete the discount group
        DiscountGroup::findOrFail($id)->delete();

        // Show success toast
        Flux::toast(
            variant: 'success',
            text: 'The DISCOUNT GROUP has been deleted.'
        );
    }

    public function resetForm(): void
    {
        $this->form->reset();
    }
}
