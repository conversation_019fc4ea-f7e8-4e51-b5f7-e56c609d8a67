<?php

namespace App\Livewire\Components\InnerTable;

use App\Enums\AssetStatuses;
use App\Enums\AssetTypes;
use App\Livewire\Forms\AssetForm;
use App\Models\Asset;
use Flux\Flux;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Livewire\Attributes\Computed;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\WithPagination;

class Meetings extends Component
{
    use WithFileUploads, WithPagination;

    public ?AssetForm $form;

    public $resourceType;

    public $resourceNameSingular;

    public $resourceNamePlural;

    public $resourceValue;

    public $search = '';

    public function mount($resourceType, $resourceValue): void
    {
        $this->resourceType = $resourceType;
        $this->resourceNameSingular = strtolower($resourceType);
        $this->resourceNamePlural = Str::plural(strtolower($resourceType));
        $this->resourceValue = $resourceValue;
    }

    public function render()
    {
        return view('livewire.components.inner-table.meetings');
    }

    public function placeholder()
    {
        return view('components.inner-table.lazy-placeholder');
    }

    public function updatedSearch(): void
    {
        $this->resetPage();
    }

    #[Computed]
    public function assets()
    {
        return $this->resourceValue->assets()
            ->where('type', AssetTypes::Meeting)
            ->when($this->search, function ($query) {
                $searchTerm = '%'.$this->search.'%';
                $query->where('name', 'ilike', $searchTerm);
            })
            ->orderBy('id', 'desc')
            ->paginate(5, pageName: 'meetings-page');
    }

    public function view($id): void
    {
        $asset = Asset::findOrFail($id);

        $this->form->setAsset($asset);

        Flux::modal('view-meeting')->show();
    }

    public function create(): void
    {
        $this->form->assetable_id = $this->resourceValue->id;
        $this->form->assetable_type = 'App\\Models\\'.$this->resourceType.'\\'.$this->resourceType;

        $this->form->type = AssetTypes::Meeting;
        $this->form->status = AssetStatuses::Open;
        $this->form->store();

        Flux::toast(
            variant: 'success',
            text: 'The MEETING has been created.'
        );

        Flux::modals()->close();
        $this->resetPage();
    }

    public function edit($id): void
    {
        $asset = Asset::findOrFail($id);

        $this->form->setAsset($asset);

        Flux::modal('edit-meeting')->show();
    }

    public function update(): void
    {
        $this->form->update();

        Flux::toast(
            variant: 'success',
            text: 'The MEETING has been updated.'
        );

        Flux::modals()->close();
        $this->resetPage();
    }

    public function delete($id): void
    {
        Asset::findOrFail($id)->delete();

        Flux::toast(
            variant: 'success',
            text: 'The MEETING has been deleted.'
        );
    }

    public function resetForm(): void
    {
        $this->form->reset();
        $this->resetValidation();
    }

    public function unsetFile(): void
    {
        $this->form->file = null;
    }

    public function downloadFile(Asset $asset)
    {
        $file = $asset->file;

        if (! Storage::disk(config('filesystems.private'))->exists($file ?? ' ')) {

            Flux::toast(
                variant: 'danger',
                text: 'The MEETING does not exist.'
            );

            return false;
        }

        $url = Storage::disk(config('filesystems.private'))
            ->temporaryUrl($file, now()->addMinutes(5));

        return response()->streamDownload(function () use ($url) {
            echo file_get_contents($url);
        }, basename($file));
    }
}
