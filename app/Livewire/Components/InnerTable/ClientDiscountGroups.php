<?php

namespace App\Livewire\Components\InnerTable;

use App\Models\DiscountGroup;
use Flux\Flux;
use Illuminate\Support\Str;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Lazy;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithPagination;

#[Lazy]
class ClientDiscountGroups extends Component
{
    use WithPagination;

    public $resourceType;

    public $resourceNameSingular;

    public $resourceNamePlural;

    public $resourceValue;

    public $selectedDiscountGroupId;

    public $selectedDiscountGroup;

    public $discount;

    public $search = '';

    public function mount($resourceType, $resourceValue): void
    {
        $this->resourceType = $resourceType;
        $this->resourceNameSingular = strtolower($resourceType);
        $this->resourceNamePlural = Str::plural(strtolower($resourceType));
        $this->resourceValue = $resourceValue;
    }

    public function render()
    {
        $discountGroupsQuery = DiscountGroup::whereHas('brand', function ($query) {
            $query->whereHas($this->resourceNamePlural, function ($query) {
                $query->where($this->resourceNameSingular.'_id', $this->resourceValue->id);
            });
        })->whereDoesntHave($this->resourceNamePlural, function ($query) {
            $query->where($this->resourceNameSingular.'_id', $this->resourceValue->id);
        });

        if ($this->resourceValue->partner) {
            $discountGroupsQuery->whereHas('partners', function ($query) {
                $query->where('partner_id', $this->resourceValue->partner->id);
            });
        }

        return view('livewire.components.inner-table.client-discount-groups', [
            'discountGroups' => $discountGroupsQuery->orderBy('code', 'asc')->get(),
        ]);
    }

    public function placeholder()
    {
        return view('components.inner-table.lazy-placeholder');
    }

    #[Computed]
    public function discountGroups()
    {
        return $this->resourceValue->discountGroups()
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $searchTerm = '%'.$this->search.'%';
                    $q->where('code', 'ilike', $searchTerm)
                        ->orWhere('description', 'ilike', $searchTerm)
                        ->orWhereHas('brand', function ($brandQuery) use ($searchTerm) {
                            $brandQuery->where('name', 'ilike', $searchTerm);
                        });
                });
            })
            ->orderBy('id', 'desc')
            ->paginate(5, pageName: 'client-discount-groups-page');
    }

    public function updatedSearch(): void
    {
        $this->resetPage();
    }

    #[On('refreshDiscountGroups')]
    public function refreshBrands()
    {
        $this->resetPage();
    }

    public function rules(): array
    {
        return [
            'selectedDiscountGroupId' => 'required|exists:discount_groups,id',
            'discount' => [
                'required',
                'numeric',
                'min:0',
                'max:100',
                function ($attribute, $value, $fail) {
                    if (! $this->selectedDiscountGroupId) {
                        return;
                    }

                    $selectedDiscountGroup = DiscountGroup::findOrFail($this->selectedDiscountGroupId);
                    $maxDiscount = $selectedDiscountGroup->discount;

                    if ($this->resourceValue->partner) {
                        $partnerDiscountGroup = $this->resourceValue->partner->discountGroups()
                            ->where('discount_group_id', $this->selectedDiscountGroupId)
                            ->first();

                        if ($partnerDiscountGroup) {
                            $maxDiscount = $partnerDiscountGroup->pivot->discount;
                        }
                    }

                    if ($value > $maxDiscount) {
                        $fail("The discount cannot be greater than the maximum allowed discount for this group. The maximum allowed discount is {$maxDiscount}%.");
                    }
                },
            ],
        ];
    }

    public function attachAllDiscountGroups(): void
    {
        // Check if current user has permission to write client discount groups
        if (! auth()->user()->can('write_client_discount_groups')) {
            Flux::toast(
                variant: 'error',
                text: 'You do not have permission to attach discount groups.'
            );

            return;
        }

        if ($this->resourceType === 'Client') {
            $existingDiscountGroupIds = $this->resourceValue->discountGroups->pluck('id')->toArray();
            $newDiscountGroupIds = DiscountGroup::whereHas('brand', function ($query) {
                $query->whereHas($this->resourceNamePlural, function ($query) {
                    $query->where($this->resourceNameSingular.'_id', $this->resourceValue->id);
                });
            })->pluck('id')->toArray();
            $discountGroupIdsToAttach = array_diff($newDiscountGroupIds, $existingDiscountGroupIds);

            $discountGroupsWithPivot = [];
            foreach ($discountGroupIdsToAttach as $id) {
                $discountGroupsWithPivot[$id] = ['discount' => 0];
            }

            $this->resourceValue->discountGroups()->attach($discountGroupsWithPivot);

            Flux::toast(
                variant: 'success',
                text: 'All DISCOUNT GROUPs attached to the '.strtoupper($this->resourceNameSingular).'.'
            );

            $this->dispatch('refreshDiscountGroups');
        } else {
            Flux::toast(
                variant: 'error',
                text: 'Something went wrong.'
            );
        }
    }

    public function attach(): void
    {
        // Check if current user has permission to write client discount groups
        if (! auth()->user()->can('write_client_discount_groups')) {
            Flux::toast(
                variant: 'error',
                text: 'You do not have permission to attach discount groups.'
            );

            return;
        }

        $this->validate();

        $this->resourceValue->discountGroups()->attach($this->selectedDiscountGroupId, [
            'discount' => $this->discount,
        ]);

        Flux::toast(
            variant: 'success',
            text: 'The DISCOUNT GROUP has been attached.'
        );

        Flux::modals()->close();
        $this->resetPage();
    }

    public function update(): void
    {
        $this->validate();

        $this->resourceValue->discountGroups()->updateExistingPivot($this->selectedDiscountGroupId, [
            'discount' => $this->discount,
        ]);

        Flux::toast(
            variant: 'success',
            text: 'The DISCOUNT GROUP has been updated.'
        );

        Flux::modals()->close();
        $this->resetPage();
    }

    public function detach($id): void
    {
        // Check if the discount group can be detached
        if (! $this->resourceValue->canDetachDiscountGroup($id)) {
            Flux::toast(
                variant: 'danger',
                text: 'Cannot detach this DISCOUNT GROUP because it has products used in active orders.'
            );

            return;
        }

        $this->resourceValue->discountGroups()->detach($id);

        Flux::toast(
            variant: 'success',
            text: 'The DISCOUNT GROUP has been detached.'
        );

        $this->resetPage();
    }

    public function view($id): void
    {
        $this->redirectRoute('discount-groups.show', ['discountGroup' => $id], navigate: true);
    }

    public function edit($id): void
    {
        $this->selectedDiscountGroupId = $id;
        $this->selectedDiscountGroup = DiscountGroup::findOrFail($id);
        $this->discount = $this->resourceValue->discountGroups()->where('discount_group_id', $id)->first()->pivot->discount;

        Flux::modal('edit-discount-group')->show();
    }

    public function delete($id): void
    {
        DiscountGroup::findOrFail($id)->delete();

        Flux::toast(
            variant: 'success',
            text: 'The DISCOUNT GROUP has been deleted.'
        );
    }

    public function resetForm(): void
    {
        $this->selectedDiscountGroupId = null;
        $this->discount = null;
        $this->resetValidation(['selectedDiscountGroupId', 'discount']);
    }
}
