<?php

namespace App\Livewire\Components\InnerTable;

use Flux\Flux;
use Illuminate\Support\Str;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Lazy;
use Livewire\Component;
use Livewire\WithPagination;

#[Lazy]
class Orders extends Component
{
    use WithPagination;

    public $resourceType;

    public $resourceNameSingular;

    public $resourceNamePlural;

    public $resourceValue;

    public $search = '';

    public function mount($resourceType, $resourceValue): void
    {
        $this->resourceType = $resourceType;
        $this->resourceNameSingular = strtolower($resourceType);
        $this->resourceNamePlural = Str::plural(strtolower($resourceType));
        $this->resourceValue = $resourceValue;
    }

    public function render()
    {
        return view('livewire.components.inner-table.orders');
    }

    public function placeholder()
    {
        return view('components.inner-table.lazy-placeholder');
    }

    public function updatedSearch(): void
    {
        $this->resetPage();
    }

    #[Computed]
    public function orders()
    {
        return $this->resourceValue->orders()
            ->when($this->search, function ($query) {
                $searchTerm = '%'.$this->search.'%';
                $query->where(function ($q) use ($searchTerm) {
                    $q->where('code', 'ilike', $searchTerm)
                        ->orWhere('description', 'ilike', $searchTerm)
                        ->orWhere('order_code', 'ilike', $searchTerm);
                });
            })
            ->orderBy('id', 'desc')
            ->paginate(5, pageName: 'orders-page');
    }

    public function view($id): void
    {
        $this->redirectRoute('orders.show', ['order' => $id], navigate: true);
    }

    public function edit($id): void
    {
        $this->redirectRoute('orders.edit', ['order' => $id], navigate: true);
    }

    public function delete($id): void
    {
        $this->resourceValue->orders()->findOrFail($id)->delete();

        Flux::toast(
            variant: 'success',
            text: 'The ORDER has been deleted.'
        );
    }
}
