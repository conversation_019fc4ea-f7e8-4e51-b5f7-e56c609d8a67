<?php

namespace App\Livewire\Components\InnerTable;

use App\Models\Brand;
use Flux\Flux;
use Illuminate\Support\Str;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Lazy;
use Livewire\Component;
use Livewire\WithPagination;

#[Lazy]
class PartnerBrands extends Component
{
    use WithPagination;

    public $resourceType;

    public $resourceNameSingular;

    public $resourceNamePlural;

    public $resourceValue;

    public $selectedBrandsIds = [];

    public $selectedBrands;

    public $discountValues = [];

    public $search = '';

    public function mount($resourceType, $resourceValue): void
    {
        $this->resourceType = $resourceType;
        $this->resourceNameSingular = strtolower($resourceType);
        $this->resourceNamePlural = Str::plural(strtolower($resourceType));
        $this->resourceValue = $resourceValue;
    }

    public function render()
    {
        $this->fetchSelectedBrands();

        return view('livewire.components.inner-table.partner-brands', [
            'brands' => Brand::whereDoesntHave($this->resourceNamePlural, function ($query) {
                $query->where($this->resourceNameSingular.'_id', $this->resourceValue->id);
            })
                ->orderBy('name', 'asc')
                ->get(),
        ]);
    }

    public function placeholder()
    {
        return view('components.inner-table.lazy-placeholder');
    }

    #[Computed]
    public function brands()
    {
        return $this->resourceValue->brands()
            ->when($this->search, function ($query) {
                $query->where('name', 'ilike', '%'.$this->search.'%');
            })
            ->orderBy('id', 'desc')
            ->paginate(5, pageName: 'partner-brands-page');
    }

    public function fetchSelectedBrands()
    {
        $query = Brand::query()
            ->whereIn('id', $this->selectedBrandsIds)
            ->orderBy('id', 'desc');

        $query->with(['discountGroups']);

        $this->selectedBrands = $query->get();
    }

    public function updatedSearch(): void
    {
        $this->resetPage();
    }

    public function resetForm(): void
    {
        $this->selectedBrandsIds = [];
        $this->selectedBrands = null;
        $this->discountValues = [];
        $this->resetValidation();
    }

    public function attachAllBrands(): void
    {
        // Check if current user has permission to write partner discount groups
        if (! auth()->user()->can('write_partner_discount_groups')) {
            Flux::toast(
                variant: 'error',
                text: 'You do not have permission to attach brands and discount groups.'
            );

            return;
        }

        if ($this->resourceType === 'Partner') {
            $existingBrandIds = $this->resourceValue->brands->pluck('id')->toArray();
            $newBrandIds = Brand::all()->pluck('id')->toArray();
            $brandIdsToAttach = array_diff($newBrandIds, $existingBrandIds);
            $this->resourceValue->brands()->attach($brandIdsToAttach);

            Flux::toast(
                variant: 'success',
                text: 'All BRANDs attached to the '.strtoupper($this->resourceNameSingular).'.'
            );

            $this->dispatch('refreshDiscountGroups');
        } else {
            Flux::toast(
                variant: 'error',
                text: 'Something went wrong.'
            );
        }
    }

    public function attach(): void
    {
        // Check if current user has permission to write partner discount groups
        if (! auth()->user()->can('write_partner_discount_groups')) {
            Flux::toast(
                variant: 'error',
                text: 'You do not have permission to attach brands and discount groups.'
            );

            return;
        }

        $rules = [];
        $messages = [];

        // Set validation rules
        foreach ($this->selectedBrands as $brand) {
            foreach ($brand->discountGroups as $discountGroup) {
                $key = "discountValues.{$brand->id}.{$discountGroup->id}";
                $maxDiscount = $discountGroup->discount;

                $rules[$key] = "nullable|numeric|min:0|max:{$maxDiscount}";
                $messages["{$key}.max"] = "The discount cannot exceed {$maxDiscount}%.";
            }
        }

        // Validate
        $this->validate($rules, $messages);

        // Attach brands
        foreach ($this->selectedBrandsIds as $brandId) {
            // Check if the brand is already attached
            if (! $this->resourceValue->brands()->where('brand_id', $brandId)->exists()) {
                $this->resourceValue->brands()->attach($brandId);
            }

            // Attach discount groups
            if (isset($this->discountValues[$brandId])) {
                foreach ($this->discountValues[$brandId] as $discountGroupId => $discountValue) {
                    if (! empty($discountValue)) {
                        // Check if the discount group is already attached
                        if (! $this->resourceValue->discountGroups()->where('discount_group_id', $discountGroupId)->exists()) {
                            $this->resourceValue->discountGroups()->attach($discountGroupId, [
                                'discount' => $discountValue,
                            ]);
                        } else {
                            $this->resourceValue->discountGroups()->updateExistingPivot($discountGroupId, [
                                'discount' => $discountValue,
                            ]);
                        }
                    }
                }
            }
        }

        Flux::toast(
            variant: 'success',
            text: 'BRANDs and DISCOUNT GROUPs have been attached.'
        );

        Flux::modals()->close();

        $this->resetPage();
        $this->resetForm();
        $this->dispatch('refreshDiscountGroups');
    }

    public function detach($id): void
    {
        // Check if the brand can be detached
        if (! $this->resourceValue->canDetachBrand($id)) {
            Flux::toast(
                variant: 'danger',
                text: 'Cannot detach this BRAND because it has products used in active orders.'
            );

            return;
        }

        $brand = Brand::findOrFail($id);
        $discountGroupIds = $brand->discountGroups->pluck('id')->toArray();

        if (! empty($discountGroupIds)) {
            foreach ($discountGroupIds as $discountGroupId) {
                $isAttachedToResource = $this->resourceValue->discountGroups()
                    ->where('discount_group_id', $discountGroupId)
                    ->exists();

                if ($isAttachedToResource) {
                    $this->resourceValue->discountGroups()->detach($discountGroupId);
                }
            }
        }

        $this->resourceValue->brands()->detach($id);

        Flux::toast(
            variant: 'success',
            text: 'The BRAND has been detached.'
        );

        $this->resetPage();
        $this->dispatch('refreshDiscountGroups');
    }

    public function view($id): void
    {
        $this->redirectRoute('brands.show', ['brand' => $id], navigate: true);
    }

    public function edit($id): void
    {
        $this->redirectRoute('brands.edit', ['brand' => $id], navigate: true);
    }

    public function delete($id): void
    {
        Brand::findOrFail($id)->delete();

        Flux::toast(
            variant: 'success',
            text: 'The BRAND has been deleted.'
        );
    }
}
