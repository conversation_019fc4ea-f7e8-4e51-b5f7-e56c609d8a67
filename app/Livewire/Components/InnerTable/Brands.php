<?php

namespace App\Livewire\Components\InnerTable;

use App\Models\Brand;
use Flux\Flux;
use Illuminate\Support\Str;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Lazy;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithPagination;

#[Lazy]
class Brands extends Component
{
    use WithPagination;

    public $resourceType;

    public $resourceNameSingular;

    public $resourceNamePlural;

    public $resourceValue;

    public $search = '';

    public function mount($resourceType, $resourceValue): void
    {
        $this->resourceType = $resourceType;
        $this->resourceNameSingular = strtolower($resourceType);
        $this->resourceNamePlural = Str::plural(strtolower($resourceType));
        $this->resourceValue = $resourceValue;
    }

    public function render()
    {
        return view('livewire.components.inner-table.brands', [
            'brands' => Brand::whereDoesntHave($this->resourceNamePlural, function ($query) {
                $query->where($this->resourceNameSingular.'_id', $this->resourceValue->id);
            })
                ->when($this->resourceValue->partner, function ($query) {
                    $query->whereHas('partners', function ($query) {
                        $query->where('partner_id', $this->resourceValue->partner->id);
                    });
                })
                ->orderBy('name', 'asc')
                ->get(),
        ]);
    }

    public function placeholder()
    {
        return view('components.inner-table.lazy-placeholder');
    }

    #[On('refreshBrands')]
    public function refreshBrands()
    {
        $this->resetPage();
    }

    public function updatedSearch(): void
    {
        $this->resetPage();
    }

    #[Computed]
    public function brands()
    {
        return $this->resourceValue->brands()
            ->when($this->search, function ($query) {
                $searchTerm = '%'.$this->search.'%';
                $query->where('name', 'ilike', $searchTerm);
            })
            ->orderBy('id', 'desc')
            ->paginate(5, pageName: 'brands-page');
    }

    public function attach($id): void
    {
        $this->resourceValue->brands()->attach($id);

        Flux::toast(
            variant: 'success',
            text: 'The BRAND has been attached.'
        );

        $this->dispatch('refreshDiscountGroups');

        Flux::modals()->close();
        $this->resetPage();
    }

    public function detach($id): void
    {
        $this->resourceValue->brands()->detach($id);

        Flux::toast(
            variant: 'success',
            text: 'The BRAND has been detached.'
        );

        $this->resetPage();
    }

    public function view($id): void
    {
        $this->redirectRoute('brands.show', ['brand' => $id], navigate: true);
    }

    public function edit($id): void
    {
        $this->redirectRoute('brands.edit', ['brand' => $id], navigate: true);
    }

    public function delete($id): void
    {
        Brand::findOrFail($id)->delete();

        Flux::toast(
            variant: 'success',
            text: 'The BRAND has been deleted.'
        );
    }
}
