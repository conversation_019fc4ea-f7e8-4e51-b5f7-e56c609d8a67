<?php

namespace App\Livewire\Components\InnerTable;

use App\Models\Client;
use Flux\Flux;
use Illuminate\Support\Str;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Lazy;
use Livewire\Component;
use Livewire\WithPagination;

#[Lazy]
class PartnerClients extends Component
{
    use WithPagination;

    public $resourceType;

    public $resourceNameSingular;

    public $resourceNamePlural;

    public $resourceValue;

    public $search = '';

    public function mount($resourceType, $resourceValue): void
    {
        $this->resourceType = $resourceType;
        $this->resourceNameSingular = strtolower($resourceType);
        $this->resourceNamePlural = Str::plural(strtolower($resourceType));
        $this->resourceValue = $resourceValue;
    }

    public function render()
    {
        return view('livewire.components.inner-table.partner-clients', [
            'clients' => Client::whereDoesntHave($this->resourceNameSingular, function ($query) {
                $query->where($this->resourceNameSingular.'_id', $this->resourceValue->id);
            })
                ->orderBy('company', 'asc')
                ->get(),
        ]);
    }

    public function placeholder()
    {
        return view('components.inner-table.lazy-placeholder');
    }

    public function updatedSearch(): void
    {
        $this->resetPage();
    }

    #[Computed]
    public function clients()
    {
        return $this->resourceValue->clients()
            ->when($this->search, function ($query) {
                $searchTerm = '%'.$this->search.'%';
                $query->where(function ($q) use ($searchTerm) {
                    $q->where('company', 'ilike', $searchTerm)
                        ->orWhere('email', 'ilike', $searchTerm);
                });
            })
            ->orderBy('id', 'desc')
            ->paginate(5, pageName: 'partner-clients-page');
    }

    public function associate($id): void
    {
        $client = Client::findOrFail($id);
        $client->partner()->associate($this->resourceValue);
        $client->save();

        Flux::toast(
            variant: 'success',
            text: 'The CLIENT has been associated.'
        );

        Flux::modals()->close();
        $this->resetPage();
    }

    public function dissociate($id): void
    {
        $client = Client::findOrFail($id);
        $client->partner()->dissociate($this->resourceValue);
        $client->save();

        Flux::toast(
            variant: 'success',
            text: 'The CLIENT has been dissociated.'
        );

        $this->resetPage();
    }

    public function view($id): void
    {
        $this->redirectRoute('clients.show', ['client' => $id], navigate: true);
    }

    public function edit($id): void
    {
        $this->redirectRoute('clients.edit', ['client' => $id], navigate: true);
    }

    public function delete($id): void
    {
        Client::findOrFail($id)->delete();

        Flux::toast(
            variant: 'success',
            text: 'The CLIENT has been deleted.'
        );
    }
}
