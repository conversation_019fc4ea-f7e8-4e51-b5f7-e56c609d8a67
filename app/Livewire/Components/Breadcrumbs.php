<?php

namespace App\Livewire\Components;

use Livewire\Component;

class Breadcrumbs extends Component
{
    public $parent;
    public $current;
    public $link;

    public function mount()
    {
        $this->parent = ucfirst(request()->segment(1));
        $this->current = ucfirst(request()->segment(2));

        $this->link = request()->segment(1) . '.' . 'index';
    }

    public function render()
    {
        return view('livewire.components.breadcrumbs');
    }
}
