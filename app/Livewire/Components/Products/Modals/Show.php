<?php

namespace App\Livewire\Components\Products\Modals;

use Flux\Flux;
use App\Models\Product;
use Livewire\Component;
use Livewire\Attributes\On;

class Show extends Component
{
    public Product $product;

    public $categories = [];
    public $options = [];

    public $selectedCategories = [];
    public $selectedOptions = [];

    public function render()
    {
        return view('livewire.components.products.modals.show');
    }

    #[On('product-show')]
    public function show(Product $product)
    {
        $this->product = $product->load('modules.options');

        if ($this->product->hasModules()) {
            $this->fetchCategories();
            $this->fetchOptions();
        }
        
        Flux::modal('product-show')->show();
    }

    public function showAddToCart($productId)
    {
        $this->dispatch('add-to-cart-show', product: $productId);
    }

    public function fetchCategories(): array
    {
        return $this->categories = $this->product->modules->map(function ($module) {
            return $module->options->pluck('category')->unique();
        })->toArray();
    }

    public function fetchOptions(): array
    {
        return $this->options = $this->product->modules->map(function ($module) {
            return $module->options->whereIn('category', $this->selectedCategories)->values();
        })->toArray();
    }

    public function updatedSelectedCategories($value, $key)
    {
        $this->selectedOptions[$key] = '';
        $this->fetchOptions();
    }

    public function setVariant(Product $variant)
    {
        $this->product = $variant;
    }

    public function getModularProductSku(): string
    {
        // Concat the selected options SKUs to create the modular product SKU
        return implode(collect($this->selectedOptions)->pluck('sku')->toArray());
    }

    public function resetProductShowModal(): void
    {
        $this->product = new Product();
        
        $this->categories = [];
        $this->options = [];
        $this->selectedCategories = [];
        $this->selectedOptions = [];
    }
}
