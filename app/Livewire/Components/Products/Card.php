<?php

namespace App\Livewire\Components\Products;

use App\Models\Product;
use Livewire\Attributes\Lazy;
use Livewire\Component;

#[Lazy]
class Card extends Component
{
    public Product $product;

    public function render()
    {
        return view('livewire.components.products.card');
    }

    public function placeholder()
    {
        return view('components.products.lazy-placeholder');
    }
}
