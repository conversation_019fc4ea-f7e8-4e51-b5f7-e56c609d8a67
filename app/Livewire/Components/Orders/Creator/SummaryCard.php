<?php

namespace App\Livewire\Components\Orders\Creator;

use Livewire\Component;
use App\Models\Order\Order;
use Livewire\Attributes\On;
use Livewire\Attributes\Lazy;

#[Lazy]
class SummaryCard extends Component
{
    public Order $order;

    public function render()
    {
        return view('livewire.components.orders.creator.summary-card');
    }

    public function placeholder()
    {
        return view('components.orders.summary-card-lazy-placeholder');
    }

    #[On('order-updated')]
    public function refreshOrder()
    {
        $this->order->refresh();
    }
}
