<?php

namespace App\Livewire\Components\Orders\Creator\Modals;

use Flux\Flux;
use App\Models\Product;
use Livewire\Component;
use App\Enums\AddonCodes;
use App\Models\Order\Order;
use Livewire\Attributes\On;
use App\Livewire\Forms\OrderAddonForm;

class Addons extends Component
{
    public Order $order;
    public Product $product;
    
    public OrderAddonForm $createAddonForm;
    public OrderAddonForm $editAddonForm;

    public function render()
    {
        return view('livewire.components.orders.creator.modals.addons',
            [
                'addonCodes' => AddonCodes::cases(),
            ]
        );
    }

    #[On('order-updated')]
    public function refreshOrder()
    {
        $this->order->refresh();
    }

    /**
     * Show the cart modal.
     */
    #[On('show-addons')]
    public function show()
    {
        Flux::modal('addons')->show();
    }


    /**
     * Save an addon.
     */
    public function saveAddon(): void
    {
        // Set the order id
        $this->createAddonForm->order_id = $this->order->id;

        // Store the addon
        $this->createAddonForm->store();

        Flux::toast(
            variant: 'success',
            text: 'The ADDON has been saved.'
        );

        $this->dispatch('order-updated');
    }

    /**
     * Edit an addon.
     */
    public function editAddon(int $addonId): void
    {
        $this->editAddonForm->reset();
        $this->resetValidation();

        $addon = $this->order->addons->find($addonId);

        $this->editAddonForm->setOrderAddon($addon);
    }

    /**
     * Cancel editing an addon.
     */
    public function cancelEditAddon(): void
    {
        $this->editAddonForm->reset();
        $this->resetValidation();
    }

    /**
     * Update an addon.
     */
    public function updateAddon(): void
    {
        $this->editAddonForm->update();

        Flux::toast(
            variant: 'success',
            text: 'The ADDON has been updated.'
        );

        $this->dispatch('order-updated');
    }

    /**
     * Delete an addon.
     */
    public function deleteAddon(int $addonId): void
    {
        $addon = $this->order->addons->find($addonId);

        $addon->delete();

        Flux::toast(
            variant: 'success',
            text: 'The ADDON has been deleted.'
        );

        $this->dispatch('order-updated');
    }

    public function updatedCreateAddonFormCode($value)
    {
        $this->createAddonForm->description = AddonCodes::from($value)->label();
    }
}
