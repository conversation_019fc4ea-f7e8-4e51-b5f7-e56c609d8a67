<?php

namespace App\Livewire\Components\Orders\Creator\Modals;

use App\Models\Order\CartGroup;
use Flux\Flux;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use Livewire\Component;
use App\Models\Order\Order;
use Livewire\Attributes\On;
use App\Livewire\Forms\CartGroupForm;

class CartGroups extends Component
{
    public Order $order;
    public $selectedCartGroup;
    public CartGroupForm $createCartGroupForm;
    public CartGroupForm $editCartGroupForm;

    public function mount()
    {
        $this->restoreSelectedCartGroupFromSession();
    }

    public function render()
    {
        return view('livewire.components.orders.creator.modals.cart-groups');
    }

    /**
     * Show the cart modal.
     */
    #[On('show-cart-groups')]
    public function show()
    {
        $this->order->load([
            'cartGroups',
        ]);

        Flux::modal('cart-groups')->show();
    }

    #[On('cart-group-content-updated')]
    public function refreshCartGroups()
    {
        $this->order->load([
            'cartGroups',
        ]);
    }

    /**
     * Select a cart group.
     */
    public function selectCartGroup(int $cartGroupId): void
    {
        $this->selectedCartGroup = CartGroup::find($cartGroupId);
        $this->saveSelectedCartGroupToSession($cartGroupId);
        $this->dispatch('select-cart-group', $this->selectedCartGroup->id);
    }

    public function deselectedCartGroup(): void
    {
        $this->selectedCartGroup = null;
        $this->clearSelectedCartGroupFromSession();
        $this->dispatch('deselect-cart-group');
    }

    /**
     * Save a cart group.
     */
    public function saveCartGroup(): void
    {
        // Set the order id and sort
        $this->createCartGroupForm->order_id = $this->order->id;
        $this->createCartGroupForm->sort = $this->order->cartGroups()->count() === 0 ? 0 : $this->order->cartGroups()->max('sort') + 1;

        // Store the cart group
        $this->createCartGroupForm->store();

        // Select the new cart group
        $this->selectCartGroup($this->order->cartGroups->last()->id);

        Flux::toast(
            variant: 'success',
            text: 'The CART GROUP has been saved.'
        );
    }

    /**
     * Edit a cart group.
     */
    public function editCartGroup(int $cartGroupId): void
    {
        $this->editCartGroupForm->reset();
        $this->resetValidation();

        $cartGroup = $this->order->cartGroups->find($cartGroupId);

        $this->editCartGroupForm->setCartGroup($cartGroup);
    }

    /**
     * Cancel editing a cart group.
     */
    public function cancelEditCartGroup(): void
    {
        $this->editCartGroupForm->reset();
        $this->resetValidation();
    }

    /**
     * Update a cart group.
     */
    public function updateCartGroup(): void
    {
        $this->editCartGroupForm->update();

        Flux::toast(
            variant: 'success',
            text: 'The CART GROUP has been updated.'
        );
    }

    /**
     * Delete a cart group.
     */
    public function deleteCartGroup(int $cartGroupId): void
    {
        $cartGroup = $this->order->cartGroups->find($cartGroupId);

        $this->moveCartGroupPosition($cartGroup, 999999);

        if ($cartGroupId === $this->selectedCartGroup?->id) {
            $this->deselectedCartGroup();
        }

        $cartGroup->delete();

        $this->dispatch('cart-group-content-updated');

        Flux::toast(
            variant: 'success',
            text: 'The CART GROUP has been deleted.'
        );
    }

    public function sort($item, $position)
    {
        $row = CartGroup::findOrFail($item);
        $this->moveCartGroupPosition($row, $position);
    }

    protected function moveCartGroupPosition($row, $position)
    {
        DB::transaction(function () use ($row, $position) {
            $current = $row->sort;
            $after = $position;

            if ($current === $after) return;

            $row->update(['sort' => -1]);

            $block = $this->order->cartGroups()->whereBetween('sort', [
                min($current, $after),
                max($current, $after),
            ]);

            $needToShiftBlockUpBecauseDraggingTargetDown = $current < $after;

            $needToShiftBlockUpBecauseDraggingTargetDown
                ? $block->decrement('sort')
                : $block->increment('sort');

            $row->update(['sort' => $after]);
        });
    }

    /**
     * Save selected cart group to session.
     */
    private function saveSelectedCartGroupToSession(int $cartGroupId): void
    {
        Session::put("selected_cart_group.order_{$this->order->id}", $cartGroupId);
    }

    /**
     * Restore selected cart group from session.
     */
    private function restoreSelectedCartGroupFromSession(): void
    {
        $sessionKey = "selected_cart_group.order_{$this->order->id}";
        $cartGroupId = Session::get($sessionKey);

        if ($cartGroupId && CartGroup::where('order_id', $this->order->id)->where('id', $cartGroupId)->exists()) {
            $this->selectCartGroup($cartGroupId);
        }
    }

    /**
     * Clear selected cart group from session.
     */
    private function clearSelectedCartGroupFromSession(): void
    {
        Session::forget("selected_cart_group.order_{$this->order->id}");
    }
}
