<?php

namespace App\Livewire\Components\Orders\Creator;

use App\Models\Client;
use App\Models\Product;
use Livewire\Component;
use Livewire\Attributes\Lazy;

#[Lazy]
class ProductCard extends Component
{
    public Product $product;
    public ?Client $client;

    public function render()
    {
        return view('livewire.components.orders.creator.product-card');
    }

    public function placeholder()
    {
        return view('components.products.lazy-placeholder');
    }
}
