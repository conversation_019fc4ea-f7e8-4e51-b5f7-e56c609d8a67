<?php

namespace App\Livewire\Components\Orders\Creator;

use Flux\Flux;
use Livewire\Component;
use Livewire\Attributes\On;
use Livewire\Attributes\Lazy;
use Illuminate\Support\Facades\DB;
use App\Livewire\Forms\OrderRowForm;
use App\Livewire\Forms\CustomProductForm;
use App\Models\Order\OrderRow as OrderRowModel;
use Livewire\WithFileUploads;

#[Lazy]
class OrderRow extends Component
{
    use WithFileUploads;

    public OrderRowModel $row;

    public OrderRowForm $orderRowForm;
    public CustomProductForm $customProductForm;
    public $resourceValue;

    public function mount($resourceValue = null): void
    {
        $this->resourceValue = $resourceValue;
    }

    public function render()
    {
        return view('livewire.components.orders.creator.order-row');
    }

    public function placeholder()
    {
        return view('components.orders.creator.order-row-lazy-placeholder');
    }

    #[On('row-updated')]
    public function refreshRow()
    {
        $this->row->refresh();
    }

    /**
     * Edit a row.
     */
    public function editRow(int $rowId)
    {
        $this->orderRowForm->reset();
        $this->customProductForm->reset();
        $this->resetValidation();

        $row = OrderRowModel::find($rowId);
        $this->orderRowForm->setOrderRow($row);

        if ($row->customProduct) {
            $this->customProductForm->setCustomProduct($row->customProduct);
        }
    }

    /**
     * Cancel editing a row.
     */
    public function cancelEditRow()
    {
        $this->orderRowForm->reset();
        $this->customProductForm->reset();
        $this->resetValidation();
    }

    /**
     * Update a row.
     */
    public function updateRow()
    {
        // Update the order row
        $this->orderRowForm->update();

        // Update the custom product if it exists
        if ($this->row->customProduct) {
            $this->customProductForm->update();
        }

        Flux::toast(
            variant: 'success',
            text: 'The ORDER ROW has been updated.'
        );

        // Refresh the row
        $this->row->refresh();
    }

    /**
     * Delete a row.
     */
    public function deleteRow(int $rowId)
    {
        // Get the row from the database
        $orderRow = OrderRowModel::findOrFail($rowId);

        // Check if the row has a custom product attached
        if ($orderRow->customProduct) {
            // Get a reference to the custom product
            $customProduct = $orderRow->customProduct;

            // Detach the custom product from the order row
            $orderRow->update(['custom_product_id' => null]);

            // Now delete the custom product
            $customProduct->delete();
        }

        // Move the row to the end of the cart group
        $this->moveOrderRow($orderRow, 9999);

        // Delete the row from the database
        $orderRow->delete();

        // Show a toast
        Flux::toast(
            variant: 'success',
            text: 'The ORDER ROW has been deleted.'
        );

        // Dispatch events to refresh the order and the cart group content (rows and custom products).
        $this->dispatch('order-updated');
        $this->dispatch('cart-group-content-updated');
    }

    /**
     * Move an order row.
     */
    protected function moveOrderRow($row, $position)
    {
        DB::transaction(function () use ($row, $position) {
            $current = $row->sort;
            $after = $position;

            if ($current === $after) return;

            $row->update(['sort' => -1]);

            $block = $row->cartGroup->orderRows()->whereBetween('sort', [
                min($current, $after),
                max($current, $after),
            ]);

            $needToShiftBlockUpBecauseDraggingTargetDown = $current < $after;

            $needToShiftBlockUpBecauseDraggingTargetDown
                ? $block->decrement('sort')
                : $block->increment('sort');

            $row->update(['sort' => $after]);
        });
    }

    // Unset the custom product image.
    public function unsetImage(): void
    {
        $this->customProductForm->image = null;
    }
}
