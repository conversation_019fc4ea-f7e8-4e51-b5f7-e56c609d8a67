<?php

namespace App\Livewire\Components\Orders;

use Livewire\Component;
use App\Models\Order\Order;
use Livewire\Attributes\Lazy;

#[Lazy]
class CartDetails extends Component
{
    public Order $order;
    public $showPaymentTerms = true;


    public function render()
    {
        return view('livewire.components.orders.cart-details');
    }

    public function placeholder()
    {
        return view('components.orders.cart-details-lazy-placeholder');
    }
}
