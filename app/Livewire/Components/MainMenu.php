<?php

namespace App\Livewire\Components;

use App\Models\Ticket;
use Flux\Flux;
use Livewire\Component;
use Livewire\Attributes\On;
use Illuminate\Support\Facades\Auth;

class MainMenu extends Component
{
    public $openTickets = 0;

    public function mount()
    {
        $this->openTickets();
    }

    public function render()
    {
        return view('livewire.components.main-menu');
    }

    public function logout()
    {
        Auth::logout();
        $this->redirectRoute('home');
    }

    #[On('redirect-to-users')]
    public function usersIndex(): void
    {
        Flux::modals()->close();
        $this->redirectRoute('users.index', navigate: true);
    }

    #[On('redirect-to-user-create')]
    public function usersCreate(): void
    {
        Flux::modals()->close();
        $this->redirectRoute('users.create', navigate: true);
    }

    #[On('redirect-to-contacts')]
    public function contactsIndex(): void
    {
        Flux::modals()->close();
        $this->redirectRoute('contacts.index', navigate: true);
    }

    #[On('redirect-to-contact-create')]
    public function contactsCreate(): void
    {
        Flux::modals()->close();
        $this->redirectRoute('contacts.create', navigate: true);
    }

    #[On('redirect-to-clients')]
    public function clientsIndex(): void
    {
        Flux::modals()->close();
        $this->redirectRoute('clients.index', navigate: true);
    }

    #[On('redirect-to-client-create')]
    public function clientsCreate(): void
    {
        Flux::modals()->close();
        $this->redirectRoute('clients.create', navigate: true);
    }

    #[On('redirect-to-partners')]
    public function partnersIndex(): void
    {
        Flux::modals()->close();
        $this->redirectRoute('partners.index', navigate: true);
    }

    #[On('redirect-to-partner-create')]
    public function partnersCreate(): void
    {
        Flux::modals()->close();
        $this->redirectRoute('partners.create', navigate: true);
    }

    #[On('redirect-to-suppliers')]
    public function suppliersIndex(): void
    {
        Flux::modals()->close();
        $this->redirectRoute('suppliers.index', navigate: true);
    }

    #[On('redirect-to-supplier-create')]
    public function suppliersCreate(): void
    {
        Flux::modals()->close();
        $this->redirectRoute('suppliers.create', navigate: true);
    }

    #[On('redirect-to-brands')]
    public function brandsIndex(): void
    {
        Flux::modals()->close();
        $this->redirectRoute('brands.index', navigate: true);
    }

    #[On('redirect-to-brand-create')]
    public function brandsCreate(): void
    {
        Flux::modals()->close();
        $this->redirectRoute('brands.create', navigate: true);
    }

    #[On('redirect-to-discount-groups')]
    public function discountGroupsIndex(): void
    {
        Flux::modals()->close();
        $this->redirectRoute('discount-groups.index', navigate: true);
    }

    #[On('redirect-to-discount-group-create')]
    public function discountGroupsCreate(): void
    {
        Flux::modals()->close();
        $this->redirectRoute('discount-groups.create', navigate: true);
    }

    #[On('redirect-to-collections')]
    public function collectionsIndex(): void
    {
        Flux::modals()->close();
        $this->redirectRoute('collections.index', navigate: true);
    }

    #[On('redirect-to-collection-create')]
    public function collectionsCreate(): void
    {
        Flux::modals()->close();
        $this->redirectRoute('collections.create', navigate: true);
    }

    #[On('redirect-to-orders')]
    public function ordersIndex(): void
    {
        Flux::modals()->close();
        $this->redirectRoute('orders.index', navigate: true);
    }

    #[On('redirect-to-order-create')]
    public function ordersCreate(): void
    {
        Flux::modals()->close();
        $this->redirectRoute('orders.create', navigate: true);
    }

    #[On('redirect-to-projects')]
    public function projectsIndex(): void
    {
        Flux::modals()->close();
        $this->redirectRoute('projects.index', navigate: true);
    }

    #[On('redirect-to-project-create')]
    public function projectsCreate(): void
    {
        Flux::modals()->close();
        $this->redirectRoute('projects.create', navigate: true);
    }

    #[On('redirect-to-roles')]
    public function rolesIndex(): void
    {
        Flux::modals()->close();
        $this->redirectRoute('roles.index', navigate: true);
    }

    #[On('redirect-to-role-create')]
    public function rolesCreate(): void
    {
        Flux::modals()->close();
        $this->redirectRoute('roles.create', navigate: true);
    }

    #[On('update-open-tickets')]
    public function openTickets(): void
    {
        $this->openTickets = Ticket::where('status', 'open')->count();
    }
}
