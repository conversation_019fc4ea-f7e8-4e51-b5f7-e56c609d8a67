<?php

namespace App\Livewire\Components\Collections;

use Livewire\Component;
use App\Models\Collection;
use Livewire\Attributes\Lazy;

#[Lazy]
class Card extends Component
{
    public Collection $collection;
    public array $selectedCollections;

    public function render()
    {
        return view('livewire.components.collections.card');
    }

    public function placeholder()
    {
        return view('components.collections.lazy-placeholder');
    }
}
