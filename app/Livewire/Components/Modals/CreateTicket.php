<?php

namespace App\Livewire\Components\Modals;

use App\Enums\ModelTypes;
use App\Livewire\Forms\TicketForm;
use App\Models\Ticket;
use Flux\Flux;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Livewire\Component;

class CreateTicket extends Component
{
    public ?TicketForm $form;

    public $resourceType;

    public $resourceValue;

    public $resourcePrefixName;

    public $size;

    public function mount($resourceType = null, $resourceValue = null): void
    {
        if (! is_null($resourceValue)) {
            $this->resourceValue = $resourceValue;
            $this->resourceType = get_class($resourceValue);
            $resourcePrefixName = Str::singular(class_basename($this->resourceType));
            $this->resourcePrefixName = $resourcePrefixName;
        } elseif (! is_null($resourceType)) {
            $this->resourceType = ModelTypes::getClassFromValue($resourceType);
            $this->resourceValue = null;
            $this->resourcePrefixName = Str::plural(class_basename($this->resourceType));
        } else {
            throw new \InvalidArgumentException('Either resourceType or resourceValue must be provided');
        }
    }

    public function render()
    {
        return view('livewire.components.modals.create-ticket');
    }

    public function create(): void
    {
        $this->form->ticketable_id = $this->resourceValue->id ?? null;
        $this->form->ticketable_type = $this->resourceType;
        $this->form->user_id = Auth::id();

        // Automatically add resource data to description
        if ($this->resourceValue) {
            $resourceData = $this->generateResourceDataObject();
            $this->form->description = trim($this->form->description."\n\n".$resourceData);
        }

        $this->form->store();

        Flux::toast(
            variant: 'success',
            text: 'The TICKET has been created.'
        );

        Flux::modals()->close();
        $this->resetForm();

        $this->dispatch('update-open-tickets');

        if (is_a($this->resourceType, Ticket::class, true)) {
            $this->dispatch('refresh-tickets');
        }
    }

    public function resetForm(): void
    {
        $this->form->reset();
        $this->resetValidation();
    }

    private function generateResourceDataObject(): string
    {
        $resourceClass = class_basename($this->resourceType);
        $data = [
            'ID' => $this->resourceValue->id,
        ];

        // Add specific fields based on resource type
        switch ($resourceClass) {
            case 'Client':
                $data['Company'] = $this->resourceValue->company ?? 'N/A';
                $data['Email'] = $this->resourceValue->email ?? 'N/A';
                $data['Country'] = $this->resourceValue->country ?? 'N/A';
                break;

            case 'Product':
                $data['SKU'] = $this->resourceValue->sku ?? 'N/A';
                $data['Description'] = $this->resourceValue->description ?? 'N/A';
                $data['Brand'] = $this->resourceValue->brand?->name ?? 'N/A';
                $data['Selling Price'] = $this->resourceValue->selling_price ?? 'N/A';
                break;

            case 'Order':
                $data['Order Number'] = $this->resourceValue->order_number ?? 'N/A';
                $data['Client'] = $this->resourceValue->client?->company ?? 'N/A';
                $data['Status'] = $this->resourceValue->status->label() ?? 'N/A';
                $data['Total'] = $this->resourceValue->total ?? 'N/A';
                break;

            case 'User':
                $data['Name'] = $this->resourceValue->name ?? 'N/A';
                $data['Email'] = $this->resourceValue->email ?? 'N/A';
                $data['Role'] = $this->resourceValue->role ?? 'N/A';
                break;

            case 'Supplier':
                $data['Company'] = $this->resourceValue->company ?? 'N/A';
                $data['Email'] = $this->resourceValue->email ?? 'N/A';
                $data['Country'] = $this->resourceValue->country ?? 'N/A';
                break;

            case 'Brand':
                $data['Name'] = $this->resourceValue->name ?? 'N/A';
                $data['Country'] = $this->resourceValue->country ?? 'N/A';
                break;

            case 'Collection':
                $data['Name'] = $this->resourceValue->name ?? 'N/A';
                $data['Brand'] = $this->resourceValue->brand?->name ?? 'N/A';
                break;

            case 'Project':
                $data['Title'] = $this->resourceValue->title ?? 'N/A';
                $data['Client'] = $this->resourceValue->client?->company ?? 'N/A';
                $data['Status'] = $this->resourceValue->status->label() ?? 'N/A';
                break;

            default:
                // For other resource types, try to get some common fields
                if (isset($this->resourceValue->name)) {
                    $data['Name'] = $this->resourceValue->name;
                }
                if (isset($this->resourceValue->title)) {
                    $data['Title'] = $this->resourceValue->title;
                }
                if (isset($this->resourceValue->email)) {
                    $data['Email'] = $this->resourceValue->email;
                }
                break;
        }

        // Generate formatted string
        $resourceInfo = "--- {$resourceClass} Data ---\n";
        foreach ($data as $key => $value) {
            $resourceInfo .= "{$key}: {$value}\n";
        }
        $resourceInfo .= '---';

        return $resourceInfo;
    }
}
