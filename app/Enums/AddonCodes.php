<?php

namespace App\Enums;

enum AddonCodes: string
{
    case SHARP = 'SHARP';
    case PLTNG = 'PLTNG';
    case BNKFE = 'BNKFE';
    case CRATE = 'CRATE';
    case CUSTM = 'CUSTM';
    case DEBOS = 'DEBOS';
    case HANDL = 'HANDL';
    case INSTL = 'INSTL';
    case INSRN = 'INSRN';
    case LNDRY = 'LNDRY';
    case LOGO = 'LOGO';
    case PLSNG = 'PLSNG';
    case PRJFE = 'PRJFE';
    case RFBSM = 'RFBSM';
    case EMBRD = 'EMBRD';
    case TINLG = 'TINLG';
    case SHPMT = 'SHPMT';

    /**
     * Get the adhoc code for the addon code.
     */
    public function adhocCode(): string
    {
        return match($this) {
            self::SHARP => 'AFF',
            self::PLTNG => 'ARG',
            self::BNKFE => 'BANK_FEE',
            self::CRATE => 'CRATE',
            self::CUSTM => 'CUSTOM',
            self::DEBOS => 'DEBOSSING',
            self::HANDL => 'HANDLING&PACKING',
            self::INSTL => 'INST',
            self::INSRN => 'INSURANCE',
            self::LNDRY => 'LAUNDRY',
            self::LOGO => 'LOGO',
            self::PLSNG => 'POLISHING',
            self::PRJFE => 'PROJECT_FEES',
            self::RFBSM => 'REFURBISHMENT',
            self::EMBRD => 'RICAMO',
            self::TINLG => 'STAGNA',
            self::SHPMT => 'TRA',
        };
    }

    /**
     * Get the description for the addon code.
     */
    public function label(): string
    {
        return match($this) {
            self::SHARP => 'SHARPENING',
            self::PLTNG => 'SILVER PLATING',
            self::BNKFE => 'BANK COST_CLIENT',
            self::CRATE => 'CRATE',
            self::CUSTM => 'CUSTOM DUTIES AND TAXES',
            self::DEBOS => 'DEBOSSING',
            self::HANDL => 'HANDLING AND PACKING',
            self::INSTL => 'INSTALLATION AND OPERATION CHECK',
            self::INSRN => 'INSURANCE ON THE GOODS',
            self::LNDRY => 'LAUNDRY SERVICE',
            self::LOGO => 'LOGO',
            self::PLSNG => 'CUTLERY POLISHING',
            self::PRJFE => 'PROJECT FEES',
            self::RFBSM => 'CUTLERY REFURBISHMENT',
            self::EMBRD => 'EMBROIDERY',
            self::TINLG => 'TIN LINING',
            self::SHPMT => 'SHIPPING COST',
        };
    }
}
