<?php

namespace App\Enums;

enum BrandLeadTime: string
{
    case OneTwoWeeks = '12W';
    case ThreeFourWeeks = '34W';
    case FiveSixWeeks = '56W';
    case SevenEightWeeks = '78W';
    case NinePlusWeeks = '9+W';
    case Ask = 'ASK';

    public function label()
    {
        return match ($this) {
            static::OneTwoWeeks => '1-2 Weeks',
            static::ThreeFourWeeks => '3-4 Weeks',
            static::FiveSixWeeks => '5-6 Weeks',
            static::SevenEightWeeks => '7-8 Weeks',
            static::NinePlusWeeks => '9+ Weeks',
            static::Ask => 'Ask for Lead Time',
        };
    }
}
