<?php

namespace App\Enums;

enum OrderCodeTypes: string
{
    case Retail = 'retail';
    case Trade = 'trade';

    public function label(): string
    {
        return match ($this) {
            static::Retail => 'Retail',
            static::Trade => 'Trade',
        };
    }

    public function code(): string
    {
        return match ($this) {
            static::Retail => 'RTL',
            static::Trade => 'TRD',
        };
    }

    public function adhocCode(): string
    {
        return match ($this) {
            static::Retail => 'PF005',
            static::Trade => 'PF001',
        };
    }

    public static function getAdhocCodeFromString(string $input): ?string
    {
        // Special case for "project"
        if (strtolower($input) === 'project') {
            return 'PF007';
        }

        // Check if the string matches one of the enum cases
        foreach (self::cases() as $case) {
            if ($case->value === strtolower($input)) {
                return $case->adhocCode();
            }
        }

        // Return null if no match is found
        return null;
    }
}
