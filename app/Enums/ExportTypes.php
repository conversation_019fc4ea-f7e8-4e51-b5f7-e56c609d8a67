<?php

namespace App\Enums;

enum ExportTypes: string
{
    case OrderFiles = 'order_files';
    case InvoicingAddress = 'invoicing_address';
    case ShippingAddress = 'shipping_address';
    case OrderRows = 'order_rows';

    /**
     * Get the human-readable label for the export type
     */
    public function label(): string
    {
        return match ($this) {
            static::OrderFiles => 'Order Files',
            static::InvoicingAddress => 'Invoicing Address',
            static::ShippingAddress => 'Shipping Address',
            static::OrderRows => 'Order Rows',
        };
    }
}
