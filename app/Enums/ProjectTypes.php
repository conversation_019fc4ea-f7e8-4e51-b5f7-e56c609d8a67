<?php

namespace App\Enums;

enum ProjectTypes: string
{
    case Nautical = 'nautical';
    case Residential = 'residential';
    case Aviation = 'aviation';
    case Hotellerie = 'hotellerie';

    public function label()
    {
        return match ($this) {
            static::Nautical => 'Nautical',
            static::Residential => 'Residential',
            static::Aviation => 'Aviation',
            static::Hotellerie => 'Hotellerie',
        };
    }

    public function code()
    {
        return match ($this) {
            static::Nautical => 'NAU',
            static::Residential => 'RES',
            static::Aviation => 'AVI',
            static::Hotellerie => 'HOT',
        };
    }

    public function color()
    {
        return match ($this) {
            static::Nautical => 'blue',
            static::Residential => 'yellow',
            static::Aviation => 'purple',
            static::Hotellerie => 'green',
        };
    }

    public function icon()
    {
        return match ($this) {
            static::Nautical => 'lifebuoy',
            static::Residential => 'home',
            static::Aviation => 'paper-airplane',
            static::Hotellerie => 'building-office',
        };
    }

    public function typeNameLabel()
    {
        return match ($this) {
            static::Nautical => 'Vessel Name',
            static::Residential => 'House Name',
            static::Aviation => 'Aircraft Name',
            static::Hotellerie => 'Hotel Name',
        };
    }

    public function typeLinkLabel()
    {
        return match ($this) {
            static::Nautical => 'Vessel Tracking',
            static::Residential => 'House Location',
            static::Aviation => 'Aircraft Tracking',
            static::Hotellerie => 'Hotel Location',
        };
    }

    public function typeLinkIcon()
    {
        return match ($this) {
            static::Nautical => 'link',
            static::Residential => 'link',
            static::Aviation => 'link',
            static::Hotellerie => 'link',
        };
    }
}
