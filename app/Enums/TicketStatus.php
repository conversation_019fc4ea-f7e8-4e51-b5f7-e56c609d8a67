<?php

namespace App\Enums;

enum TicketStatus: string
{
    case OPEN = 'open';
    case PROGRESS = 'progress';
    case CLOSED = 'closed';

    public function label(): string
    {
        return match ($this) {
            self::OPEN => 'Open',
            self::PROGRESS => 'Progress',
            self::CLOSED => 'Closed',
        };
    }

    public function color()
    {
        return match ($this) {
            self::OPEN => 'blue',
            self::PROGRESS => 'yellow',
            self::CLOSED => 'green',
        };
    }

    public function icon()
    {
        return match ($this) {
            self::OPEN => 'document-text',
            self::PROGRESS => 'arrow-path',
            self::CLOSED => 'check-circle',

        };
    }
}
