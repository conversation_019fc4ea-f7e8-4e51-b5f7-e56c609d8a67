<?php

namespace App\Enums;

enum CommercialCategories: string
{
    case MonzaStoreClient = 'monza_store_client';
    case Corporate = 'corporate';
    case Developer = 'developer';
    case ECommerceClient = 'e_commerce_client';
    case FranchisingBergomi = 'franchising_bergomi';
    case GeneralContractor = 'general_contractor';
    case InteriorDesigner = 'interior_designer';
    case Store = 'store';
    case FurnitureStore = 'furniture_store';
    case PrivateClient = 'private_client';
    case Shipyard = 'shipyard';
    case ArchitectureStudio = 'architecture_studio';
    case TradingCompany = 'trading_company';
    case VIPPrivateClient = 'vip_private_client';
    case VirtualStore = 'virtual_store';
    case AviationCompany = 'aviation_company';

    public function label()
    {
        return match ($this) {
            static::MonzaStoreClient => 'Monza Store Client',
            static::Corporate => 'Corporate',
            static::Developer => 'Developer',
            static::ECommerceClient => 'E-Commerce Client',
            static::FranchisingBergomi => 'Franchising Bergomi',
            static::GeneralContractor => 'General Contractor',
            static::InteriorDesigner => 'Interior Designer',
            static::Store => 'Store',
            static::FurnitureStore => 'Furniture Store',
            static::PrivateClient => 'Private Client',
            static::Shipyard => 'Shipyard',
            static::ArchitectureStudio => 'Architecture Studio',
            static::TradingCompany => 'Trading Company',
            static::VIPPrivateClient => 'VIP Private Client',
            static::VirtualStore => 'Virtual Store',
            static::AviationCompany => 'Aviation Company',
        };
    }

    public function code()
    {
        return match ($this) {
            static::MonzaStoreClient => 'CLN',
            static::Corporate => 'CRP',
            static::Developer => 'DEV',
            static::ECommerceClient => 'ECO',
            static::FranchisingBergomi => 'FRN',
            static::GeneralContractor => 'GCT',
            static::InteriorDesigner => 'INT',
            static::Store => 'NEG',
            static::FurnitureStore => 'NGM',
            static::PrivateClient => 'PRI',
            static::Shipyard => 'SHP',
            static::ArchitectureStudio => 'STA',
            static::TradingCompany => 'TRD',
            static::VIPPrivateClient => 'VIP',
            static::VirtualStore => 'VST',
            static::AviationCompany => 'AVI',
        };
    }
}
