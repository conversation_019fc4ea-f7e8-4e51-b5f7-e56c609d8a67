<?php

namespace App\Enums;

enum Companies: string
{
    case Bergomi = 'bergomi';
    case HouseAndYacht = 'house-and-yacht';

    /**
     * Get the label for the company.
     */
    public function label(): string
    {
        return match ($this) {
            self::<PERSON><PERSON> => '<PERSON><PERSON>',
            self::HouseAndYacht => 'House & Yacht',
        };
    }

    /**
     * Get the code for the company.
     */
    public function code(): string
    {
        return match ($this) {
            self::Bergomi => 'BR',
            self::HouseAndYacht => 'HY',
        };
    }
}
