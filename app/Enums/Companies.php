<?php

namespace App\Enums;

enum Companies: string
{
    case Bergomi = 'bergomi';
    case HouseAndYacht = 'house-and-yacht';

    /**
     * Get the label for the company.
     */
    public function label(): string
    {
        return match ($this) {
            self::<PERSON>omi => '<PERSON><PERSON>',
            self::HouseAndYacht => 'House & Yacht',
        };
    }

    /**
     * Get the code for the company.
     */
    public function code(): string
    {
        return match ($this) {
            self::Bergomi => 'BR',
            self::HouseAndYacht => 'HY',
        };
    }

    /**
     * Get the company information for PDF exports.
     */
    public function companyInfo(): object
    {
        return match ($this) {
            self::<PERSON><PERSON> => (object) [
                'name' => 'BERGOMI SRL',
                'address' => 'Via B. Zucchi, 40 - 20900 Monza (MB) - Italy',
                'phone' => 'Tel +39 ***********',
                'fax' => 'Fax +39 039 2300798',
                'vat_number' => 'P.IVA ***********',
                'fiscal_code' => 'C.F. ***********',
                'rea' => 'REA N. MB117824',
                'email' => '<EMAIL>',
                'website' => 'www.bergomiinteriors.com',
                'logo_url' => 'https://www.bergomiinteriors.com/wp-content/uploads/2022/09/cropped-LogoBergomiNEW.png',
                'bank_account_name' => 'BERGOMI SRL',
                'iban' => '***************************',
                'bic' => 'ICRAITRRCAR',
            ],
            self::HouseAndYacht => (object) [
                'name' => 'HOUSE & YACHT SRL',
                'address' => 'Via Example, 123 - 20100 Milano (MI) - Italy',
                'phone' => 'Tel +39 02 123 456',
                'fax' => 'Fax +39 02 123 457',
                'vat_number' => 'P.IVA ***********',
                'fiscal_code' => 'C.F. ***********',
                'rea' => 'REA N. MI123456',
                'email' => '<EMAIL>',
                'website' => 'www.houseandyacht.com',
                'logo_url' => 'https://example.com/logo.png',
                'bank_account_name' => 'HOUSE & YACHT SRL',
                'iban' => '***************************',
                'bic' => 'EXAMPLEBANK',
            ],
        };
    }
}
