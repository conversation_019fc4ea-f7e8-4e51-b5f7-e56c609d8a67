<?php

namespace App\Enums;

enum BrandPriceRange: int
{
    case One = 1;
    case Two = 2;
    case Three = 3;
    case Four = 4;
    case Five = 5;

    public function label()
    {
        return match ($this) {
            static::One => '€',
            static::Two => '€€',
            static::Three => '€€€',
            static::Four => '€€€€',
            static::Five => '€€€€€',
        };
    }
}
