<?php

namespace App\Enums;

enum DeliveryTerms: string
{
    case EXW = 'exw';
    case DAP = 'dap';
    case DDP = 'ddp';
    case CIF = 'cif';
    case FOB = 'fob';

    public function label()
    {
        return match ($this) {
            static::EXW => 'EXW',
            static::DAP => 'DAP',
            static::DDP => 'DDP',
            static::CIF => 'CIF',
            static::FOB => 'FOB',
        };
    }
}
