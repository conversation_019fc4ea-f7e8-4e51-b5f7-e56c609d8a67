<?php

namespace App\Enums;

enum ProjectStatuses: string
{
    case Draft = 'draft';
    case Pending = 'pending';
    case InProgress = 'in_progress';
    case Completed = 'completed';
    case Canceled = 'canceled';

    public function label()
    {
        return match ($this) {
            static::Draft => 'Draft',
            static::Pending => 'Pending',
            static::InProgress => 'In Progress',
            static::Completed => 'Completed',
            static::Canceled => 'Canceled',
        };
    }

    public function color()
    {
        return match ($this) {
            static::Draft => 'blue',
            static::Pending => 'yellow',
            static::InProgress => 'purple',
            static::Completed => 'green',
            static::Canceled => 'red',
        };
    }

    public function icon()
    {
        return match ($this) {
            static::Draft => 'clock',
            static::Pending => 'clock',
            static::InProgress => 'clock',
            static::Completed => 'check',
            static::Canceled => 'x',
        };
    }
}
