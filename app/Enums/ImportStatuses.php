<?php

namespace App\Enums;

enum ImportStatuses: string
{
    case Pending = 'pending';
    case Processing = 'processing';
    case Completed = 'completed';
    case CompletedWithErrors = 'completed_with_errors';
    case Failed = 'failed';

    /**
     * Get the human-readable label for the import status
     */
    public function label(): string
    {
        return match ($this) {
            static::Pending => 'Pending',
            static::Processing => 'Processing',
            static::Completed => 'Completed',
            static::CompletedWithErrors => 'Completed with Errors',
            static::Failed => 'Failed',
        };
    }

    /**
     * Get the icon for the import status
     */
    public function icon(): string
    {
        return match ($this) {
            static::Pending => 'clock',
            static::Processing => 'loading',
            static::Completed => 'check-circle',
            static::CompletedWithErrors => 'exclamation-triangle',
            static::Failed => 'x-circle',
        };
    }

    /**
     * Get the color for the import status
     */
    public function color(): string
    {
        return match ($this) {
            static::Pending => 'gray',
            static::Processing => 'blue',
            static::Completed => 'green',
            static::CompletedWithErrors => 'amber',
            static::Failed => 'red',
        };
    }

    /**
     * Check if the import is in a final state
     */
    public function isFinal(): bool
    {
        return in_array($this, [static::Completed, static::CompletedWithErrors, static::Failed]);
    }

    /**
     * Check if the import is currently active
     */
    public function isActive(): bool
    {
        return in_array($this, [static::Pending, static::Processing]);
    }
}
