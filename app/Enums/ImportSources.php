<?php

namespace App\Enums;

enum ImportSources: string
{
    case Cli = 'cli';
    case Scheduler = 'scheduler';
    case User = 'user';

    /**
     * Get the human-readable label for the import source
     */
    public function label(): string
    {
        return match ($this) {
            static::Cli => 'Command Line',
            static::Scheduler => 'Scheduled Task',
            static::User => 'User Interface',
        };
    }

    /**
     * Get the icon for the import source
     */
    public function icon(): string
    {
        return match ($this) {
            static::Cli => 'command-line',
            static::Scheduler => 'clock',
            static::User => 'user',
        };
    }

    /**
     * Get the color for the import source
     */
    public function color(): string
    {
        return match ($this) {
            static::Cli => 'gray',
            static::Scheduler => 'blue',
            static::User => 'green',
        };
    }
}
