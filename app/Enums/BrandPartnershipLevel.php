<?php

namespace App\Enums;

enum BrandPartnershipLevel: int
{
    case One = 1;
    case Two = 2;
    case Three = 3;

    public function label()
    {
        return match ($this) {
            static::One => 'Bronze',
            static::Two => 'Silver',
            static::Three => 'Gold',
        };
    }

    public function color()
    {
        return match ($this) {
            static::One => 'orange',
            static::Two => 'zinc',
            static::Three => 'yellow',
        };
    }
}
