<?php

namespace App\Enums;

enum AssetTypes: string
{
    case Asset = 'asset';
    case Meeting = 'meeting';
    case Moodboard = 'moodboard';
    case Presentation = 'presentation';

    public function label()
    {
        return match ($this) {
            static::Asset => 'Asset',
            static::Meeting => 'Meeting',
            static::Moodboard => 'Moodboard',
            static::Presentation => 'Presentation',
        };
    }
}
