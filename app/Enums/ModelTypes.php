<?php

namespace App\Enums;


use App\Models\Brand;
use App\Models\Client;
use App\Models\Collection;
use App\Models\Contact;
use App\Models\DiscountGroup;
use App\Models\Export;
use App\Models\Import;
use App\Models\Order\Order;
use App\Models\Partner;
use App\Models\Product;
use App\Models\Project\Project;
use App\Models\Supplier;
use App\Models\Ticket;
use App\Models\User;
use Spatie\Permission\Models\Role;

enum ModelTypes: string
{
    case Brands = 'brands';
    case Clients = 'clients';
    case Collections = 'collections';
    case Contacts = 'contacts';
    case DiscountGroups = 'discount_groups';
    case ClientDiscountGroups = 'client_discount_groups';
    case PartnerDiscountGroups = 'partner_discount_groups';
    case Orders = 'orders';
    case Partners = 'partners';
    case Products = 'products';
    case Projects = 'projects';
    case Roles = 'roles';
    case Suppliers = 'suppliers';
    case Tickets = 'tickets';
    case Users = 'users';

    case Imports = 'imports';
    case Exports = 'exports';


    public function label()
    {
        return match ($this) {
            static::Users => 'Users',
            static::Roles => 'Roles',
            static::Orders => 'Orders',
            static::Projects => 'Projects',
            static::Clients => 'Clients',
            static::Partners => 'Partners',
            static::Brands => 'Brands',
            static::Products => 'Products',
            static::DiscountGroups => 'Discount Groups',
            static::ClientDiscountGroups => 'Discount Groups - Clients',
            static::PartnerDiscountGroups => 'Discount Groups - Partners',
            static::Suppliers => 'Suppliers',
            static::Collections => 'Collections',
            static::Contacts => 'Contacts',
            static::Tickets => 'Tickets',
            static::Imports => 'Imports',
            static::Exports => 'Exports',
        };
    }

    public function matchAttributes()
    {
        return match ($this) {
            static::Users => User::modelAttributes(),
            static::Roles => null,
            static::Orders => Order::modelAttributes(),
            static::Projects => Project::modelAttributes(),
            static::Clients => Client::modelAttributes(),
            static::Partners => Partner::modelAttributes(),
            static::Brands => Brand::modelAttributes(),
            static::Products => null,
            static::DiscountGroups => DiscountGroup::modelAttributes(),
            static::ClientDiscountGroups => null,
            static::PartnerDiscountGroups => null,
            static::Suppliers => Supplier::modelAttributes(),
            static::Collections => Collection::modelAttributes(),
            static::Contacts => Contact::modelAttributes(),
            static::Tickets => null,
            static::Imports => null,
            static::Exports => null,
        };
    }

    public static function mapPermissions(): array
    {
        return collect(static::cases())->mapWithKeys(function (ModelTypes $type) {
            return [$type->value => [
                'read' => false,
                'write' => false,
                'attributes' => collect($type->matchAttributes())->filter(
                    fn($attr) => $attr['required'] ?? false
                )->pluck('key')->toArray()
            ]];
        })->toArray();
    }

    public static function fromClass(string $className): ?self
    {
        return match ($className) {
            Order::class => self::Orders,
            Product::class => self::Products,
            Project::class => self::Projects,
            User::class => self::Users,
            Client::class => self::Clients,
            Partner::class => self::Partners,
            Brand::class => self::Brands,
            Supplier::class => self::Suppliers,
            Collection::class => self::Collections,
            Contact::class => self::Contacts,
            DiscountGroup::class => self::DiscountGroups,
            Ticket::class => self::Tickets,
            Role::class => self::Roles,
            Import::class => self::Imports,
            Export::class => self::Exports,
            default => null,
        };
    }

    public static function getClassFromValue(string $value): ?string
    {
        return match ($value) {
            'orders' => Order::class,
            'projects' => Project::class,
            'users' => User::class,
            'clients' => Client::class,
            'partners' => Partner::class,
            'brands' => Brand::class,
            'products' => Product::class,
            'suppliers' => Supplier::class,
            'collections' => Collection::class,
            'contacts' => Contact::class,
            'discount_groups' => DiscountGroup::class,
            'tickets' => Ticket::class,
            'roles' => Role::class,
            'imports' => Import::class,
            'exports' => Export::class,
            default => null,
        };
    }
}
