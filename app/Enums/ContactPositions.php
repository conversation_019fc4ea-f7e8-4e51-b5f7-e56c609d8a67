<?php

namespace App\Enums;

enum ContactPositions: string
{
    case Sales = 'sales';
    case Warehouse = 'warehouse';
    case Administration = 'administration';
    case Owner = 'owner';

    public function label()
    {
        return match ($this) {
            static::Sales => 'Sales',
            static::Warehouse => 'Warehouse',
            static::Administration => 'Administration',
            static::Owner => 'Owner',
        };
    }
}
