<?php

namespace App\Enums;

enum AddressTypes: string
{
    case Invoicing = 'invoicing';
    case Shipping = 'shipping';

    public function label()
    {
        return match ($this) {
            static::Invoicing => 'Invoicing',
            static::Shipping => 'Shipping',
        };
    }

    public function icon()
    {
        return match ($this) {
            static::Invoicing => 'document',
            static::Shipping => 'truck',
        };
    }

    public function color()
    {
        return match ($this) {
            static::Invoicing => 'cyan',
            static::Shipping => 'amber',
        };
    }
}
