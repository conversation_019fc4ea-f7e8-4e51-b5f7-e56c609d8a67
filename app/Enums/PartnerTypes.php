<?php

namespace App\Enums;

enum PartnerTypes: string
{
    case Agent = 'agent';
    case Company = 'company';
    case Architect = 'architect';

    public function label()
    {
        return match ($this) {
            static::Agent => 'Agent',
            static::Company => 'Company',
            static::Architect => 'Architect',
        };
    }

    public function icon()
    {
        return match ($this) {
            static::Agent => 'megaphone',
            static::Company => 'building-office',
            static::Architect => 'paint-brush',
        };
    }

    public function color()
    {
        return match ($this) {
            static::Agent => 'cyan',
            static::Company => 'amber',
            static::Architect => 'indigo',
        };
    }
}