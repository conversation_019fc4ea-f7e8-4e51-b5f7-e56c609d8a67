<?php

namespace App\Enums;

enum ExportSources: string
{
    case OrderSubmit = 'order_submit';
    case OrderApprove = 'order_approve';
    case User = 'user';
    case Scheduler = 'scheduler';

    /**
     * Get the human-readable label for the export source
     */
    public function label(): string
    {
        return match ($this) {
            static::OrderSubmit => 'Order Submit',
            static::OrderApprove => 'Order Approve',
            static::User => 'User Interface',
            static::Scheduler => 'Scheduled Task',
        };
    }

    /**
     * Get the icon for the export source
     */
    public function icon(): string
    {
        return match ($this) {
            static::OrderSubmit => 'paper-airplane',
            static::OrderApprove => 'check-badge',
            static::User => 'user',
            static::Scheduler => 'clock',
        };
    }

    /**
     * Get the color for the export source
     */
    public function color(): string
    {
        return match ($this) {
            static::OrderSubmit => 'blue',
            static::OrderApprove => 'green',
            static::User => 'gray',
            static::Scheduler => 'purple',
        };
    }
}
