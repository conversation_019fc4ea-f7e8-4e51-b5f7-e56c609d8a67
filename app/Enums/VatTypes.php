<?php

namespace App\Enums;

enum VatTypes: string
{
    case Standard = 'standard';
    case NI74 = 'ni74';
    case NI41 = 'ni41';
    case NI41T = 'ni41t';
    case NI58 = 'ni58';
    case NI8IT = 'ni8it';
    case NI8LA = 'ni8la';
    case NI8LB = 'ni8lb';
    case NI8TR = 'ni8tr';

    public function label(): string
    {
        return match ($this) {
            static::Standard => '22 - IVA 22%',
            static::NI74 => 'NI.74 - Non Imp. Art.74 Comma 7-8',
            static::NI41 => 'NI41 - Non Imp. Art. 41 Cessioni UE',
            static::NI41T => 'NI41T - Non Imp. Art. 41 Cessioni UE Triang',
            static::NI58 => 'NI58 - Operazione Triang. non Imponibile',
            static::NI8IT => 'NI8IT - N.I. Art. 8 con dich. intento',
            static::NI8LA => 'NI8LA - Non Imp. Art. 8, Comma 1, Lettera A',
            static::NI8LB => 'NI8LB - Non Imp. Art. 8, Comma 1, Lettera B',
            static::NI8TR => 'NI8TR - Operazione triangolare art.8',
        };
    }

    public function rate(): float
    {
        return match ($this) {
            static::Standard => 0.22,
            static::NI74 => 0.0,
            static::NI41 => 0.0,
            static::NI41T => 0.0,
            static::NI58 => 0.0,
            static::NI8IT => 0.0,
            static::NI8LA => 0.0,
            static::NI8LB => 0.0,
            static::NI8TR => 0.0,
        };
    }

    public function adhocCode(): string
    {
        return match ($this) {
            static::Standard => '',
            static::NI74 => 'NI.74',
            static::NI41 => 'NI41',
            static::NI41T => 'NI41T',
            static::NI58 => 'NI58',
            static::NI8IT => 'NI8IT',
            static::NI8LA => 'NI8LA',
            static::NI8LB => 'NI8LB',
            static::NI8TR => 'NI8TR',
        };
    }
}
