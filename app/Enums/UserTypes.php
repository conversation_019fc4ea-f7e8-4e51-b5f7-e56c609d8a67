<?php

namespace App\Enums;

enum UserTypes: string
{
    case Admin = 'admin';
    case Member = 'member';

    public function label()
    {
        return match ($this) {
            static::Admin => 'Admin',
            static::Member => 'Member',
        };
    }

    public function  icon()
    {
        return match ($this) {
            static::Admin => 'wrench-screwdriver',
            static::Member => 'user',
        };
    }

    public function  color()
    {
        return match ($this) {
            static::Admin => 'blue',
            static::Member => 'yellow',
        };
    }
}
