<?php

namespace App\Enums;

enum ClientTypes: string
{
    case Lead = 'lead';
    case Prospect = 'prospect';
    case Customer = 'customer';

    public function label()
    {
        return match ($this) {
            static::Lead => 'Lead',
            static::Prospect => 'Prospect',
            static::Customer => 'Customer',
        };
    }

    public function icon()
    {
        return match ($this) {
            static::Lead => 'bolt',
            static::Prospect => 'magnifying-glass',
            static::Customer => 'check-badge',
        };
    }

    public function color()
    {
        return match ($this) {
            static::Lead => 'blue',
            static::Prospect => 'yellow',
            static::Customer => 'green',
        };
    }
}
