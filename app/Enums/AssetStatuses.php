<?php

namespace App\Enums;

enum AssetStatuses: string
{
    case Open = 'open';
    case Submitted = 'submitted';
    case Approved = 'approved';
    case Rejected = 'rejected';

    public function label()
    {
        return match ($this) {
            static::Open => 'Open',
            static::Submitted => 'Submitted',
            static::Approved => 'Approved',
            static::Rejected => 'Rejected',
        };
    }

    public function color()
    {
        return match ($this) {
            static::Open => 'blue',
            static::Submitted => 'yellow',
            static::Approved => 'green',
            static::Rejected => 'red',
        };
    }

    public function icon()
    {
        return match ($this) {
            static::Open => 'clipboard-document-list',
            static::Submitted => 'check',
            static::Approved => 'check-badge',
            static::Rejected => 'x',
        };
    }
}
