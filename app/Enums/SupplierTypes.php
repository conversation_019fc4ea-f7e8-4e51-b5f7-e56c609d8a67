<?php

namespace App\Enums;

enum SupplierTypes: string
{
    case BrandSupplier = 'brand';
    case ThirdPartySupplier = 'third-party';

    public function label()
    {
        return match ($this) {
            static::BrandSupplier => 'Brand',
            static::ThirdPartySupplier => 'Third Party',
        };
    }

    public function icon()
    {
        return match ($this) {
            static::BrandSupplier => 'shopping-bag',
            static::ThirdPartySupplier => 'paint-brush',
        };
    }

    public function color()
    {
        return match ($this) {
            static::BrandSupplier => 'blue',
            static::ThirdPartySupplier => 'green',
        };
    }
}
