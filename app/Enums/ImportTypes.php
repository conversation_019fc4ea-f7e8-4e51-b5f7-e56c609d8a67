<?php

namespace App\Enums;

enum ImportTypes: string
{
    case Brands = 'brands';
    case BrandDiscountGroups = 'brand_discount_groups';
    case BrandContacts = 'brand_contacts';

    case Clients = 'clients';
    case ClientAddresses = 'client_addresses';
    case ClientContacts = 'client_contacts';
    case ClientBrands = 'client_brands';
    case ClientDiscountGroups = 'client_discount_groups';

    case Partners = 'partners';
    case PartnerAddresses = 'partner_addresses';
    case PartnerContacts = 'partner_contacts';
    case PartnerBrands = 'partner_brands';
    case PartnerDiscountGroups = 'partner_discount_groups';

    case Suppliers = 'suppliers';
    case SupplierAddresses = 'supplier_addresses';

    // Products
    case Products = 'products';
    case SimpleProducts = 'simple_products';
    case VariantProducts = 'variant_products';
    case ModularProducts = 'modular_products';
    case ModularOptions = 'modular_options';

    // Collections
    case Collections = 'collections';

    // Discount Groups
    case DiscountGroups = 'discount_groups';

    // Brand Tags
    case BrandTags = 'brand_tags';

    // Inventory
    case Inventory = 'inventory';


    /**
     * Get the human-readable label for the import type
     */
    public function label(): string
    {
        return match ($this) {
            // static::Agent => 'Agent',
            static::Brands => 'Brands',
            static::BrandDiscountGroups => 'Brand Discount Groups',
            static::BrandContacts => 'Brand Contacts',

            static::Clients => 'Clients',
            static::ClientAddresses => 'Client Addresses',
            static::ClientContacts => 'Client Contacts',
            static::ClientBrands => 'Client Brands',
            static::ClientDiscountGroups => 'Client Discount Groups',

            static::Partners => 'Partners',
            static::PartnerAddresses => 'Partner Addresses',
            static::PartnerContacts => 'Partner Contacts',
            static::PartnerBrands => 'Partner Brands',
            static::PartnerDiscountGroups => 'Partner Discount Groups',

            static::Suppliers => 'Suppliers',
            static::SupplierAddresses => 'Supplier Addresses',

            static::Products => 'Products',
            static::SimpleProducts => 'Simple Products',
            static::VariantProducts => 'Variant Products',
            static::ModularProducts => 'Modular Products',
            static::ModularOptions => 'Modular Options',

            static::Collections => 'Collections',

            static::DiscountGroups => 'Discount Groups',

            static::BrandTags => 'Brand Tags',

            static::Inventory => 'Inventory',
        };
    }
}
