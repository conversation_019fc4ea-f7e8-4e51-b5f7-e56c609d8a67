<?php

namespace App\Enums;

enum WarehouseNames: string
{
    case Retail = 'retail';
    case Trade = 'trade';

    /**
     * Get the human-readable label for the warehouse.
     */
    public function label(): string
    {
        return match ($this) {
            self::Retail => 'Stock Retail',
            self::Trade => 'Stock Trade',
        };
    }

    /**
     * Get the adhoc code for the warehouse.
     */
    public function adhocCode(): string
    {
        return match ($this) {
            self::Retail => '00001',
            self::Trade => '00004',
        };
    }
}
