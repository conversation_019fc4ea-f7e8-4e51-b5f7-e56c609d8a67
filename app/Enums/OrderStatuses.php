<?php

namespace App\Enums;

enum OrderStatuses: string
{
    case Open = 'open';
    case Submitted = 'submitted';
    case Approved = 'approved';
    case OnGoing = 'on_going';
    case PartiallyShipped = 'partially_shipped';
    case Shipped = 'shipped';
    case Rejected = 'rejected';

    public function label()
    {
        return match ($this) {
            static::Open => 'Open',
            static::Submitted => 'Submitted',
            static::Approved => 'Approved',
            static::OnGoing => 'On Going',
            static::PartiallyShipped => 'Partially Shipped',
            static::Shipped => 'Shipped',
            static::Rejected => 'Rejected',
        };
    }

    public function color()
    {
        return match ($this) {
            static::Open => 'blue',
            static::Submitted => 'yellow',
            static::Approved => 'green',
            static::OnGoing => 'purple',
            static::PartiallyShipped => 'orange',
            static::Shipped => 'green',
            static::Rejected => 'red',
        };
    }

    public function icon()
    {
        return match ($this) {
            static::Open => 'clipboard-document-list',
            static::Submitted => 'check',
            static::Approved => 'check-badge',
            static::OnGoing => 'arrow-path-rounded-square',
            static::PartiallyShipped => 'truck',
            static::Shipped => 'clipboard-document-check',
            static::Rejected => 'x-mark',
        };
    }
}
