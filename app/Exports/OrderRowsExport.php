<?php

namespace App\Exports;

use App\Models\Order\Order;
use App\Models\Order\OrderRow;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;

class OrderRowsExport implements FromQuery, WithHeadings, WithMapping
{
    protected $order;

    public function __construct(Order $order)
    {
        $this->order = $order;
    }
    
    public function query()
    {
        // Optimized query with eager loading to prevent N+1 queries during export
        $cartGroupIds = $this->order->cartGroups()->pluck('id')->toArray();
        
        return OrderRow::query()
            ->whereIn('cart_group_id', $cartGroupIds)
            ->with([
                'product' => function($query) {
                    $query->withTrashed()
                        ->with(['brand:id,name', 'collection:id,name']);
                },
                'customProduct:id,sku,description',
                'cartGroup:id,name'
            ])
            ->orderBy('sort');
    }

    public function headings(): array
    {
        return [
            'ADHOC_SKU',
            'QUANTITY',
            'PRICE',
            'DISCOUNT',
        ];
    }

    public function map($row): array
    {
        return [
            $row->options ? $row->getModularSku() : $row->product?->adhoc_sku ?? $row->customProduct?->sku ?? '',
            $row->quantity,
            isset($row->selling_price_override) ? $row->selling_price_override : ($row->selling_price ?? $row->product->selling_price ?? 0),
            isset($row->discount_override) ? "-" . $row->discount_override : "-" . ($row->discount ?? $row->getDiscountForClient()),
        ];
    }
}
