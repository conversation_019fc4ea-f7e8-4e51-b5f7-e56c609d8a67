<?php

namespace App\Exports;

use App\Models\Order\Order;
use App\Models\Order\OrderRow;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithDrawings;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Events\AfterSheet;

class OrderExport implements FromQuery, WithColumnFormatting, WithDrawings, WithEvents, WithHeadings, WithMapping
{
    protected $order;

    protected $rows = [];

    protected $rowIndex = 0;

    protected $rowHeight = 60; // Row height in points

    private $temporaryFiles;

    public function __construct(Order $order)
    {
        $this->order = $order;
    }

    public function query()
    {
        // Get cart groups ordered by sort to maintain proper order
        $cartGroupIds = $this->order->cartGroups()->orderBy('sort', 'asc')->pluck('id')->toArray();

        return OrderRow::query()
            ->whereIn('cart_group_id', $cartGroupIds)
            ->with([
                'product' => function ($query) {
                    $query->withTrashed()
                        ->with([
                            'brand:id,name',
                            'collection:id,name',
                            'discountGroup:id,discount',
                            'modules',
                        ]);
                },
                'customProduct:id,sku,description,brand_name,dimensions,image,brand_name,dimensions,supplier_color',
                'cartGroup' => function ($query) {
                    $query->with(['order.client.discountGroups']);
                },
            ])
            ->orderByRaw('
                CASE cart_group_id ' .
                collect($cartGroupIds)->map(fn($id, $index) => "WHEN {$id} THEN {$index}")->implode(' ') .
                ' END, sort ASC'
            );
    }

    public function headings(): array
    {
        return [
            'FLOOR & LOCATION',
            'ID CODE',
            'OPTION',
            'BRAND',
            'IMAGE',
            'MODEL',
            'DESCRIPTION',
            'CODE',
            'FINISHES',
            'DIMENSIONS',
            'QTY',
            'M.C.',
            'UNIT PRICE',
            'TOTAL PRICE',
            'BG UNIT PRICE',
            'BG TOTAL PRICE',
            'DISCOUNT',
            'VARIATION',
            'UNIT DISCOUNTED PRICE',
            'TOTAL DISCOUNTED PRICE',
            'PROVV. PARTNER',
            'BG INCOME',
            'LEAD TIME',
            'NOTES',
        ];
    }

    public function map($row): array
    {
        // Store the row for later use in drawings
        $this->rows[$this->rowIndex++] = $row;

        // Build the options string if the row has options (optimized to use eager-loaded relationships)
        $optionDescriptions = '';
        if ($row->options) {
            $options = $row->getSelectedOptions();
            $optionDescriptions = array_map(function ($option) {
                return $option['description'];
            }, $options);
        }

        // Pre-calculate values to avoid multiple calculations
        // Handle modular products with options pricing
        if ($row->product && $row->product->hasModules() && $row->options) {
            $unitPrice = $row->selling_price ?? $row->product->getModularPrice($row->options) ?? 0;
        } else {
            $unitPrice = $row->selling_price ?? $row->product?->selling_price ?? 0;
        }
        $quantity = $row->quantity ?? 0;
        $discountPercent = $row->product?->discountGroup?->discount ?? 0;
        $bgUnitPrice = $unitPrice * (1 - $discountPercent / 100);
        $bgTotalPrice = $bgUnitPrice * $quantity;
        $rowFinalPrice = $row->rowFinalPrice ?? 0;

        return [
            // FLOOR & LOCATION
            $row->cartGroup->name ?? '',

            // ID CODE
            $row->position_id ?? '',

            // OPTION
            '', // EMPTY

            // BRAND
            $row->product?->brand->name ?? $row->customProduct?->brand_name ?? '',

            // IMAGE
            '', // Populated with WithDrawings

            // MODEL
            $row->product?->collection ? explode('-', $row->product?->collection->name)[0] : '',

            // DESCRIPTION
            $row->description ?? $row->product?->description ?? $row->customProduct?->description ?? '',

            // CODE
            $row->sku ?? ($row->options ? $row->getModularSku() : $row->product?->sku ?? $row->customProduct?->sku ?? ''),

            // FINISHES
            $row->customProduct?->supplier_color ? 'Color: ' . $row->customProduct?->supplier_color : $optionDescriptions,

            // DIMENSIONS
            $row->product?->dimensions ?? $row->customProduct?->dimensions ?? '',

            // QTY
            $quantity,

            // M.C.
            $row->product?->purchase_units ?? '',

            // UNIT PRICE
            $unitPrice,

            // TOTAL PRICE
            $unitPrice * $quantity,

            // BG UNIT PRICE
            $bgUnitPrice,

            // BG TOTAL PRICE
            $bgTotalPrice,

            // DISCOUNT
            isset($row->discount_override) ? $row->discount_override : ($row->discount ?? $row->getDiscountForClient()),

            // VARIATION
            $row->variation ?? '',

            // UNIT DISCOUNTED PRICE
            $row->rowUnitPrice ?? '',

            // TOTAL DISCOUNTED PRICE
            $rowFinalPrice,

            // PROVV. PARTNER
            '', // EMPTY

            // BG INCOME
            $rowFinalPrice - $bgTotalPrice,

            // LEAD TIME
            $row->expected_delivery_date?->format('d-m-y') ?? '-',

            // NOTES
            '', // EMPTY
        ];
    }

    public function columnFormats(): array
    {
        return [
            'M' => '#,##0.00_-"€"', // UNIT PRICE
            'N' => '#,##0.00_-"€"', // TOTAL PRICE
            'O' => '#,##0.00_-"€"', // BG UNIT PRICE
            'P' => '#,##0.00_-"€"', // BG TOTAL PRICE
            'S' => '#,##0.00_-"€"', // UNIT DISCOUNTED PRICE
            'T' => '#,##0.00_-"€"', // TOTAL DISCOUNTED PRICE
            'V' => '#,##0.00_-"€"', // BG INCOME
        ];
    }

    public function drawings()
    {
        $this->temporaryFiles = [];

        $drawings = [];

        foreach ($this->rows as $index => $row) {
            $imagePath = null;

            if ($row->product && $row->product->image) {
                $imagePath = $this->getLocalImagePath($row->product->image);
            } elseif ($row->customProduct && $row->customProduct->image) {
                $imagePath = $this->getLocalImagePath($row->customProduct->image);
            }

            if ($imagePath) {
                $drawing = new \PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
                $drawing->setName('Product Image');
                $drawing->setDescription('Product Image');
                $drawing->setPath($imagePath);
                $drawing->setHeight(50);
                $drawing->setResizeProportional(true);
                $drawing->setCoordinates('E' . ($index + 2));
                $drawing->setOffsetX(2);
                $drawing->setOffsetY(2);

                $drawings[] = $drawing;
            }
        }

        return $drawings;
    }

    private function getLocalImagePath($path)
    {
        try {
            $diskName = config('filesystems.public');
            $disk = Storage::disk($diskName);
            $diskDriver = config("filesystems.disks.{$diskName}.driver");

            if ($diskDriver === 's3') {
                $tmpPath = storage_path('app/tmp/' . basename($path));
                if (!file_exists(dirname($tmpPath))) {
                    mkdir(dirname($tmpPath), 0755, true);
                }

                // Try to get the file, but handle the case where it doesn't exist
                $fileContent = $disk->get($path);
                file_put_contents($tmpPath, $fileContent);

                $this->temporaryFiles[] = $tmpPath;

                return $tmpPath;
            }

            $localPath = $disk->path($path);

            // For local storage, check if file exists without additional API calls
            return file_exists($localPath) ? $localPath : null;
        } catch (\Exception $e) {
            // If image doesn't exist or any other error, return null
            return null;
        }
    }

    public function __destruct()
    {
        if (!empty($this->temporaryFiles)) {
            foreach ($this->temporaryFiles as $file) {
                if (file_exists($file)) {
                    @unlink($file);
                }
            }
        }
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                // Set row height for header and all data rows
                $event->sheet->getRowDimension(1)->setRowHeight($this->rowHeight);

                foreach ($this->rows as $index => $row) {
                    $rowNumber = $index + 2;
                    $event->sheet->getRowDimension($rowNumber)->setRowHeight($this->rowHeight);
                }

                // Set the first row (header) in bold
                $event->sheet->getStyle('A1:X1')->getFont()->setBold(true);

                // Apply background color to header without custom borders
                $event->sheet->getStyle('A1:X1')->applyFromArray([
                    'fill' => [
                        'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                        'color' => ['argb' => 'FFE6E6E6'], // Light gray background
                    ],
                    'alignment' => [
                        'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
                    ],
                ]);

                // Set vertical alignment to middle for all cells
                $lastRow = count($this->rows) + 1;
                $event->sheet->getStyle('A1:X' . $lastRow)->getAlignment()->setVertical(
                    \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER
                );

                // Auto-size all columns to fit content
                foreach (range('A', 'X') as $column) {
                    $event->sheet->getColumnDimension($column)->setAutoSize(true);
                }
            },
        ];
    }
}
