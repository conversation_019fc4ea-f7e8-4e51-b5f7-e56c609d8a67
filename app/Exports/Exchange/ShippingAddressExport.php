<?php

namespace App\Exports\Exchange;

use App\Models\Address;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class ShippingAddressExport implements FromQuery, WithHeadings, WithMapping
{
    protected $address;

    public function __construct(Address $address)
    {
        $this->address = $address;
    }
    
    public function query()
    {
        // Return a query that contains only this address
        return Address::query()->where('id', $this->address->id);
    }

    public function headings(): array
    {
        return [
            'ergo_id',
            'name',
            'type',
            'adhoc_invoicing_address_id',
            'adhoc_shipping_address_id',
            'description',
            'street',
            'city',
            'state',
            'zip',
            'country',
            'preparation_type',
            'note',
            'facility_type',
        ];
    }

    public function map($address): array
    {
        return [
            $address->id, // ergo_id
            $address->name, // name
            $address->type?->value, // type
            $address->code_invoicing, // adhoc_invoicing_address_id
            $address->code_shipping, // adhoc_shipping_address_id
            $address->company, // description
            $address->street, // street
            $address->city, // city
            $address->state, // state
            $address->zip, // zip
            $address->country?->value, // country
            '', // preparation_type (not available in Address model)
            '', // note (not available in Address model)
            'CO', // facility_type
        ];
    }
}
