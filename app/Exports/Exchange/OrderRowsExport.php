<?php

namespace App\Exports\Exchange;

use App\Enums\AddonCodes;
use App\Models\Order\Order;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;

class OrderRowsExport implements FromCollection, WithHeadings, WithMapping
{
    protected $order;

    public function __construct(Order $order)
    {
        $this->order = $order;
    }

    public function collection()
    {
        $rows = collect();

        // Optimized query with comprehensive eager loading to prevent N+1 queries
        $cartGroups = $this->order->cartGroups()
            ->with([
                'orderRows' => function($query) {
                    $query->with([
                        'product' => function($q) {
                            $q->withTrashed()
                                ->with(['brand:id,name', 'collection:id,name']);
                        },
                        'customProduct:id,sku,description,brand_name'
                    ])->orderBy('sort');
                }
            ])
            ->orderBy('sort')
            ->get();

        // Pre-calculate net total to avoid multiple calculations
        $netTotal = $this->order->orderRows()
            ->with(['product', 'customProduct'])
            ->get()
            ->sum(function ($orderRow) {
                return $orderRow->rowFinalPrice;
            });

        foreach ($cartGroups as $group) {
            // Add group header row with special SKU
            $groupHeaderRow = (object) [
                'is_group_header' => true,
                'group_name' => $group->name,
                'cart_group_id' => $group->id,
                'product' => null,
                'position_id' => null,
                'quantity' => null,
                'selling_price_override' => null,
                'selling_price' => null,
                'discount_override' => null,
                'discount' => null,
                'variation' => null,
                'required_delivery_date' => null,
            ];

            $rows->push($groupHeaderRow);

            // Add all order rows for this group
            foreach ($group->orderRows as $orderRow) {
                $orderRow->is_group_header = false;
                $rows->push($orderRow);
            }
        }

        // Add addon rows at the end (use eager-loaded addons if available)
        $addons = $this->order->relationLoaded('addons') 
            ? $this->order->addons 
            : $this->order->addons()->get();

        foreach ($addons as $addon) {
            $addonRow = (object) [
                'is_addon' => true,
                'addon_code' => $addon->code,
                'addon_description' => $addon->description,
                'addon_price' => $netTotal * ($addon->percent / 100),
                'product' => null,
                'position_id' => null,
                'quantity' => null,
                'selling_price_override' => null,
                'selling_price' => null,
                'discount_override' => null,
                'discount' => null,
                'variation' => null,
                'required_delivery_date' => null,
            ];

            $rows->push($addonRow);
        }

        return $rows;
    }

    public function headings(): array
    {
        return [
            'ergo_id',
            'row_number',
            'date',
            'description',
            'order_code',
            'ergo_partner_id',
            'adhoc_partner_id',
            'payment_term',
            'vat_type',
            'ergo_invoicing_address_id',
            'adhoc_invoicing_address_id',
            'ergo_shipping_address_id',
            'adhoc_shipping_address_id',
            'position_id',
            'adhoc_sku',
            'row_description',
            'row_extra_description',
            'quantity',
            'price',
            'discount',
            'variation',
            'required_delivery_date',
            'adhoc_order_code',
            'note',
            'row_type',
            'bank_account',
        ];
    }

    public function map($row): array
    {
        static $rowNumber = 0;
        $rowNumber++;

        // Check if this is a group header row
        if (isset($row->is_group_header) && $row->is_group_header) {
            return [
                $this->order->id, // ergo_id
                $rowNumber, // row_number
                $this->order->date?->format('Y-m-d'), // date
                $this->order->description, // description
                $this->order->order_code, // order_code
                $this->order->partner_id, // ergo_partner_id
                '', // adhoc_partner_id (not available)
                $this->order->paymentTerm?->code, // payment_term
                $this->order->vat_type?->adhocCode(), // vat_type
                $this->order->invoicing_address_id, // ergo_invoicing_address_id
                $this->order->invoicingAddress?->code_invoicing, // adhoc_invoicing_address_id
                $this->order->shipping_address_id, // ergo_shipping_address_id
                $this->order->shippingAddress?->code_shipping, // adhoc_shipping_address_id
                '', // position_id (empty for group header)
                'stanza_wms', // adhoc_sku (special SKU for group header)
                $row->group_name, // row_description (group name for group header)
                '', // row_extra_description (empty for group header)
                '', // quantity (empty for group header)
                '', // price (empty for group header)
                '', // discount (empty for group header)
                '', // variation (empty for group header)
                '', // required_delivery_date (empty for group header)
                $this->order->order_code_type->getAdhocCodeFromString($this->order->order_code_type->value), // adhoc_order_code
                '', // note (not available)
                'X', // row_type
                'BCC', // bank_account
            ];
        }

        // Check if this is an addon row
        if (isset($row->is_addon) && $row->is_addon) {
            return [
                $this->order->id, // ergo_id
                $rowNumber, // row_number
                $this->order->date?->format('Y-m-d'), // date
                $this->order->description, // description
                $this->order->order_code, // order_code
                $this->order->partner_id, // ergo_partner_id
                '', // adhoc_partner_id (not available)
                $this->order->paymentTerm?->code, // payment_term
                $this->order->vat_type?->adhocCode(), // vat_type
                $this->order->invoicing_address_id, // ergo_invoicing_address_id
                $this->order->invoicingAddress?->code_invoicing, // adhoc_invoicing_address_id
                $this->order->shipping_address_id, // ergo_shipping_address_id
                $this->order->shippingAddress?->code_shipping, // adhoc_shipping_address_id
                '', // position_id (empty for addon)
                AddonCodes::tryFrom($row->addon_code)?->adhocCode() ?? '', // adhoc_sku (addon code)
                $row->addon_description, // row_description (addon description)
                '', // row_extra_description (empty for addon)
                '1', // quantity
                number_format($row->addon_price, 2, '.', ''), // price (calculated addon price)
                '', // discount (empty for addon)
                '', // variation (empty for addon)
                '', // required_delivery_date (empty for addon)
                $this->order->order_code_type->getAdhocCodeFromString($this->order->order_code_type->value), // adhoc_order_code
                '', // note (not available)
                'X', // row_type
                'BCC', // bank_account
            ];
        }

        // Regular order row - optimized to use eager-loaded relationships
        return [
            $this->order->id, // ergo_id
            $rowNumber, // row_number
            $this->order->date?->format('Y-m-d'), // date
            $this->order->description, // description
            $this->order->order_code, // order_code
            $this->order->partner_id, // ergo_partner_id
            '', // adhoc_partner_id (not available)
            $this->order->paymentTerm?->code, // payment_term
            $this->order->vat_type?->adhocCode(), // vat_type
            $this->order->invoicing_address_id, // ergo_invoicing_address_id
            $this->order->invoicingAddress?->code_invoicing, // adhoc_invoicing_address_id
            $this->order->shipping_address_id, // ergo_shipping_address_id
            $this->order->shippingAddress?->code_shipping, // adhoc_shipping_address_id
            $row->position_id, // position_id
            $row->product?->adhoc_sku ?? '', // adhoc_sku (using eager-loaded product)
            $row->product?->description ?? '', // row_description (using eager-loaded product)
            $row->product?->extra_description ?? '', // row_extra_description (using eager-loaded product)
            $row->quantity, // quantity
            number_format($row->selling_price_override ?? $row->selling_price, 2, '.', ''), // price
            ($row->discount_override ?? $row->discount) ? '-' . ($row->discount_override ?? $row->discount) : '', // discount
            $row->variation ? (string) $row->variation : '', // variation
            $row->required_delivery_date?->format('Y-m-d'), // required_delivery_date
            $this->order->order_code_type->getAdhocCodeFromString($this->order->order_code_type->value), // adhoc_order_code
            '', // note (not available)
            'X', // row_type
            'BCC', // bank_account
        ];
    }
}
