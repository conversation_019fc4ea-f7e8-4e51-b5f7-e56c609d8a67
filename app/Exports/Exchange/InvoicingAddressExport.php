<?php

namespace App\Exports\Exchange;

use App\Models\Address;
use App\Models\Order\Order;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;

class InvoicingAddressExport implements FromQuery, WithHeadings, WithMapping
{
    protected $address;
    protected $order;

    public function __construct(Address $address, Order $order)
    {
        $this->address = $address;
        $this->order = $order;
    }
    
    public function query()
    {
        // Return a query that contains only this address with related data
        return Address::query()
            ->with(['addressable.paymentTerm'])
            ->where('id', $this->address->id);
    }

    public function headings(): array
    {
        return [
            'ergo_id',
            'name',
            'type',
            'adhoc_invoicing_address_id',
            'company',
            'vat_number',
            'fisacal_code',
            'sdi_code',
            'street',
            'city',
            'state',
            'zip',
            'country',
            'email',
            'payment_term',
            'commercial_category',
            'language',
            'client_clustering',
            'accounting_category',
        ];
    }

    public function map($address): array
    {
        $addressable = $address->addressable;

        return [
            $address->id, // ergo_id
            $address->name, // name
            $address->type?->value, // type
            $address->code_invoicing, // adhoc_invoicing_address_id
            $address->company, // company
            $address->vat_number, // vat_number
            $address->fiscal_code, // fisacal_code
            $address->sdi_code, // sdi_code
            $address->street, // street
            $address->city, // city
            $address->state, // state
            $address->zip, // zip
            $address->country?->value, // country
            $addressable?->email, // email
            $this->order?->paymentTerm?->code, // payment_term
            $addressable?->commercial_category?->code, // commercial_category
            $address->country?->value === 'ITA' ? 'IT' : 'IN', // language
            $address->country?->value === 'ITA' ? '1400' : '1410', // client_clustering
            'CF', // accounting_category
        ];
    }
}
