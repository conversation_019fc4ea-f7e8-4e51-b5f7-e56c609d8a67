<div class="flex min-h-screen max-w-lg mx-auto flex-col justify-center px-6 py-12 lg:px-8">
    <form wire:submit="login">
        <flux:card class="space-y-6">

            <div class="flex flex-col text-center">
                <flux:heading level="3">Sign in - {{ config('app.name') }}</flux:heading>
                <flux:subheading>Log in to access your datas.</flux:subheading>
            </div>

            <div class="space-y-6">
                <flux:input wire:model="email" label="Email" type="email" placeholder="Your email address" />

                <flux:field>
                    <flux:label class="flex justify-between">
                        Password
                    </flux:label>

                    <flux:input wire:model="password" type="password" placeholder="Your password" />

                    <flux:error name="password" />
                </flux:field>
            </div>
            <div class="space-y-2">
                <flux:button type="submit" variant="primary" class="w-full">Log in</flux:button>
            </div>

            <flux:separator text="or" />

            <div>
                <flux:button variant="filled" class="w-full">Contact us to ask for access.</flux:button>
            </div>
        </flux:card>
    </form>
</div>
