<div>
    <flux:modal.trigger name="open-ticket">
        <flux:button variant="ghost" size="{{ $size ?? 'base' }}" icon="question-mark-circle" square tooltip="Open ticket"/>
    </flux:modal.trigger>

    <flux:modal name="open-ticket" class="w-11/12 md:w-2/3" :dismissible="false">
        <div class="space-y-6">
            <div>
                <flux:heading size="lg">Create ticket</flux:heading>
                <flux:text class="mt-2">Create a new ticket to admin about a problem with this resource</flux:text>
            </div>

            <flux:badge icon="document-text">
                {{ $resourcePrefixName }} {{ $resourceValue ? ('#' . $resourceValue->id) : '' }}
            </flux:badge>

            <form wire:submit="create" class="flex flex-col gap-6">
                <flux:input label="Title" placeholder="Ticket title" wire:model="form.title" type="text"
                            variant="filled"/>
                <flux:textarea label="Description (optional)" placeholder="Describe your issue"
                               wire:model="form.description"
                               rows="6"/>

                <div class="flex gap-4 justify-end">
                    <flux:spacer/>

                    <flux:modal.close>
                        <flux:button size="sm" variant="ghost">Cancel</flux:button>
                    </flux:modal.close>

                    <flux:button size="sm" type="submit" variant="primary">Create ticket</flux:button>
                </div>
            </form>

        </div>
    </flux:modal>

</div>
