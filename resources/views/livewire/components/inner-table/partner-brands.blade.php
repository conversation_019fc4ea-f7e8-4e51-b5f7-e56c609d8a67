<div>
    {{-- Brands Listing --}}
    <div class="flex flex-col">
        <div class="flex flex-col">
            <div class="flex flex-col mb-6">
                <flux:heading size="lg" level="1">Brands</flux:heading>
                <flux:subheading size="md">Here's the brands for
                    the {{ $resourceNameSingular }}</flux:subheading>
            </div>
            <div class="flex justify-between gap-2">
                <div class="grow">
                    <flux:input wire:model.live="search" placeholder="Search..." clearable size="sm"/>
                </div>
                <div class="flex items-center gap-2">
                    @can('update', $resourceValue)
                        @can('read_brands')
                            @can('write_partner_discount_groups')
                                <flux:button wire:click="attachAllBrands"
                                             wire:confirm="Are you sure you want to attach all brands?"
                                             size="sm">Attach all brands
                                </flux:button>

                                <flux:modal.trigger name="attach-brand">
                                    <flux:button size="sm">Attach brand</flux:button>
                                </flux:modal.trigger>
                            @endcan
                        @endcan
                    @endcan
                </div>
            </div>
        </div>

        <div class="mt-6">
            @if ($this->brands->isEmpty())
                <flux:subheading class="flex justify-center pb-4">No results.</flux:subheading>
            @else
                <flux:table :paginate="$this->brands">
                    <flux:table.columns>
                        @can('view_brands_image')
                            <flux:table.column>Image</flux:table.column>
                        @endcan
                        @can('view_brands_name')
                            <flux:table.column>Brand Name</flux:table.column>
                        @endcan
                        @can('view_brands_price_range')
                            <flux:table.column>Price Range</flux:table.column>
                        @endcan
                        @can('view_brands_rating')
                            <flux:table.column>Rating</flux:table.column>
                        @endcan
                        @can('view_brands_partnership_level')
                            <flux:table.column>Partnership</flux:table.column>
                        @endcan
                        @can('view_brands_lead_time')
                            <flux:table.column>Lead Time (Weeks)</flux:table.column>
                        @endcan
                    </flux:table.columns>

                    <flux:table.rows>
                        @foreach ($this->brands as $brand)
                            <flux:table.row :key="$brand->id">
                                @can('view_brands_image')
                                    <flux:table.cell>
                                        @if ($brand->image && Storage::disk(config('filesystems.public'), 'brands')->exists($brand->image))
                                            <img
                                                src="{{ Storage::disk(config('filesystems.public'))->url($brand->image) }}"
                                                alt="{{ $brand->name }}" class="h-6"/>
                                        @elseif ($brand->image)
                                            <flux:tooltip content="Possible broken link." position="left">
                                                <flux:icon.exclamation-triangle variant="outline"
                                                                                class="text-amber-500 dark:text-amber-300"/>
                                            </flux:tooltip>
                                        @else
                                            <flux:tooltip content="No image uploaded." position="left">
                                                <flux:icon.information-circle variant="outline"
                                                                              class="text-grey-500 dark:text-grey-300"/>
                                            </flux:tooltip>
                                        @endif
                                    </flux:table.cell>
                                @endcan
                                @can('view_brands_name')
                                    <flux:table.cell variant="strong">
                                        <a href="{{ route('brands.show', $brand->id) }}"
                                           wire:navigate>{{ $brand->name }}</a>
                                    </flux:table.cell>
                                @endcan
                                @can('view_brands_price_range')
                                    <flux:table.cell>{{ $brand->price_range ? $brand->price_range->label() : '-' }}</flux:table.cell>
                                @endcan
                                @can('view_brands_rating')
                                    <flux:table.cell>{{ $brand->rating ? $brand->rating->label() : '-' }}</flux:table.cell>
                                @endcan
                                @can('view_brands_partnership_level')
                                    <flux:table.cell>
                                        <flux:badge
                                            color="{{ $brand->partnership_level ? $brand->partnership_level->color() : 'grey' }}"
                                            size="sm"
                                            inset>{{ $brand->partnership_level ? $brand->partnership_level->label() : '-' }}</flux:badge>
                                    </flux:table.cell>
                                @endcan
                                @can('view_brands_lead_time')
                                    <flux:table.cell>{{ $brand->lead_time }}</flux:table.cell>
                                @endcan

                                <flux:table.cell class="flex justify-end items-center gap-2">
                                    <flux:button href="{{ $brand->catalogs }}" target="_blank" size="sm"
                                                 icon="book-open" tooltip="Discover catalog"></flux:button>
                                    <flux:button size="sm" icon="folder-plus" tooltip="Request access" variant="subtle"
                                                 disabled></flux:button>
                                    <flux:dropdown>
                                        <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions"
                                                     variant="ghost"></flux:button>

                                        <flux:menu>
                                            @can('view', $brand)
                                                <flux:menu.item wire:click="view({{ $brand->id }})" icon="eye">View
                                                </flux:menu.item>
                                            @endcan
                                            {{-- <flux:menu.item wire:click="edit({{ $brand->id }})" icon="pencil-square">Edit</flux:menu.item> --}}

                                            @can('update', $resourceValue)
                                                @can('read_brands')
                                                    <flux:menu.separator/>
                                                    <flux:menu.item
                                                        wire:confirm="Are you sure you want to detach this brand? This will not delete the brand itself."
                                                        wire:click="detach({{ $brand->id }})" variant="danger"
                                                        icon="x-mark">Detach
                                                    </flux:menu.item>
                                                @endcan
                                            @endcan
                                        </flux:menu>
                                    </flux:dropdown>
                                </flux:table.cell>
                            </flux:table.row>
                        @endforeach
                    </flux:table.rows>
                </flux:table>
            @endif
        </div>
    </div>

    @can('update', $resourceValue)
        @can('read_brands')
            @can('write_partner_discount_groups')
                {{-- Attach Brand - Modal --}}
                <flux:modal name="attach-brand" variant="flyout" class="w-full md:w-2/3 lg:w-1/3 space-y-12"
                            @close="resetForm"
                            :dismissible="false">
                    <div>
                        <flux:heading size="lg">Attach brands and discount groups</flux:heading>
                        <flux:subheading>Attach brands and discount groups to the {{ $resourceNameSingular }}.
                        </flux:subheading>
                    </div>

                    <form wire:submit="attach" class="flex flex-col gap-6">
                        <flux:select wire:model.live="selectedBrandsIds" variant="listbox" multiple searchable clearable
                                     placeholder="Choose brands..." label="Brands"
                                     description="This will be publicly displayed.">
                            @foreach($brands as $brand)
                                <flux:select.option value="{{ $brand->id }}">{{ $brand->name }}</flux:select.option>
                            @endforeach
                        </flux:select>

                        @if (count($selectedBrandsIds) > 0 && $selectedBrands)
                            <flux:separator text="set discount groups"/>
                        @endif

                        @if (count($selectedBrandsIds) > 0 && $selectedBrands)
                            <div class="flex flex-col gap-8">
                                @foreach ($selectedBrands as $brand)
                                    <div wire:key="{{ $brand->id }}" class="flex flex-col gap-4">
                                        <div>
                                            <flux:heading size="lg">Brand: {{ $brand->name }}</flux:heading>
                                        </div>

                                        @forelse ($brand->discountGroups as $discountGroup)
                                            <flux:field wire:key="{{ $discountGroup->id }}">
                                                <flux:description class="mb-2">Discount
                                                    group: {{ $discountGroup->code . ' - ' . $discountGroup->description }}</flux:description>

                                                <flux:input.group>
                                                    <flux:input
                                                        wire:model="discountValues.{{ $brand->id }}.{{ $discountGroup->id }}"
                                                        type="number"
                                                        step="0.01"
                                                        icon="receipt-percent"
                                                        placeholder="Discount Percentage"
                                                        variant="filled"/>
                                                    <flux:input.group.suffix> &le; {{ $discountGroup->discount }}%
                                                    </flux:input.group.suffix>
                                                </flux:input.group>

                                                <flux:error
                                                    name="discountValues.{{ $brand->id }}.{{ $discountGroup->id }}"/>
                                            </flux:field>
                                        @empty
                                            <flux:subheading>No discount groups found.</flux:subheading>
                                        @endforelse
                                    </div>
                                @endforeach
                            </div>
                        @endif

                        <div class="flex">
                            <flux:spacer/>
                            @can('update', $resourceValue)
                                <flux:button type="submit" variant="primary" size="sm">Attach brands</flux:button>
                            @endcan
                        </div>
                    </form>
                </flux:modal>
            @endcan
        @endcan
    @endcan
</div>
