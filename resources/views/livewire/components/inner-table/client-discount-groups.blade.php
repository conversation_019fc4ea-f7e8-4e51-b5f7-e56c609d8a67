<div>
    {{-- Discount Groups Listing --}}
    <div class="flex flex-col mt-12">
        <div class="flex flex-col">
            <div class="flex flex-col mb-6">
                <flux:heading size="lg" level="1">Discount Groups</flux:heading>
                <flux:subheading size="md">Here's the discount groups for
                    the {{ $resourceNameSingular }}</flux:subheading>
            </div>
            <div class="flex justify-between gap-2">
                <div class="grow">
                    <flux:input wire:model.live="search" placeholder="Search..." clearable size="sm"/>
                </div>
                <div class="flex items-center gap-2">
                    @can('update', $resourceValue)
                        @can('read_discount_groups')
                            @can('write_client_discount_groups')
                                <flux:button wire:click="attachAllDiscountGroups"
                                             wire:confirm="Are you sure you want to attach all discount groups?" size="sm">
                                    Attach all discount groups
                                </flux:button>

                                <flux:modal.trigger name="attach-discount-group">
                                    <flux:button size="sm">Attach discount group</flux:button>
                                </flux:modal.trigger>
                            @endcan
                        @endcan
                    @endcan
                </div>
            </div>
        </div>

        <div class="mt-6">
            @if ($this->discountGroups->isEmpty())
                <flux:subheading class="flex justify-center pb-4">No results.</flux:subheading>
            @else
                <flux:table :paginate="$this->discountGroups">
                    <flux:table.columns>
                        <flux:table.column>Code</flux:table.column>
                        <flux:table.column>Brand</flux:table.column>
                        <flux:table.column>Discount in %</flux:table.column>
                        <flux:table.column>Description</flux:table.column>
                    </flux:table.columns>

                    <flux:table.rows>
                        @foreach ($this->discountGroups as $discountGroup)
                            <flux:table.row>
                                <flux:table.cell>{{ $discountGroup->code }}</flux:table.cell>
                                <flux:table.cell variant="strong">
                                    @if ($discountGroup->brand)
                                        <a href="{{ route('brands.show', $discountGroup->brand->id) }}"
                                           wire:navigate>{{ $discountGroup->brand->name }}</a>
                                    @else
                                        -
                                    @endif
                                </flux:table.cell>
                                <flux:table.cell>{{ $discountGroup->pivot->discount }}</flux:table.cell>
                                <flux:table.cell>{{ $discountGroup->description }}</flux:table.cell>

                                <flux:table.cell class="flex justify-end">
                                    <flux:dropdown>
                                        <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions"
                                                     variant="ghost"></flux:button>

                                        <flux:menu>
                                            @can('view', $discountGroup)
                                                <flux:menu.item wire:click="view({{ $discountGroup->id }})" icon="eye">
                                                    View
                                                </flux:menu.item>
                                            @endcan
                                            @can('read_discount_groups')
                                                @can('write_client_discount_groups')
                                                    @can('update', $discountGroup)
                                                        <flux:menu.item wire:click="edit({{ $discountGroup->id }})"
                                                                        icon="pencil-square">Edit
                                                        </flux:menu.item>
                                                    @endcan
                                                    @can('update', $resourceValue)
                                                        @can('read_client_discount_groups')
                                                            <flux:menu.separator/>
                                                            <flux:menu.item
                                                                wire:confirm="Are you sure you want to detach this discount group? This will not delete the discount group itself."
                                                                wire:click="detach({{ $discountGroup->id }})"
                                                                variant="danger"
                                                                icon="x-mark">Detach
                                                            </flux:menu.item>
                                                        @endcan
                                                    @endcan
                                                @endcan
                                            @endcan
                                        </flux:menu>
                                    </flux:dropdown>
                                </flux:table.cell>
                            </flux:table.row>
                        @endforeach
                    </flux:table.rows>
                </flux:table>
            @endif
        </div>
    </div>

    @can('update', $resourceValue)
        @can('read_discount_groups')
            @can('write_client_discount_groups')
                {{-- Attach Discount Group - Modal --}}
                <flux:modal name="attach-discount-group" variant="flyout" class="w-full md:w-2/3 lg:w-1/3 space-y-12"
                            @close="resetForm" :dismissible="false">
                    <div>
                        <flux:heading size="lg">Attach discount group</flux:heading>
                        <flux:subheading>Attach discount group to the {{ $resourceNameSingular }}.</flux:subheading>
                    </div>
                    <form wire:submit="attach" class="flex flex-col gap-4">
                        <flux:select searchable wire:model="selectedDiscountGroupId" variant="listbox" searchable
                                     clearable
                                     placeholder="Choose discount group..." label="Discount Group"
                                     description="Select the discount group.">
                            @foreach($discountGroups as $discountGroup)
                                <flux:select.option
                                    value="{{ $discountGroup->id }}">{{ $discountGroup->code . ' - ' . $discountGroup->description }}</flux:select.option>
                            @endforeach
                        </flux:select>
                        <flux:input wire:model="discount" type="number" step="0.01" icon="receipt-percent"
                                    placeholder="Discount Percentage" variant="filled" label="Discount (%)"
                                    description="Enter the discount percentage."/>

                        <div class="flex">
                            <flux:spacer/>
                            <flux:button type="submit" variant="primary" size="sm">Attach discount group</flux:button>
                        </div>
                    </form>
                </flux:modal>

                {{-- Edit Discount Group - Modal --}}
                <flux:modal name="edit-discount-group" variant="flyout" class="w-full md:w-2/3 lg:w-1/3 space-y-12"
                            @close="resetForm" :dismissible="false">
                    <div>
                        <flux:heading size="lg">Edit discount group</flux:heading>
                        <flux:subheading>Edit discount group for the {{ $resourceNameSingular }}.</flux:subheading>
                    </div>
                    <form wire:submit="update" class="flex flex-col gap-4">
                        <flux:select disabled wire:model="selectedDiscountGroupId" variant="listbox"
                                     placeholder="Choose discount group..." label="Discount Group"
                                     description="Select the discount group.">
                            <flux:select.option
                                value="{{ $selectedDiscountGroup?->id }}">{{ $selectedDiscountGroup?->code . ' - ' . $selectedDiscountGroup?->description }}</flux:select.option>
                        </flux:select>
                        <flux:input wire:model="discount" type="number" step="0.01" icon="receipt-percent"
                                    placeholder="Discount Percentage" variant="filled" label="Discount (%)"
                                    description="Enter the discount percentage."/>

                        <div class="flex">
                            <flux:spacer/>
                            <flux:button type="submit" variant="primary" size="sm">Edit discount group</flux:button>
                        </div>
                    </form>
                </flux:modal>
            @endcan
        @endcan
    @endcan

</div>
