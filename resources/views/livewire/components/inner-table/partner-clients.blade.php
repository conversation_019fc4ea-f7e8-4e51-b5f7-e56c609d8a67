<div>
    {{-- Clients Listing --}}
    <div class="flex flex-col">
        <div class="flex flex-col">
            <div class="flex flex-col mb-6">
                <flux:heading size="lg" level="1">Clients</flux:heading>
                <flux:subheading size="md">Here's the clients for
                    the {{ $resourceNameSingular }}</flux:subheading>
            </div>
            <div class="flex justify-between gap-2">
                <div class="grow">
                    <flux:input wire:model.live="search" placeholder="Search..." clearable size="sm"/>
                </div>
                <div class="flex items-center gap-2">
                    @can('update', $resourceValue)
                        @can('read_clients')
                            <flux:modal.trigger name="associate-client">
                                <flux:button size="sm">Associate client</flux:button>
                            </flux:modal.trigger>
                        @endcan
                    @endcan
                </div>
            </div>
        </div>

        <div class="mt-6">
            @if ($this->clients->isEmpty())
                <flux:subheading class="flex justify-center pb-4">No results.</flux:subheading>
            @else
                <flux:table :paginate="$this->clients">
                    <flux:table.columns>
                        @can('view_clients_company')
                            <flux:table.column>Name</flux:table.column>
                        @endcan
                        @can('view_clients_type')
                            <flux:table.column>Type</flux:table.column>
                        @endcan
                        <flux:table.column>Code</flux:table.column>
                        @can('view_clients_email')
                            <flux:table.column>Email</flux:table.column>
                        @endcan
                    </flux:table.columns>

                    <flux:table.rows>
                        @foreach ($this->clients as $client)
                            <flux:table.row>
                                @can('view_clients_company')
                                    <flux:table.cell variant="strong">
                                        @can('view', $client)
                                            <a href="{{ route('clients.show', $client->id) }}"
                                               wire:navigate>{{ $client->company }}</a>
                                        @else
                                            {{ $client->company }}
                                        @endcan
                                    </flux:table.cell>
                                @endcan
                                @can('view_clients_type')
                                    <flux:table.cell>
                                        <flux:badge icon="{{ $client->type->icon() }}"
                                                    color="{{ $client->type->color() }}"
                                                    size="sm" inset>{{ $client->type->label() }}</flux:badge>
                                    </flux:table.cell>
                                @endcan
                                <flux:table.cell>{{ $client->code }}</flux:table.cell>
                                @can('view_clients_email')
                                    <flux:table.cell>{{ $client->email }}</flux:table.cell>
                                @endcan

                                <flux:table.cell class="flex justify-end">
                                    <flux:dropdown>
                                        <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions"
                                                     variant="ghost"></flux:button>

                                        <flux:menu>
                                            @can('view', $client)
                                                <flux:menu.item wire:click="view({{ $client->id }})" icon="eye">View
                                                </flux:menu.item>
                                            @endcan
                                            @can('update', $client)
                                                <flux:menu.item wire:click="edit({{ $client->id }})"
                                                                icon="pencil-square">Edit
                                                </flux:menu.item>
                                            @endcan
                                            @can('update', $resourceValue)
                                                @can('read_clients')
                                                    <flux:menu.separator/>
                                                    <flux:menu.item
                                                        wire:confirm="Are you sure you want to dissociate this client? This will not delete the client itself."
                                                        wire:click="dissociate({{ $client->id }})" variant="danger"
                                                        icon="x-mark">Dissociate
                                                    </flux:menu.item>
                                                @endcan
                                            @endcan
                                        </flux:menu>
                                    </flux:dropdown>
                                </flux:table.cell>
                            </flux:table.row>
                        @endforeach
                    </flux:table.rows>
                </flux:table>
            @endif
        </div>
    </div>

    @can('update', $resourceValue)
        @can('read_clients')
            {{-- Associate Client - Modal --}}
            <flux:modal name="associate-client" variant="flyout" class="space-y-12" x-data="{ selectedClientId: null }"
                        x-on:close="selectedClientId = null" :dismissible="false">
                <div>
                    <flux:heading size="lg">Associate client</flux:heading>
                    <flux:subheading>Associate a client to the partner.</flux:subheading>
                </div>

                <flux:select searchable x-model="selectedClientId" variant="listbox" searchable clearable
                             placeholder="Choose client...">
                    @foreach($clients as $client)
                        <flux:select.option value="{{ $client->id }}">{{ $client->company }}</flux:select.option>
                    @endforeach
                </flux:select>

                <div class="flex">
                    <flux:spacer/>
                    @can('update', $resourceValue)
                        <flux:button @click="$wire.associate(selectedClientId)" type="submit" variant="primary"
                                     size="sm">
                            Associate client
                        </flux:button>
                    @endcan
                </div>
            </flux:modal>
        @endcan
    @endcan
</div>
