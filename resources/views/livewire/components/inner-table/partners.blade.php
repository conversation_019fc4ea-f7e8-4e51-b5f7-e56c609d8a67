<div>
    {{-- Partners Listing --}}
    <div class="flex flex-col">
        <div class="flex flex-col">
            <div class="flex flex-col mb-6">
                <flux:heading size="lg" level="1">Partners</flux:heading>
                <flux:subheading size="md">Here's the partners for
                    the {{ $resourceNameSingular }}</flux:subheading>
            </div>
            <div class="flex justify-between gap-2">
                <div class="grow">
                    <flux:input wire:model.live="search" placeholder="Search..." clearable size="sm"/>
                </div>
                <div class="flex items-center gap-2">
                    @can('update', $resourceValue)
                        @can('read_partners')
                            <flux:modal.trigger name="attach-partner">
                                <flux:button size="sm">Attach partner</flux:button>
                            </flux:modal.trigger>
                        @endcan
                    @endcan
                </div>
            </div>
        </div>

        <div class="mt-6">
            @if ($this->partners->isEmpty())
                <flux:subheading class="flex justify-center pb-4">No results.</flux:subheading>
            @else
                <flux:table :paginate="$this->partners">
                    <flux:table.columns>
                        @can('view_partners_company')
                            <flux:table.column>Name</flux:table.column>
                        @endcan
                        @can('view_partners_type')
                            <flux:table.column>Type</flux:table.column>
                        @endcan
                        @can('view_partners_internal_referent_id')
                            <flux:table.column>Internal Ref.</flux:table.column>
                        @endcan
                        @can('view_partners_commercial_category')
                            <flux:table.column>Commercial Category</flux:table.column>
                        @endcan
                        @can('view_partners_priority')
                            <flux:table.column>Priority</flux:table.column>
                        @endcan
                    </flux:table.columns>

                    <flux:table.rows>
                        @foreach ($this->partners as $partner)
                            <flux:table.row>
                                @can('view_partners_company')
                                    <flux:table.cell variant="strong">
                                        <a href="{{ route('partners.show', $partner->id) }}"
                                           wire:navigate>{{ $partner->company }}</a>
                                    </flux:table.cell>
                                @endcan
                                @can('view_partners_type')
                                    <flux:table.cell>
                                        <flux:badge icon="{{ $partner->type->icon() }}"
                                                    color="{{ $partner->type->color() }}" size="sm"
                                                    inset>{{ $partner->type->label() }}</flux:badge>
                                    </flux:table.cell>
                                @endcan
                                @can('view_partners_internal_referent_id')
                                    <flux:table.cell>{{ $partner->internalReferent ? ($partner->internalReferent->first_name . ' ' . $partner->internalReferent->last_name) : '-' }}</flux:table.cell>
                                @endcan
                                @can('view_partners_commercial_category')
                                    <flux:table.cell>{{ $partner->commercial_category ? $partner->commercial_category->label() : '-' }}</flux:table.cell>
                                @endcan
                                @can('view_partners_priority')
                                    <flux:table.cell>{{ $partner->priority ? $partner->priority->label() : '-' }}</flux:table.cell>
                                @endcan
                                <flux:table.cell class="flex justify-end">
                                    <flux:dropdown>
                                        <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions"
                                                     variant="ghost"></flux:button>

                                        <flux:menu>
                                            @can('view', $partner)
                                                <flux:menu.item wire:click="view({{ $partner->id }})" icon="eye">View
                                                </flux:menu.item>
                                            @endcan
                                            @can('update', $partner)
                                                <flux:menu.item wire:click="edit({{ $partner->id }})"
                                                                icon="pencil-square">Edit
                                                </flux:menu.item>
                                            @endcan
                                            @can('update', $resourceValue)
                                                @can('read_partners')
                                                    <flux:menu.separator/>
                                                    <flux:menu.item
                                                        wire:confirm="Are you sure you want to detach this partner? This will not delete the partner itself."
                                                        wire:click="detach({{ $partner->id }})" variant="danger"
                                                        icon="x-mark">Detach
                                                    </flux:menu.item>
                                                @endcan
                                            @endcan
                                        </flux:menu>
                                    </flux:dropdown>
                                </flux:table.cell>
                            </flux:table.row>
                        @endforeach
                    </flux:table.rows>
                </flux:table>
            @endif
        </div>
    </div>

    @can('update', $resourceValue)
        @can('read_partners')
            {{-- Attach Partner - Modal --}}
            <flux:modal name="attach-partner" variant="flyout" class="space-y-12" x-data="{ selectedPartnerId: null }"
                        x-on:close="selectedPartnerId = null" :dismissible="false">
                <div>
                    <flux:heading size="lg">Attach partner</flux:heading>
                    <flux:subheading>Attach partner to the {{ $resourceNameSingular }}.</flux:subheading>
                </div>

                <flux:select searchable x-model="selectedPartnerId" variant="listbox" searchable clearable
                             placeholder="Choose partner...">
                    @foreach($partners as $partner)
                        <flux:select.option value="{{ $partner->id }}">{{ $partner->company }}</flux:select.option>
                    @endforeach
                </flux:select>

                <div class="flex">
                    <flux:spacer/>
                    @can('update', $resourceValue)
                        <flux:button @click="$wire.attach(selectedPartnerId)" type="submit" variant="primary" size="sm">
                            Attach
                            partner
                        </flux:button>
                    @endcan
                </div>
            </flux:modal>
        @endcan
    @endcan
</div>
