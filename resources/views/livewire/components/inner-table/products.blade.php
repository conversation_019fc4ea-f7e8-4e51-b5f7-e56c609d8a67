<div>
    {{-- Products Listing --}}
    <div class="flex flex-col">
        <div class="flex flex-col">
            <div class="flex flex-col mb-6">
                <flux:heading size="lg" level="1">Products</flux:heading>
                <flux:subheading size="md">Here's the products for the {{ $resourceNameSingular }}</flux:subheading>
            </div>
            <div class="flex justify-between gap-2">
                <div class="grow">
                    <flux:input wire:model.live="search" placeholder="Search..." clearable size="sm"/>
                </div>
                <div class="flex items-center gap-2">
                    {{-- Actions --}}
                </div>
            </div>
        </div>

        <div class="mt-6">
            @if ($this->products->isEmpty())
                <flux:subheading class="flex justify-center pb-4">No results.</flux:subheading>
            @else
                <flux:table :paginate="$this->products">
                    <flux:table.columns>
                        <flux:table.column>#</flux:table.column>
                        <flux:table.column>Image</flux:table.column>
                        <flux:table.column>SKU</flux:table.column>
                        <flux:table.column>EAN</flux:table.column>
                        <flux:table.column>Description</flux:table.column>
                        <flux:table.column>Brand</flux:table.column>
                        <flux:table.column>Collection</flux:table.column>
                        <flux:table.column>Variant of</flux:table.column>
                    </flux:table.columns>

                    <flux:table.rows>
                        @foreach ($this->products as $product)
                        <flux:table.row>
                            <flux:table.cell>{{ $product->id }}</flux:table.cell>
                            <flux:table.cell>
                                @if ($product->image)
                                    <flux:avatar size="sm" src="{{ Storage::disk(config('filesystems.public'))->url($product?->image) }}" />
                                @else
                                    <flux:tooltip content="No image uploaded." position="left">
                                        <flux:icon.information-circle variant="outline" class="text-grey-500 dark:text-grey-300" />
                                    </flux:tooltip>
                                @endif
                            </flux:table.cell>
                            <flux:table.cell variant="strong">
                                <a href="#" wire:navigate>{{ $product->sku }}</a>
                            </flux:table.cell>
                            <flux:table.cell>{{ $product->ean }}</flux:table.cell>
                            <flux:table.cell>{{ $product->description }}</flux:table.cell>
                            <flux:table.cell>{{ $product->brand ? $product->brand->name : '-' }}</flux:table.cell>
                            <flux:table.cell>{{ $product->collection ? $product->collection->name : '-' }}</flux:table.cell>
                            <flux:table.cell>
                                @if (Str::startsWith($product->parent_sku, 'solo_'))
                                    -
                                @else
                                    {{ $product->parent_sku }}
                                @endif
                            </flux:table.cell>

                            <flux:table.cell class="flex justify-end">
                                <flux:dropdown>
                                    {{-- <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions" variant="ghost"></flux:button>

                                    <flux:menu>
                                        <flux:menu.item wire:click="view({{ $client->id }})" icon="eye">View</flux:menu.item>
                                        <flux:menu.item wire:click="edit({{ $client->id }})" icon="pencil-square">Edit</flux:menu.item>

                                        <flux:menu.separator />

                                        <flux:menu.item wire:click="detach({{ $client->id }})" variant="danger" icon="link-slash">Detach</flux:menu.item>
                                    </flux:menu> --}}
                                </flux:dropdown>
                            </flux:table.cell>
                        </flux:table.row>
                        @endforeach
                    </flux:table.rows>
                </flux:table>
            @endif
        </div>
    </div>
</div>
