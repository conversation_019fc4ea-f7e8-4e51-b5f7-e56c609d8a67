<div>
    {{-- Collaboratos Listing --}}
    <div class="flex flex-col">
        <div class="flex flex-col">
            <div class="flex flex-col mb-6">
                <flux:heading size="lg" level="1">Collaborators</flux:heading>
                <flux:subheading size="md">Here's the collaborators for the {{ $resourceNameSingular }}</flux:subheading>
            </div>
            <div class="flex justify-between gap-2">
                <div class="grow">
                    <flux:input wire:model.live="search" placeholder="Search..." clearable size="sm"/>
                </div>
                <div class="flex items-center gap-2">
                    @can('create', $resourceValue )
                        <flux:modal.trigger name="create-collaborator">
                            <flux:button size="sm">Create collaborator</flux:button>
                        </flux:modal.trigger>
                    @endcan
                </div>
            </div>
        </div>

        <div class="mt-6">
            @if ($this->collaborators->isEmpty())
                <flux:subheading class="flex justify-center pb-4">No results.</flux:subheading>
            @else
                <flux:table :paginate="$this->collaborators">
                    <flux:table.columns>
                        <flux:table.column>Name</flux:table.column>
                        <flux:table.column>Position</flux:table.column>
                        <flux:table.column>User</flux:table.column>
                        <flux:table.column>Contact</flux:table.column>
                    </flux:table.columns>

                    <flux:table.rows>
                        @foreach ($this->collaborators as $collaborator)
                        <flux:table.row>
                            <flux:table.cell variant="strong">{{ $collaborator->name }}</flux:table.cell>
                            <flux:table.cell>{{ $collaborator->position ?? '-' }}</flux:table.cell>
                            <flux:table.cell>{{ $collaborator->user ? $collaborator->user->first_name . " " . $collaborator->user->last_name : '-' }}</flux:table.cell>
                            <flux:table.cell>{{ $collaborator->contact ? $collaborator->contact->name : '-' }}</flux:table.cell>

                            <flux:table.cell class="flex justify-end">
                                @can('create', $resourceValue )
                                    <flux:dropdown>
                                        <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions" variant="ghost"></flux:button>
                                            <x-inner-table.row-crud-menu :item="$collaborator" />
                                    </flux:dropdown>
                                @endcan

                            </flux:table.cell>
                        </flux:table.row>
                        @endforeach
                    </flux:table.rows>
                </flux:table>
            @endif
        </div>
    </div>

    {{-- View Collaborator - Modal --}}
    <flux:modal name="view-collaborator" @close="resetForm" variant="flyout" class="w-full md:w-2/3 lg:w-1/3 space-y-12" :dismissible="false">
        <div>
            <flux:heading size="lg">View collaborator</flux:heading>
            <flux:subheading>View collaborator for the {{ $resourceNameSingular }}.</flux:subheading>
        </div>

        <div class="flex flex-col gap-6">
            <flux:input wire:model="form.name" readonly type="text" placeholder="Mario Rossi" variant="filled" label="Name" description="This will be publicly displayed." />
            <flux:input wire:model="form.position" readonly type="text" placeholder="Sales manager" variant="filled" label="Position" description="This will be publicly displayed." badge="Optional" />

            <flux:select searchable wire:model="form.user_id" disabled variant="listbox" placeholder="Choose user..." label="User" description="This will be publicly displayed." badge="Optional">
                @foreach($users as $user)
                    <flux:select.option value="{{ $user->id }}">{{ $user->first_name . " " . $user->last_name }}</flux:select.option>
                @endforeach
            </flux:select>
            <flux:select searchable wire:model="form.contact_id" disabled variant="listbox" placeholder="Choose contact..." label="Contact" description="This will be publicly displayed." badge="Optional">
                @foreach($contacts as $contact)
                    <flux:select.option value="{{ $contact->id }}">{{ $contact->name }}</flux:select.option>
                @endforeach
            </flux:select>
        </div>
    </flux:modal>

    {{-- Create Collaborator - Modal --}}
    <flux:modal name="create-collaborator" @close="resetForm" variant="flyout" class="w-full md:w-2/3 lg:w-1/3 space-y-12" :dismissible="false">
        <div>
            <flux:heading size="lg">Create collaborator</flux:heading>
            <flux:subheading>Create collaborator for the {{ $resourceNameSingular }}.</flux:subheading>
        </div>

        <form wire:submit="create" class="flex flex-col gap-6">
            <flux:input wire:model="form.name" type="text" placeholder="Mario Rossi" variant="filled" label="Name" description="This will be publicly displayed." />
            <flux:input wire:model="form.position" type="text" placeholder="Sales manager" variant="filled" label="Position" description="This will be publicly displayed." badge="Optional" />

            <flux:select searchable clearable wire:model="form.user_id" variant="listbox" placeholder="Choose user..." label="User" description="This will be publicly displayed." badge="Optional">
                @foreach($users as $user)
                    <flux:select.option value="{{ $user->id }}">{{ $user->first_name . " " . $user->last_name }}</flux:select.option>
                @endforeach
            </flux:select>
            <flux:select searchable clearable wire:model="form.contact_id" variant="listbox" placeholder="Choose contact..." label="Contact" description="This will be publicly displayed." badge="Optional">
                @foreach($contacts as $contact)
                    <flux:select.option value="{{ $contact->id }}">{{ $contact->name }}</flux:select.option>
                @endforeach
            </flux:select>

            <div class="flex">
                <flux:spacer />
                @can('create', $resourceValue )
                    <flux:button size="sm" type="submit" variant="primary">Create collaborator</flux:button>
                @endcan
            </div>
        </form>
    </flux:modal>

    {{-- Edit Collaborator - Modal --}}
    <flux:modal name="edit-collaborator" @close="resetForm" variant="flyout" class="w-full md:w-2/3 lg:w-1/3 space-y-12" :dismissible="false">
        <div>
            <flux:heading size="lg">Edit collaborator</flux:heading>
            <flux:subheading>Edit collaborator for the {{ $resourceNameSingular }}.</flux:subheading>
        </div>

        <form wire:submit="update" class="flex flex-col gap-6">
            <flux:input wire:model="form.name" type="text" placeholder="Mario Rossi" variant="filled" label="Name" description="This will be publicly displayed." />
            <flux:input wire:model="form.position" type="text" placeholder="Sales manager" variant="filled" label="Position" description="This will be publicly displayed." badge="Optional" />

            <flux:select searchable clearable wire:model="form.user_id" variant="listbox" placeholder="Choose user..." label="User" description="This will be publicly displayed." badge="Optional">
                @foreach($users as $user)
                    <flux:select.option value="{{ $user->id }}">{{ $user->first_name . " " . $user->last_name }}</flux:select.option>
                @endforeach
            </flux:select>
            <flux:select searchable clearable wire:model="form.contact_id" variant="listbox" placeholder="Choose contact..." label="Contact" description="This will be publicly displayed." badge="Optional">
                @foreach($contacts as $contact)
                    <flux:select.option value="{{ $contact->id }}">{{ $contact->name }}</flux:select.option>
                @endforeach
            </flux:select>

            <div class="flex">
                <flux:spacer />
                @can('update', $resourceValue )
                    <flux:button size="sm" type="submit" variant="primary">Edit collaborator</flux:button>
                @endcan
            </div>
        </form>
    </flux:modal>
</div>
