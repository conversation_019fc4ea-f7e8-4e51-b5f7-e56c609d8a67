<div>
    {{-- Discount Groups Listing --}}
    <div class="flex flex-col">
        <div class="flex flex-col">
            <div class="flex flex-col mb-6">
                <flux:heading size="lg" level="1">Discount Groups</flux:heading>
                <flux:subheading size="md">Here's the discount groups for
                    the {{ $resourceNameSingular }}</flux:subheading>
            </div>
            <div class="flex justify-between gap-2">
                <div class="grow">
                    <flux:input wire:model.live="search" placeholder="Search..." clearable size="sm"/>
                </div>
                <div class="flex items-center gap-2">
                    {{-- <flux:modal.trigger name="create-discount-group">
                        <flux:button size="sm">Create discount group</flux:button>
                    </flux:modal.trigger> --}}
                </div>
            </div>
        </div>

        <div class="mt-6">
            @if ($this->discountGroups->isEmpty())
                <flux:subheading class="flex justify-center pb-4">No results.</flux:subheading>
            @else
                <flux:table :paginate="$this->discountGroups">
                    <flux:table.columns>
                        <flux:table.column>#</flux:table.column>
                        @can('view_discount_groups_code')
                            <flux:table.column>Code</flux:table.column>
                        @endcan
                        @can('view_discount_groups_brand_id')
                            <flux:table.column>Brand</flux:table.column>
                        @endcan
                        @can('view_discount_groups_discount')
                            <flux:table.column>Discount in %</flux:table.column>
                        @endcan
                        @can('view_discount_groups_description')
                            <flux:table.column>Description</flux:table.column>
                        @endcan
                    </flux:table.columns>

                    <flux:table.rows>
                        @foreach ($this->discountGroups as $discountGroup)
                            <flux:table.row>
                                @can('view_discount_groups_code')
                                    <flux:table.cell>{{ $discountGroup->id }}</flux:table.cell>
                                @endcan
                                @can('view_discount_groups_brand_id')
                                    <flux:table.cell>{{ $discountGroup->code }}</flux:table.cell>
                                    <flux:table.cell variant="strong">
                                        @if ($discountGroup->brand)
                                            <a href="{{ route('brands.show', $discountGroup->brand->id) }}"
                                               wire:navigate>{{ $discountGroup->brand->name }}</a>
                                        @else
                                            -
                                        @endif
                                    </flux:table.cell>
                                @endcan
                                @can('view_discount_groups_discount')
                                    <flux:table.cell>{{ $discountGroup->discount }}</flux:table.cell>
                                @endcan
                                @can('view_discount_groups_description')
                                    <flux:table.cell>{{ $discountGroup->description }}</flux:table.cell>
                                @endcan
                                    
                                <flux:table.cell class="flex justify-end">
                                    <flux:dropdown>
                                        <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions"
                                                     variant="ghost"></flux:button>

                                        <flux:menu>
                                            @can('view', $discountGroup)
                                                <flux:menu.item wire:click="view({{ $discountGroup->id }})"
                                                                icon="eye">
                                                    View
                                                </flux:menu.item>
                                            @endcan
                                            @can('update', $discountGroup)
                                                <flux:menu.item wire:click="edit({{ $discountGroup->id }})"
                                                                icon="pencil-square">Edit
                                                </flux:menu.item>
                                            @endcan
                                            @can('delete', $discountGroup)
                                                <flux:menu.separator/>
                                                <flux:menu.item wire:click="delete({{ $discountGroup->id }})"
                                                                variant="danger" icon="trash">Delete
                                                </flux:menu.item>
                                            @endcan
                                        </flux:menu>
                                    </flux:dropdown>
                                </flux:table.cell>
                            </flux:table.row>
                        @endforeach
                    </flux:table.rows>
                </flux:table>
            @endif
        </div>
    </div>

</div>
