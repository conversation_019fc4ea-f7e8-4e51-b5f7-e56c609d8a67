<div>
    {{-- Orders Listing --}}
    <div class="flex flex-col mt-12">
        <div class="flex flex-col">
            <div class="flex flex-col mb-6">
                <flux:heading size="lg" level="1">Orders</flux:heading>
                <flux:subheading size="md">Here's the orders for
                    the {{ $resourceNameSingular }}</flux:subheading>
            </div>
            <div class="flex justify-between gap-2">
                <div class="grow">
                    <flux:input wire:model.live="search" placeholder="Search..." clearable size="sm"/>
                </div>
                <div class="flex items-center gap-2">
                    @can('create', \App\Models\Order\Order::class)
                        @if ($resourceType === 'Project')
                            <flux:button href="{{ route('orders.create', ['viaProject' => $resourceValue->id]) }}"
                                         wire:navigate size="sm">Create order
                            </flux:button>
                        @elseif ($resourceType === 'Client')
                            <flux:button href="{{ route('orders.create', ['viaClient' => $resourceValue->id]) }}"
                                         wire:navigate size="sm">Create order
                            </flux:button>
                        @else
                            <flux:button href="{{ route('orders.create') }}" wire:navigate size="sm">Create order
                            </flux:button>
                        @endif
                    @endcan
                </div>
            </div>
        </div>

        <div class="mt-6">
            @if ($this->orders->isEmpty())
                <flux:subheading class="flex justify-center pb-4">No results.</flux:subheading>
            @else
                <flux:table :paginate="$this->orders">
                    <flux:table.columns>
                        <flux:table.column>#</flux:table.column>
                        <flux:table.column>Code</flux:table.column>
                        <flux:table.column>Status</flux:table.column>
                        @can('view_orders_description')
                            <flux:table.column>Description</flux:table.column>
                        @endcan
                        @can('view_orders_internal_referent_id')
                            <flux:table.column>Internal Ref.</flux:table.column>
                        @endcan
                        @can('view_orders_client_id')
                            <flux:table.column>Client</flux:table.column>
                        @endcan
                        @can('view_orders_partner_id')
                            <flux:table.column>Partner</flux:table.column>
                        @endcan
                        <flux:table.column>Order Code</flux:table.column>
                    </flux:table.columns>

                    <flux:table.rows>
                        @foreach ($this->orders as $order)
                            <flux:table.row>
                                <flux:table.cell>{{ $order->id }}</flux:table.cell>
                                <flux:table.cell variant="strong">
                                    <a href="{{ route('orders.show', $order->id) }}"
                                       wire:navigate>{{ $order->code }}</a>
                                </flux:table.cell>
                                <flux:table.cell>
                                    <flux:badge icon="{{ $order->status->icon() }}"
                                                color="{{ $order->status->color() }}" size="sm"
                                                inset>{{ $order->status->label() }}</flux:badge>
                                </flux:table.cell>
                                @can('view_orders_description')
                                    <flux:table.cell>{{ $order->description }}</flux:table.cell>
                                @endcan
                                @can('view_orders_internal_referent_id')
                                    <flux:table.cell>{{ $order->internalReferent ? ($order->internalReferent->first_name . ' ' . $order->internalReferent->last_name) : '-' }}</flux:table.cell>
                                @endcan
                                @can('view_orders_client_id')
                                    <flux:table.cell variant="strong">
                                        @if ($order->client)
                                            @can('view', $order->client)
                                                <flux:button
                                                    href="{{ route('clients.show', $order->client->id ?? '') }}"
                                                    wire:navigate size="xs"
                                                    variant="ghost">{{ $order->client->company ?? '' }}</flux:button>
                                            @else
                                                {{ $order->client->company ?? '' }}
                                            @endcan
                                        @else
                                            -
                                        @endif
                                    </flux:table.cell>
                                @endcan
                                @can('view_orders_partner_id')
                                    <flux:table.cell variant="strong">
                                        @if ($order->partner)
                                            @can('view', $order->partner)
                                                <flux:button
                                                    href="{{ route('partners.show', $order->partner->id ?? '') }}"
                                                    wire:navigate size="xs"
                                                    variant="ghost">{{ $order->partner->company ?? '' }}</flux:button>
                                            @else
                                                {{ $order->partner->company ?? '' }}
                                            @endcan
                                        @else
                                            -
                                        @endif
                                    </flux:table.cell>
                                @endcan
                                <flux:table.cell>{{ $order->order_code }}</flux:table.cell>

                                <flux:table.cell class="flex justify-end">
                                    <flux:dropdown>
                                        <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions"
                                                     variant="ghost"></flux:button>

                                        <flux:menu>
                                            @can('view', $order )
                                                <flux:menu.item wire:click="view({{ $order->id }})" icon="eye">View
                                                </flux:menu.item>
                                            @endcan
                                            @can('update', $order )
                                                <flux:menu.item wire:click="edit({{ $order->id }})"
                                                                icon="pencil-square">Edit
                                                </flux:menu.item>
                                            @endcan

                                            @can('delete', $order )
                                                <flux:menu.separator/>
                                                <flux:menu.item
                                                    wire:confirm="Are you sure you want to delete this order? This action cannot be undone."
                                                    wire:click="delete({{ $order->id }})" variant="danger" icon="trash">
                                                    Delete
                                                </flux:menu.item>
                                            @endcan
                                        </flux:menu>
                                    </flux:dropdown>
                                </flux:table.cell>
                            </flux:table.row>
                        @endforeach
                    </flux:table.rows>
                </flux:table>
            @endif
        </div>
    </div>
</div>
