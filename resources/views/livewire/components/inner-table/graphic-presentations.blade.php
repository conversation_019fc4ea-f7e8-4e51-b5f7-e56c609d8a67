<div>
    {{-- Graphic Presentations Listing --}}
    <div class="flex flex-col">
        <div class="flex flex-col">
            <div class="flex flex-col mb-6">
                <flux:heading size="lg" level="1">Graphic Presentations</flux:heading>
                <flux:subheading size="md">Here's the graphic presentations for the {{ $resourceNameSingular }}</flux:subheading>
            </div>
            <div class="flex justify-between gap-2">
                <div class="grow">
                    <flux:input wire:model.live="search" placeholder="Search..." clearable size="sm"/>
                </div>
                <div class="flex items-center gap-2">
                    @can('create', $resourceValue)
                        <flux:modal.trigger name="create-graphic-presentation">
                                <flux:button size="sm">Create graphic presentation</flux:button>
                        </flux:modal.trigger>
                    @endcan
                </div>
            </div>
        </div>

        <div class="mt-6">
            @if ($this->assets->isEmpty())
                <flux:subheading class="flex justify-center pb-4">No results.</flux:subheading>
            @else
                <flux:table :paginate="$this->assets">
                    <flux:table.columns>
                        <flux:table.column>Name</flux:table.column>
                        <flux:table.column>Type</flux:table.column>
                        <flux:table.column>Related Order</flux:table.column>
                        <flux:table.column>Status</flux:table.column>
                    </flux:table.columns>

                    <flux:table.rows>
                        @foreach ($this->assets as $asset)
                        <flux:table.row>
                            <flux:table.cell variant="strong">{{ $asset->name }}</flux:table.cell>
                            <flux:table.cell>{{ $asset->type->label() }}</flux:table.cell>
                            <flux:table.cell>
                                @if ($asset->relatable)
                                    @can('view', $asset->relatable)
                                        <flux:link href="{{ route('orders.show', $asset->relatable->id) }}" wire:navigate>
                                            {{ $asset->relatable->description }}
                                        </flux:link>
                                    @else
                                        {{ $asset->relatable->description }}
                                    @endcan
                                @else
                                    -
                                @endif
                            </flux:table.cell>
                            <flux:table.cell><flux:badge icon="{{ $asset->status->icon() }}" color="{{ $asset->status->color() }}" size="sm" inset>{{ $asset->status->label() }}</flux:badge></flux:table.cell>

                            <flux:table.cell class="flex justify-end">
                                <flux:button href="{{ $asset->link }}" target="_blank" size="sm" icon="arrow-top-right-on-square" tooltip="Open Link" variant="ghost" x-bind:disabled="! '{{ $asset->link }}'"></flux:button>
                                <flux:button wire:click="downloadFile({{ $asset->id }})" size="sm" icon="folder-arrow-down" tooltip="Download File" variant="ghost" x-bind:disabled="! '{{ $asset->file }}'"></flux:button>

                                <flux:dropdown>
                                    <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions" variant="ghost"></flux:button>
                                    @can('create', $resourceValue)
                                        <x-inner-table.row-crud-menu :item="$asset" />
                                    @endcan

                                </flux:dropdown>
                            </flux:table.cell>
                        </flux:table.row>
                        @endforeach
                    </flux:table.rows>
                </flux:table>
            @endif
        </div>
    </div>

    {{-- View Graphic Presentation - Modal --}}
    <flux:modal name="view-graphic-presentation" @close="resetForm" variant="flyout" class="w-full md:w-2/3 lg:w-1/3 space-y-12" :dismissible="false">
        <div>
            <flux:heading size="lg">View graphic presentation</flux:heading>
            <flux:subheading>View graphic presentation for the {{ $resourceNameSingular }}.</flux:subheading>
        </div>

        <div class="flex flex-col gap-6">
            <flux:input wire:model="form.name" readonly type="text" placeholder="Mario Rossi" variant="filled" label="Name" description="This will be publicly displayed." />
            <flux:input wire:model="form.link" readonly copyable icon="link" variant="filled" label="Link" badge="Optional" description="This will be publicly displayed." />

            <flux:select wire:model="form.relatable_id" variant="listbox" disabled searchable clearable placeholder="Choose related order..." label="Related Order" description="This will be publicly displayed." badge="Optional">
                @foreach($orders as $order)
                    <flux:select.option value="{{ $order->id }}">{{ $order->description }}</flux:select.option>
                @endforeach
            </flux:select>
        </div>
    </flux:modal>

    {{-- Create Graphic Presentation - Modal --}}
    <flux:modal name="create-graphic-presentation" @close="resetForm" variant="flyout" class="w-full md:w-2/3 lg:w-1/3 space-y-12" :dismissible="false">
        <div>
            <flux:heading size="lg">Create graphic presentation</flux:heading>
            <flux:subheading>Create graphic presentation for the {{ $resourceNameSingular }}.</flux:subheading>
        </div>

        <form wire:submit="create" class="flex flex-col gap-6">
            <flux:input wire:model="form.name" type="text" placeholder="Mario Rossi" variant="filled" label="Name" description="This will be publicly displayed." />
            <flux:input wire:model="form.link" copyable icon="link" variant="filled" label="Link" badge="Optional" description="This will be publicly displayed." />

            <flux:select wire:model="form.relatable_id" variant="listbox" searchable clearable placeholder="Choose related order..." label="Related Order" description="This will be publicly displayed." badge="Optional">
                @foreach($orders as $order)
                    <flux:select.option value="{{ $order->id }}">{{ $order->description }}</flux:select.option>
                @endforeach
            </flux:select>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <flux:input wire:model="form.file" type="file" size="sm" label="File" description="Upload the file. (max 10MB)" badge="Optional" />

                @if ($this->form->file)
                    <x-files.preview-card unsetAction="unsetFile" />
                @endif
            </div>

            <div class="flex">
                <flux:spacer />
                <flux:button size="sm" type="submit" variant="primary">Create graphic presentation</flux:button>
            </div>
        </form>
    </flux:modal>

    {{-- Edit Graphic Presentation - Modal --}}
    <flux:modal name="edit-graphic-presentation" @close="resetForm" variant="flyout" class="w-full md:w-2/3 lg:w-1/3 space-y-12" :dismissible="false">
        <div>
            <flux:heading size="lg">Edit graphic presentation</flux:heading>
            <flux:subheading>Edit graphic presentation for the {{ $resourceNameSingular }}.</flux:subheading>
        </div>

        <form wire:submit="update" class="flex flex-col gap-6">
            <flux:input wire:model="form.name" type="text" placeholder="Mario Rossi" variant="filled" label="Name" description="This will be publicly displayed." />
            <flux:input wire:model="form.link" copyable icon="link" variant="filled" label="Link" badge="Optional" description="This will be publicly displayed." />

            <flux:select wire:model="form.relatable_id" variant="listbox" searchable clearable placeholder="Choose related order..." label="Related Order" description="This will be publicly displayed." badge="Optional">
                @foreach($orders as $order)
                    <flux:select.option value="{{ $order->id }}">{{ $order->description }}</flux:select.option>
                @endforeach
            </flux:select>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <flux:input wire:model="form.file" type="file" size="sm" label="File" description="Upload the file. (max 10MB)" badge="Optional" />

                @if ($this->form->file)
                    <x-files.preview-card unsetAction="unsetFile" />
                @endif
            </div>

            <div class="flex">
                <flux:spacer />
                <flux:button size="sm" type="submit" variant="primary">Edit graphic presentation</flux:button>
            </div>
        </form>
    </flux:modal>
</div>
