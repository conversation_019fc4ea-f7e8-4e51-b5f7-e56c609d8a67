<div>
    {{-- Order Rows Listing --}}
    <div class="flex flex-col mt-12">
        <div class="flex justify-between gap-4">
            <div class="flex flex-col basis-1/3">
                <flux:heading size="lg" level="1">Order Rows</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the order rows for the {{ $resourceNameSingular }}</flux:subheading>
            </div>
            <div class="flex flex-col basis-1/3">
            </div>
            <div class="flex items-center gap-2 basis-1/3">
                <flux:select searchable wire:model.live="selectedGroup" variant="listbox" size="sm" clearable placeholder="Filter by Group...">
                    @foreach ($groups as $group)
                        <flux:select.option value="{{ $group->id }}">{{ $group->name }}</flux:select.option>
                    @endforeach
                </flux:select>
            </div>
        </div>

        <div>
            @if ($this->orderRows->isEmpty())
                <flux:subheading class="flex justify-center pb-4">No results.</flux:subheading>
            @else
                <flux:table :paginate="$this->orderRows">
                    <flux:table.columns>
                        <flux:table.column>Pos. ID</flux:table.column>
                        <flux:table.column>Image</flux:table.column>
                        <flux:table.column>Details</flux:table.column>
                        <flux:table.column>Qty</flux:table.column>
                        <flux:table.column>Status</flux:table.column>
                        <flux:table.column>Prices</flux:table.column>
                        <flux:table.column>Final Price</flux:table.column>
                        <flux:table.column>Dates</flux:table.column>
                    </flux:table.columns>

                    <flux:table.rows>
                        @foreach ($this->orderRows as $row)
                        <flux:table.row>
                            <flux:table.cell>{{ $row->position_id ?? '-' }}</flux:table.cell>
                            <flux:table.cell>
                                @if ($row->product)
                                    <flux:avatar src="{{ Storage::disk(config('filesystems.public'))->url($row->product->image ?? '') }}" />
                                @elseif ($row->customProduct)
                                    <flux:avatar src="{{ Storage::disk(config('filesystems.public'))->url($row->customProduct->image ?? '') }}" />
                                @endif
                            </flux:table.cell>
                            <flux:table.cell x-data="{ open: false }">
                                <div class="flex items-center gap-2">
                                    <div>
                                        <flux:button x-on:click="open = !open" variant="ghost" size="sm" square x-bind:disabled="! '{{ $row->product?->hasModules() ?? false }}'">
                                            <flux:icon.information-circle variant="outline" class="w-5 h-5 text-grey-500 dark:text-grey-300" />
                                        </flux:button>
                                    </div>
                                    <div class="">
                                        @if ($row->product)
                                            <flux:text variant="strong">{{ $row->product->brand->name }}</flux:text>
                                            <flux:text>{{ $row->description ?? $row->product->description ?? '-' }}</flux:text>
                                            <flux:text variant="strong">{{ $row->sku ?? ($row->product->hasModules() ? $row->getModularSku() : $row->product->sku) ?? '-' }}</flux:text>
                                        @elseif ($row->customProduct)
                                            <flux:text variant="strong">{{ $row->customProduct->brand_name }}</flux:text>
                                            <flux:text>{{ $row->description ?? $row->customProduct->description ?? '-' }}</flux:text>
                                            <flux:text variant="strong">{{ $row->sku ?? $row->customProduct->sku ?? '-' }}</flux:text>
                                        @endif

                                        @if ($row->product?->hasModules())
                                            <div x-show="open" class="flex flex-col gap-2 mt-4">
                                                <flux:heading>Collection: {{ $row->product->collection->name ?? '-' }}</flux:heading>
                                                <flux:heading>Color: {{ $row->product->supplier_color ?? '-' }}</flux:heading>
                                                <flux:heading>Dimensions: {{ $row->product->dimensions ?? '-' }}</flux:heading>

                                                @if ($row->options)
                                                    <div class="flex flex-col">
                                                        <flux:heading class="mb-0!">Options:</flux:heading>
                                                        @foreach ($row->getSelectedOptions() as $option)
                                                            <div class="flex items-center gap-2">
                                                                @if ($option['image'] && Storage::disk(config('filesystems.public'), 'products')->exists($option['image']))
                                                                    <img src="{{ Storage::disk(config('filesystems.public'))->url($option['image']) }}"
                                                                        alt="{{ $option['description'] }}" class="w-4 object-cover rounded-full" />
                                                                @elseif ($option['image'])
                                                                    {{-- Image url in db but file not in filesystem --}}
                                                                    <img src="{{ asset('no-image.jpg') }}" class="w-4 object-cover rounded-full">
                                                                @else
                                                                    {{-- Image url not in db --}}
                                                                    <img src="{{ asset('no-image.jpg') }}" class="w-4 object-cover rounded-full">
                                                                @endif
                                                                <flux:subheading>{{ $option['description'] }}</flux:subheading>
                                                            </div>
                                                        @endforeach
                                                    </div>
                                                @endif
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </flux:table.cell>
                            <flux:table.cell>
                                <flux:text>
                                    <span x-bind:class="'{{ $row->shipped_quantity === $row->quantity }}' ? 'text-green-600 dark:text-green-400' : 'text-amber-600 dark:text-amber-400'">{{ $row->quantity }}</span>
                                </flux:text>
                            </flux:table.cell>
                            <flux:table.cell>
                                <flux:text>Shipped: {{ $row->shipped_quantity }}</flux:text>
                                <flux:text>Stocked: {{ $row->stocked_quantity }}</flux:text>
                                <flux:text>Ordered: {{ $row->ordered_quantity }}</flux:text>
                            </flux:table.cell>
                            <flux:table.cell align="start">
                                {{-- Selling Price --}}
                                @if ($row->selling_price_override)
                                    {{ eu_currency($row->sellingPriceOverride) }}
                                @else
                                    {{ $row->sellingPrice ? eu_currency($row->sellingPrice) : ($row->product?->hasModules() ? eu_currency($row->product?->getModularPrice($row->options)) : eu_currency($row->product->sellingPrice ?? 0)) }}
                                @endif
                                <br>
                                {{-- Discount --}}
                                @if (isset($row->discount_override))
                                    - {{ $row->discount_override }} %
                                @else
                                    - {{ $row->discount ?? $row->getDiscountForClient() }} %
                                @endif

                                {{-- Variation --}}
                                @if (isset($row->variation))
                                    ({{ $row->variation > 0 ? '+' : '' }}{{ $row->variation }} %)
                                @endif
                                <br>
                                {{-- Unit Price --}}
                                {{ eu_currency($row->rowUnitPrice) }}
                            </flux:table.cell>
                            <flux:table.cell>
                                {{ eu_currency($row->rowFinalPrice) }}
                            </flux:table.cell>
                            <flux:table.cell>
                                <flux:text>Req. {{ $row->required_delivery_date?->format('d-m-y') ?? '-' }}</flux:text>
                                <flux:text>Exp. {{ $row->expected_delivery_date?->format('d-m-y') ?? '-' }}</flux:text>
                                <flux:text>
                                    @if($row->required_delivery_date && $row->expected_delivery_date)
                                        @if($row->expected_delivery_date->lt($row->required_delivery_date))
                                            <span class="text-green-600 dark:text-green-400">On time</span>
                                        @else
                                            <span class="text-red-600 dark:text-red-400">Delayed</span>
                                        @endif
                                    @else
                                        -
                                    @endif
                                </flux:text>
                            </flux:table.cell>

                            <flux:table.cell align="end">
                                <flux:dropdown>
                                    <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions" variant="ghost"></flux:button>

                                    <flux:menu>
                                        @can('view', $resourceValue)
                                            <flux:menu.item wire:click="view({{ $row->id }})" icon="eye">View</flux:menu.item>
                                        @endcan
                                        @can('update', $resourceValue)
                                            <flux:menu.item wire:click="edit({{ $row->id }})" icon="pencil-square">Edit</flux:menu.item>
                                        @endcan

                                        @can('delete', $resourceValue)
                                            <flux:menu.separator />
                                            <flux:menu.item wire:confirm="Are you sure you want to delete this order? This action cannot be undone." wire:click="delete({{ $row->id }})" variant="danger" icon="trash" disabled>Delete</flux:menu.item>
                                        @endcan
                                    </flux:menu>
                                </flux:dropdown>
                            </flux:table.cell>
                        </flux:table.row>
                        @endforeach
                    </flux:table.rows>
                </flux:table>
            @endif
        </div>
    </div>

    {{-- View Order Row - Modal --}}
    <flux:modal name="view-order-row" @close="resetForm" variant="flyout" class="w-full md:w-2/3 lg:w-1/3 space-y-12" :dismissible="false">
        <div>
            <flux:heading size="lg">View order row</flux:heading>
            <flux:subheading>View order row for the {{ $resourceNameSingular }}.</flux:subheading>
        </div>

        <div class="flex flex-col gap-6">
            <flux:input wire:model="form.position_id" readonly type="text" placeholder="ABC123" variant="filled" label="Position ID" description="This will be publicly displayed." />
            <flux:input wire:model="form.shipped_quantity" readonly type="number" placeholder="1" variant="filled" label="Shipped Quantity" description="This will be publicly displayed." />
            <flux:input wire:model="form.stocked_quantity" readonly type="number" placeholder="1" variant="filled" label="Stocked Quantity" description="This will be publicly displayed." />
            <flux:input wire:model="form.ordered_quantity" readonly type="number" placeholder="1" variant="filled" label="Ordered Quantity" description="This will be publicly displayed." />
            <flux:date-picker wire:model="form.expected_delivery_date" disabled label="Expected Delivery" description="This will be publicly displayed." />
            <flux:date-picker wire:model="form.required_delivery_date" disabled label="Required Delivery" description="This will be publicly displayed." />
        </div>
    </flux:modal>

    {{-- Edit Order Row - Modal --}}
    <flux:modal name="edit-order-row" @close="resetForm" variant="flyout" class="w-full md:w-2/3 lg:w-1/3 space-y-12" :dismissible="false">
        <div>
            <flux:heading size="lg">Edit order row</flux:heading>
            <flux:subheading>Edit order row for the {{ $resourceNameSingular }}.</flux:subheading>
        </div>

        <form wire:submit="update" class="flex flex-col gap-6">
            <flux:input wire:model="form.position_id" type="text" placeholder="ABC123" variant="filled" label="Position ID" description="This will be publicly displayed." />
            <flux:input wire:model="form.shipped_quantity" type="number" placeholder="1" variant="filled" label="Shipped Quantity" description="This will be publicly displayed." />
            <flux:input wire:model="form.stocked_quantity" type="number" placeholder="1" variant="filled" label="Stocked Quantity" description="This will be publicly displayed." />
            <flux:input wire:model="form.ordered_quantity" type="number" placeholder="1" variant="filled" label="Ordered Quantity" description="This will be publicly displayed." />
            <flux:date-picker wire:model="form.expected_delivery_date" label="Expected Delivery" description="This will be publicly displayed." />
            <flux:date-picker wire:model="form.required_delivery_date" label="Required Delivery" description="This will be publicly displayed." />

            <div class="flex">
                <flux:spacer />
                <flux:button size="sm" type="submit" variant="primary">Edit order row</flux:button>
            </div>
        </form>
    </flux:modal>
</div>
