{{-- Cart and Summary --}}
<flux:card size="sm" class="flex flex-col justify-between">
    <div>
        <flux:subheading class="mb-1">Cart details</flux:subheading>
        <flux:separator class="mb-2"></flux:separator>
    </div>
    <div>
        <flux:heading size="lg" class="mt-1 flex justify-between"><span
                class="text-sm font-light">Amount</span>{{ eu_currency($order->totalAmount) }}</flux:heading>
        <flux:heading size="lg" class="mt-1 flex justify-between"><span
                class="text-sm font-light">With addons</span>{{ eu_currency($order->getTotalAmountAttribute($withAddons = true)) }}
        </flux:heading>
    </div>
    @if($showPaymentTerms)
        <div>
            <flux:heading size="lg"
                          class="mb-1 flex justify-end">{{ $order->paymentTerm ? $order->paymentTerm->name : '-' }}</flux:heading>
        </div>
    @endif
</flux:card>
