{{-- Single Product --}}
<div
    class="flex flex-col rounded-xl justify-between bg-white dark:bg-zinc-700 border border-zinc-200 dark:border-zinc-700">
    <div class="relative rounded-t-xl overflow-hidden">
        @if ($product->hasVariants())
            <div class="absolute bg-zinc-300 dark:bg-zinc-600 rounded-full top-0 left-0 m-2 p-2">
                <flux:tooltip content="Has variants">
                    <flux:icon.swatch variant="micro"/>
                </flux:tooltip>
            </div>
        @endif
        @if ($product->hasModules())
            <div class="absolute bg-zinc-300 dark:bg-zinc-600 rounded-full top-0 left-0 m-2 p-2">
                <flux:tooltip content="Has modules">
                    <flux:icon.rectangle-stack variant="micro"/>
                </flux:tooltip>
            </div>
        @endif

        @if ($product->image)
            <a
                href="#"
                class="aspect-square flex rounded-t-lg overflow-hidden"
                x-data="{ url: '{{ Storage::disk(config('filesystems.public'))->url($product->image) }}', thumb: '{{ Storage::disk(config('filesystems.public'))->url($product->image) }}' }"
                x-lightbox="url"
            >
                <img class="flex-1 object-cover object-center" :src="thumb" alt="">
            </a>
        @else
            {{-- Image url not in db --}}
            <img src="{{ asset('no-image.jpg') }}" class="w-full object-cover rounded-t-lg">
        @endif
    </div>
    <div class="flex flex-col flex-auto justify-between bg-zinc-100 dark:bg-zinc-700 rounded-b-xl p-2">
        <div>
            <flux:subheading class="text-xs">{{ $product->brand->name ?? '-' }}</flux:subheading>
            <flux:heading class="text-md mt-0!">{{ $product->description }}</flux:heading>
        </div>
        <div>
            <flux:subheading class="text-xs">{{ $product->sku }}</flux:subheading>

            <flux:separator class="my-1"></flux:separator>

            <div class="flex justify-between items-center mt-2">
                <div class="flex flex-col gap-1">
                    @if (!$product->hasModules())
                        <flux:heading class="text-xs opacity-50">
                        <span class="line-through">
                            {{ eu_currency($product->sellingPrice) }}
                        </span>
                        </flux:heading>
                    @endif
                    <flux:heading size="lg">
                        <span>
                            @if ($product->hasModules())
                                <flux:subheading class="text-xs">
                                    Starting from
                                </flux:subheading>
                                {{ eu_currency($product->startingSellingPrice) }}
                            @else
                                {{ eu_currency($product->getSellingPriceForClient($client)) }}
                            @endif
                        </span>
                    </flux:heading>
                </div>

                <div>
                    @can('create', \App\Models\Order\Order::class)
                        <flux:button @click="$dispatch('add-to-cart-show', { product: {{ $product->id }} })" size="sm"
                                     icon="shopping-bag"/>
                    @endcan
                    <flux:button @click="$dispatch('product-show', { product: {{ $product->id }} })" size="sm"
                                 icon="magnifying-glass-plus"/>
                </div>
            </div>
        </div>
    </div>
</div>
