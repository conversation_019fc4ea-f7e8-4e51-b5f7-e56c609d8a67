<flux:modal name="cart-groups" variant="flyout" class="w-full pt-16">
    <div class="flex flex-col gap-8">
        <div>
            <flux:heading size="xl">Cart Groups</flux:heading>
            <flux:subheading>Selected cart group: {{ $selectedCartGroup->name ?? 'None' }}</flux:subheading>
        </div>

        <div class="flex flex-col gap-12">
            {{-- Create a Cart Group --}}
            @can('create', \App\Models\Order\Order::class)
                <form wire:submit="saveCartGroup" class="flex flex-col md:w-1/5">
                    <flux:input wire:model="createCartGroupForm.name" type="text" placeholder="Create a new cart group"
                                size="sm" variant="filled" :disabled="$editCartGroupForm->isEditMode">
                        <x-slot name="iconTrailing">
                            <flux:button type="submit" size="xs" variant="subtle" icon="plus" class="-mr-1"
                                         :disabled="$editCartGroupForm->isEditMode"/>
                        </x-slot>
                    </flux:input>

                    <flux:error name="createCartGroupForm.name"/>
                </form>
            @endcan

            {{-- Cart Groups Listing and Items --}}
            @if ($order && !$order->cartGroups->isEmpty())
                <flux:table>
                    <flux:table.columns>
                        <flux:table.cell class="w-16"></flux:table.cell>
                        <flux:table.column class="w-16">ID</flux:table.column>
                        <flux:table.column class="flex-1">Group Name</flux:table.column>
                        <flux:table.column class="w-16">Products count</flux:table.column>
                        <flux:table.column class="w-64"></flux:table.column>
                    </flux:table.columns>

                    <flux:table.rows x-sort="$wire.sort($item, $position)">
                        @foreach ($order->cartGroups as $cartGroup)

                            {{-- Edit Cart Group --}}
                            @if ($editCartGroupForm->isEditMode && $editCartGroupForm->cartGroup->id === $cartGroup->id)
                                @can('update', $order)
                                    <flux:table.row
                                        wire:key="edit-cart-group-{{ $cartGroup->id }}-{{ $loop->iteration }}-{{ $selectedCartGroup?->id }}"
                                        x-sort:item="{{ $cartGroup->id }}">
                                        <flux:table.cell class="w-16">
                                            <flux:icon.chevron-up-down variant="outline"
                                                                       class="text-grey-500 dark:text-grey-300"/>
                                        </flux:table.cell>
                                        <flux:table.cell
                                            class="w-16">{{ $cartGroup?->id ?? '-' }}</flux:table.cell>
                                        <flux:table.cell class="flex-1">
                                            <form wire:submit="updateCartGroup" class="flex flex-col md:w-1/2">
                                                <flux:input wire:model="editCartGroupForm.name" type="text"
                                                            placeholder="Edit a cart group" size="sm" variant="filled">
                                                    <x-slot name="iconTrailing" class="flex gap-2">
                                                        <flux:button type="submit" size="xs" variant="subtle"
                                                                     icon="check"
                                                                     class="-mr-1"/>
                                                        <flux:button type="button" wire:click="cancelEditCartGroup"
                                                                     size="xs" variant="subtle" icon="x-mark"
                                                                     class="-mr-1"/>
                                                    </x-slot>
                                                </flux:input>

                                                <flux:error name="editCartGroupForm.name"/>
                                            </form>
                                        </flux:table.cell>
                                        <flux:table.cell class="w-16">
                                            {{ $cartGroup?->orderRows()->count() ?? '0' }}
                                        </flux:table.cell>
                                        <flux:table.cell class="w-64"></flux:table.cell>
                                    </flux:table.row>
                                @endcan

                            @else
                                <flux:table.row
                                    wire:key="cart-group-{{ $cartGroup->id }}-{{ $loop->iteration }}-{{ $selectedCartGroup?->id }}"
                                    x-sort:item="{{ $cartGroup->id }}">
                                    <flux:table.cell class="w-16">
                                        <flux:icon.chevron-up-down variant="outline"
                                                                   class="text-grey-500 dark:text-grey-300"/>
                                    </flux:table.cell>
                                    <flux:table.cell class="w-16">
                                        {{ $cartGroup?->id ?? '-' }}
                                    </flux:table.cell>
                                    <flux:table.cell class="flex-1">
                                        <div class="flex items-center gap-2">
                                            <span>{{ $cartGroup?->name ?? '-' }}</span>
                                            @if ($cartGroup?->id === $selectedCartGroup?->id)
                                                <flux:badge size="sm">Selected</flux:badge>
                                            @endif
                                        </div>
                                    </flux:table.cell>
                                    <flux:table.cell class="w-16">
                                        {{ $cartGroup?->orderRows()->count() ?? '0' }}
                                    </flux:table.cell>

                                    {{-- Actions Dropdown --}}
                                    <flux:table.cell class="w-64">
                                        <div class="flex justify-end">
                                            <flux:dropdown>
                                                <flux:button size="sm" icon="ellipsis-vertical" square/>
                                                @can('update', $order)
                                                    <flux:menu>
                                                        <flux:menu.item
                                                            wire:click="selectCartGroup({{ $cartGroup?->id }})"
                                                            icon="plus"
                                                            :disabled="$cartGroup?->id === $selectedCartGroup?->id">
                                                            {{ $cartGroup?->id === $selectedCartGroup?->id ? 'Selected' : 'Select' }}
                                                        </flux:menu.item>

                                                        @can('update', $order)
                                                            <flux:menu.item
                                                                wire:click="editCartGroup({{ $cartGroup?->id }})"
                                                                icon="pencil-square">Edit
                                                            </flux:menu.item>
                                                        @endcan
                                                        @can('delete', $order)
                                                            <flux:menu.separator/>
                                                            <flux:menu.item
                                                                wire:click="deleteCartGroup({{ $cartGroup?->id }})"
                                                                variant="danger" icon="trash">Delete
                                                            </flux:menu.item>
                                                        @endcan
                                                    </flux:menu>
                                                @endcan
                                            </flux:dropdown>
                                        </div>
                                    </flux:table.cell>
                                </flux:table.row>
                            @endif
                        @endforeach
                    </flux:table.rows>
                </flux:table>
            @else
                <flux:subheading class="flex justify-center pb-4">No results.</flux:subheading>
            @endif
        </div>
    </div>
</flux:modal>
