<flux:modal name="addons" variant="flyout" class="w-full pt-16">
    <div class="flex flex-col gap-8">
        <div>
            <flux:heading size="xl">Addons</flux:heading>
        </div>

        <div class="flex flex-col gap-12">
            {{-- Addons Listing --}}
            @if ($order && !$order->addons->isEmpty())
                @if ($order->addons()->get()->isEmpty())
                    <flux:subheading class="flex justify-center pb-4">No results.</flux:subheading>
                @else
                    <form wire:submit="updateAddon">
                        <flux:table>
                            <flux:table.columns>
                                <flux:table.column>Code</flux:table.column>
                                <flux:table.column>Description</flux:table.column>
                                <flux:table.column>Percent (%)</flux:table.column>
                            </flux:table.columns>

                            <flux:table.rows>
                                @foreach ($order->addons as $addon)

                                    {{-- Edit Addon --}}
                                    @if ($editAddonForm->isEditMode && $editAddonForm->orderAddon->id === $addon->id)
                                        <flux:table.row>
                                            <flux:table.cell>
                                                <flux:select searchable clearable
                                                             wire:model.live="editAddonForm.code" variant="listbox"
                                                             size="sm" placeholder="Choose code...">
                                                    @foreach($addonCodes as $addonCode)
                                                        <flux:select.option
                                                            value="{{ $addonCode->value }}">{{ $addonCode->label() }}</flux:select.option>
                                                    @endforeach
                                                </flux:select>
                                                <flux:error name="editAddonForm.code"/>
                                            </flux:table.cell>
                                            <flux:table.cell>
                                                <flux:input wire:model="editAddonForm.description" type="text"
                                                            placeholder="Addon description" size="sm"
                                                            variant="filled"/>
                                                <flux:error name="editAddonForm.description"/>
                                            </flux:table.cell>
                                            <flux:table.cell>
                                                <flux:input wire:model="editAddonForm.percent" type="number"
                                                            placeholder="Addon percent" size="sm" variant="filled"/>
                                                <flux:error name="editAddonForm.percent"/>
                                            </flux:table.cell>

                                            {{-- Actions --}}
                                            <flux:table.cell>
                                                <div class="flex justify-end gap-2">
                                                    <flux:button type="submit" size="sm" variant="subtle"
                                                                 icon="check" class="-mr-1"/>
                                                    <flux:button type="button" wire:click="cancelEditAddon"
                                                                 size="sm" variant="subtle" icon="x-mark" class=""/>
                                                </div>
                                            </flux:table.cell>
                                        </flux:table.row>
                                    @else

                                        {{-- View Addon --}}
                                        <flux:table.row>
                                            <flux:table.cell>{{ $addon->code ?? '-' }}</flux:table.cell>
                                            <flux:table.cell>{{ $addon->description ?? '-' }}</flux:table.cell>
                                            <flux:table.cell>{{ $addon->percent ?? '--' }} %</flux:table.cell>

                                            {{-- Actions Dropdown --}}
                                            <flux:table.cell>
                                                <div class="flex justify-end">
                                                    <flux:dropdown>
                                                        <flux:button size="sm" icon="ellipsis-vertical" square/>

                                                        <flux:menu>
                                                            <flux:menu.item wire:click="" icon="eye" disabled>View
                                                            </flux:menu.item>
                                                            @can('update', $order)
                                                                <flux:menu.item
                                                                    wire:click="editAddon({{ $addon?->id }})"
                                                                    icon="pencil-square">Edit
                                                                </flux:menu.item>
                                                            @endcan
                                                            @can('delete', $order)
                                                                <flux:menu.separator/>
                                                                <flux:menu.item
                                                                    wire:confirm="Are you sure you want to delete this addon? This action cannot be undone."
                                                                    wire:click="deleteAddon({{ $addon->id }})"
                                                                    variant="danger" icon="trash">Delete
                                                                </flux:menu.item>
                                                            @endcan
                                                        </flux:menu>
                                                    </flux:dropdown>
                                                </div>
                                            </flux:table.cell>
                                        </flux:table.row>
                                    @endif
                                @endforeach
                            </flux:table.rows>
                        </flux:table>
                    </form>
                @endif
            @endif

            {{-- Create Addons --}}
            @can('create', \App\Models\Order\Order::class)
                <form wire:submit="saveAddon">
                    <flux:table>
                        <flux:table.columns>
                            <flux:table.column>Code</flux:table.column>
                            <flux:table.column>Description</flux:table.column>
                            <flux:table.column>Percent</flux:table.column>
                        </flux:table.columns>

                        <flux:table.rows>
                            <flux:table.row>
                                <flux:table.cell>
                                    <flux:select searchable clearable wire:model.live="createAddonForm.code"
                                                 variant="listbox" size="sm" placeholder="Choose code...">
                                        @foreach($addonCodes as $addonCode)
                                            <flux:select.option
                                                value="{{ $addonCode->value }}">{{ $addonCode->label() }}</flux:select.option>
                                        @endforeach
                                    </flux:select>
                                    <flux:error name="createAddonForm.code"/>
                                </flux:table.cell>
                                <flux:table.cell>
                                    <flux:input wire:model="createAddonForm.description" type="text"
                                                placeholder="Addon description" size="sm" variant="filled"
                                                :disabled="$createAddonForm->isEditMode"/>
                                    <flux:error name="createAddonForm.description"/>
                                </flux:table.cell>
                                <flux:table.cell>
                                    <flux:input wire:model="createAddonForm.percent" type="number"
                                                placeholder="Addon percent" size="sm" variant="filled"
                                                :disabled="$createAddonForm->isEditMode"/>
                                    <flux:error name="createAddonForm.percent"/>
                                </flux:table.cell>

                                {{-- Actions --}}
                                <flux:table.cell>
                                    <div class="flex justify-end gap-2">
                                        <flux:button type="submit" size="sm" icon="plus"
                                                     :disabled="$editAddonForm->isEditMode">
                                        </flux:button>
                                    </div>
                                </flux:table.cell>
                            </flux:table.row>
                        </flux:table.rows>
                    </flux:table>
                </form>
            @endcan
        </div>
    </div>
</flux:modal>
