<flux:modal name="cart" variant="flyout" class="w-full pt-16" wire:close="refreshAll">
    <div class="flex flex-col gap-8">
        <div>
            <flux:heading size="xl">Cart Details</flux:heading>
            <flux:subheading>Selected cart group: {{ $selectedCartGroup->name ?? 'None.' }}</flux:subheading>
        </div>

        <div class="flex flex-col gap-12">


            {{-- Cart Groups Listing and Items --}}
            @if ($order && $selectedCartGroup)
                <div class="flex flex-col gap-12">
                    {{-- Items Listing --}}
                    @if ($selectedCartGroup->orderRows->isEmpty())
                        <flux:subheading class="flex justify-center pb-4">No results.</flux:subheading>
                    @else
                        <flux:table>
                            <flux:table.columns>
                                <flux:table.column></flux:table.column>
                                <flux:table.column></flux:table.column>
                                <flux:table.column>Description</flux:table.column>

                                <flux:table.column>Quantity</flux:table.column>
                                <flux:table.column>Price</flux:table.column>
                                <flux:table.column>Discount (%)</flux:table.column>
                                <flux:table.column>Variation (%)</flux:table.column>
                                <flux:table.column>Req. Delivery</flux:table.column>
                                <flux:table.column>Unit Price</flux:table.column>
                                <flux:table.column>Final Price</flux:table.column>
                            </flux:table.columns>

                            <flux:table.rows x-sort="$wire.sort($item, $position)">
                                @foreach ($selectedCartGroup->orderRows->sortBy('sort') as $row)
                                    <livewire:components.orders.creator.order-row :resourceValue="$order"
                                                                                  :row="$row" :key="$row->id"/>
                                @endforeach
                            </flux:table.rows>
                        </flux:table>
                    @endif

                    {{-- Create Custom Products --}}
                    @can('create', \App\Models\Order\Order::class)
                        <div class="flex flex-col gap-2">
                            <flux:heading size="lg">Custom Products</flux:heading>

                            <form wire:submit="saveCustomProduct">
                                <flux:table>
                                    <flux:table.columns>
                                        <flux:table.column>SKU</flux:table.column>
                                        <flux:table.column>Description</flux:table.column>
                                        <flux:table.column>Brand</flux:table.column>
                                        <flux:table.column>Dimensions</flux:table.column>
                                        <flux:table.column>Color</flux:table.column>
                                    </flux:table.columns>

                                    <flux:table.rows>
                                        <flux:table.row>
                                            <form wire:submit="saveCustomOrderRow" class="flex flex-col gap-2">
                                                <flux:table.cell>
                                                    <flux:input wire:model="createCustomProductForm.sku" type="text"
                                                                placeholder="SKU" size="sm" variant="filled"/>
                                                </flux:table.cell>
                                                <flux:table.cell>
                                                    <flux:input wire:model="createCustomProductForm.description"
                                                                type="text"
                                                                placeholder="Description" size="sm" variant="filled"/>
                                                </flux:table.cell>
                                                <flux:table.cell>
                                                    <flux:input wire:model="createCustomProductForm.brand_name"
                                                                type="text"
                                                                placeholder="Brand" size="sm" variant="filled"/>
                                                </flux:table.cell>
                                                <flux:table.cell>
                                                    <flux:input wire:model="createCustomProductForm.dimensions"
                                                                type="text"
                                                                placeholder="Dimensions" size="sm" variant="filled"/>
                                                </flux:table.cell>
                                                <flux:table.cell>
                                                    <flux:input wire:model="createCustomProductForm.supplier_color"
                                                                type="text"
                                                                placeholder="Color" size="sm" variant="filled"/>
                                                </flux:table.cell>

                                                {{-- Actions --}}
                                                <flux:table.cell>
                                                    <div class="flex justify-end gap-2">
                                                        <flux:button type="submit" size="sm" icon="plus"
                                                                     square></flux:button>
                                                    </div>
                                                </flux:table.cell>
                                            </form>
                                        </flux:table.row>
                                    </flux:table.rows>
                                </flux:table>
                            </form>
                        </div>
                    @endcan
                </div>
            @else
                <flux:subheading class="flex justify-center pb-4">No cart group selected.</flux:subheading>
            @endif
        </div>
    </div>
</flux:modal>
