{{-- <PERSON><PERSON> and Summary --}}
<div class="flex flex-col gap-2">
    <flux:card size="sm" class="flex flex-col justify-between">
        <div>
            <flux:subheading class="mb-1">Cart details</flux:subheading>
            <flux:separator class="mb-2"></flux:separator>
        </div>
        <div>
            <flux:heading size="lg" class="mt-1 flex justify-between"><span class="text-sm font-light">Amount</span>{{ eu_currency($order->totalAmount) }}</flux:heading>
            <flux:heading size="lg" class="mt-1 flex justify-between"><span class="text-sm font-light">With addons</span>{{ eu_currency($order->getTotalAmountAttribute($withAddons = true)) }}</flux:heading>
        </div>
    </flux:card>

    <div class="flex gap-2">
        <flux:button @click="$dispatch('show-cart')" icon="shopping-bag" variant="primary" size="sm" class="w-full">Show cart</flux:button>
    </div>
</div>