<div class="flex justify-between">
    <div class="w-2/4">
        <flux:breadcrumbs>
            <flux:breadcrumbs.item href="{{ route('home') }}" wire:navigate icon="home" />
            @if ($parent)
                <flux:breadcrumbs.item href="{{ route($link) }}" wire:navigate>{{ $parent }}</flux:breadcrumbs.item>    
            @endif
            @if ($current)
                <flux:breadcrumbs.item>{{ $current }}</flux:breadcrumbs.item>
            @endif
        </flux:breadcrumbs>
    </div>
    <div class="w-1/4">
        {{-- <flux:input placeholder="Search..." icon="magnifying-glass" kbd="⌘K" /> --}}
    </div>
</div>