<flux:modal name="product-show" class="w-full md:max-w-2xl lg:max-w-4xl xl:max-w-5xl" @close="resetProductShowModal">
    <div class="grid grid-cols-1 md:grid-cols-2 auto-rows-[550px] overflow-hidden">
        <div class="flex flex-col justify-between gap-4 h-full">
            <div>
                @if ($product?->image)
                    <img src="{{ Storage::disk(config('filesystems.public'))->url($product?->image) }}"
                         alt="{{ $product?->description }}" class="w-full h-auto aspect-square object-cover rounded-lg">
                @else
                    {{-- Image url not in db --}}
                    <img src="{{ asset('no-image.jpg') }}" class="w-full h-auto aspect-square object-cover rounded-lg">
                @endif
            </div>
            <div class="flex gap-2">
                @can('create', \App\Models\Order\Order::class)
                    <flux:button
                        wire:click="showAddToCart({{ $product?->id }})"
                        icon="shopping-bag" variant="primary" class="flex-1">
                        Add to cart
                    </flux:button>
                @endcan
                @if($product)
                    <livewire:components.modals.create-ticket :resourceValue="$product"/>
                @endif
            </div>
        </div>

        <div class="flex flex-col h-full">
            <flux:tab.group class="h-full p-4">
                {{-- Tabs --}}
                <flux:tabs>
                    <flux:tab name="overview">Overview</flux:tab>
                    <flux:tab name="specifications">Specifications</flux:tab>
                    <flux:tab name="tags">Tags</flux:tab>
                    @if ($product?->hasVariants())
                        <flux:tab
                            name="variants">{{ Str::ucfirst(Str::lower($product->parent_category_label ?? "Variants")) }}</flux:tab>
                    @endif
                    @if ($product?->hasModules())
                        <flux:tab name="options">Options</flux:tab>
                    @endif
                </flux:tabs>

                {{-- Overview --}}
                <flux:tab.panel name="overview" class="flex flex-col gap-2 h-full overflow-y-auto px-2">
                    {{-- SKU and Description --}}
                    <div>
                        <flux:subheading>SKU: {{ $product->sku ?? '-' }}</flux:subheading>
                        <flux:heading class="my-0!">{{ $product->description ?? '-' }}</flux:heading>
                    </div>

                    {{-- Brand --}}
                    <div>
                        <flux:subheading>Brand:</flux:subheading>
                        <flux:heading class="mt-0!">{{ $product->brand->name ?? '-' }}</flux:heading>
                    </div>

                    {{-- Collection --}}
                    <div>
                        <flux:subheading>Collection:</flux:subheading>
                        <flux:heading class="mt-0!">{{ $product->collection->name ?? '-' }}</flux:heading>
                    </div>

                    {{-- Dimensions --}}
                    <div>
                        <flux:subheading>Dimensions:</flux:subheading>
                        <flux:heading class="mt-0!">{{ $product->dimensions ?? '-' }}</flux:heading>
                    </div>

                    {{-- Extra Description --}}
                    <div>
                        <flux:subheading>Extra description:</flux:subheading>
                        <flux:heading class="mt-0!">{{ $product->extra_description ?? '-' }}</flux:heading>
                    </div>
                </flux:tab.panel>

                {{-- Specifications --}}
                <flux:tab.panel name="specifications" class="flex flex-col gap-2 h-full overflow-y-auto px-2">
                    {{-- Height --}}
                    <div>
                        <flux:subheading>Height:</flux:subheading>
                        <flux:heading class="mt-0!">{{ $product->height ?? '-' }}</flux:heading>
                    </div>

                    {{-- Width --}}
                    <div>
                        <flux:subheading>Width:</flux:subheading>
                        <flux:heading class="mt-0!">{{ $product->width ?? '-' }}</flux:heading>
                    </div>

                    {{-- Length --}}
                    <div>
                        <flux:subheading>Length:</flux:subheading>
                        <flux:heading class="mt-0!">{{ $product->length ?? '-' }}</flux:heading>
                    </div>

                    {{-- Diameter --}}
                    <div>
                        <flux:subheading>Diameter:</flux:subheading>
                        <flux:heading class="mt-0!">{{ $product->diameter ?? '-' }}</flux:heading>
                    </div>

                    {{-- Volume --}}
                    <div>
                        <flux:subheading>Volume:</flux:subheading>
                        <flux:heading class="mt-0!">{{ $product->volume ?? '-' }}</flux:heading>
                    </div>

                    {{-- Capacity --}}
                    <div>
                        <flux:subheading>Capacity:</flux:subheading>
                        <flux:heading class="mt-0!">{{ $product->capacity ?? '-' }}</flux:heading>
                    </div>
                </flux:tab.panel>

                {{-- Tags --}}
                <flux:tab.panel name="tags" class="flex flex-col gap-2 h-full overflow-y-auto px-2">
                    {{-- Product Type --}}
                    <div>
                        <flux:subheading>Type:</flux:subheading>
                        <flux:heading class="mt-0!">{{ $product->type ?? '-' }}</flux:heading>
                    </div>

                    {{-- Colors --}}
                    <div class="flex flex-col">
                        <flux:subheading>Colors:</flux:subheading>
                        <div class="flex flex-wrap gap-2">
                            @if ($product?->tagsWithType('color')->isNotEmpty())
                                @foreach ($product?->tagsWithType('color') as $tag)
                                    <flux:badge color="cyan" size="sm">{{ $tag->name }}</flux:badge>
                                @endforeach
                            @else
                                <flux:subheading> -</flux:subheading>
                            @endif
                        </div>
                    </div>

                    {{-- Materials --}}
                    <div class="flex flex-col">
                        <flux:subheading>Materials:</flux:subheading>
                        <div class="flex flex-wrap gap-2">
                            @if ($product?->tagsWithType('material')->isNotEmpty())
                                @foreach ($product?->tagsWithType('material') as $tag)
                                    <flux:badge color="amber" size="sm">{{ $tag->name }}</flux:badge>
                                @endforeach
                            @else
                                <flux:subheading> -</flux:subheading>
                            @endif
                        </div>
                    </div>

                    {{-- Styles --}}
                    <div class="flex flex-col">
                        <flux:subheading>Styles:</flux:subheading>
                        <div class="flex flex-wrap gap-2">
                            @if ($product?->tagsWithType('style')->isNotEmpty())
                                @foreach ($product?->tagsWithType('style') as $tag)
                                    <flux:badge color="violet" size="sm">{{ $tag->name }}</flux:badge>
                                @endforeach
                            @else
                                <flux:subheading> -</flux:subheading>
                            @endif
                        </div>
                    </div>

                    {{-- Destination Room --}}
                    <div class="flex flex-col">
                        <flux:subheading>Destination rooms:</flux:subheading>
                        <div class="flex flex-wrap gap-2">
                            @if ($product?->tagsWithType('destination_room')->isNotEmpty())
                                @foreach ($product?->tagsWithType('destination_room') as $tag)
                                    <flux:badge color="green" size="sm">{{ $tag->name }}</flux:badge>
                                @endforeach
                            @else
                                <flux:subheading> -</flux:subheading>
                            @endif
                        </div>
                    </div>
                </flux:tab.panel>

                {{-- Variants --}}
                @if ($product?->hasVariants())
                    <flux:tab.panel name="variants" class="flex flex-col gap-2 h-full overflow-y-auto px-2 pb-6">
                        @foreach ($product->variants() as $variant)
                            <div wire:click="setVariant({{ $variant->id }})" wire:key="variant-{{ $variant->id }}"
                                 class="flex flex-row rounded-xl bg-white dark:bg-white/10 border border-zinc-200 dark:border-white/10 cursor-pointer">
                                <div class="basis-1/4">
                                    @if ($variant->image)
                                        <img
                                            src="{{ Storage::disk(config('filesystems.public'))->url($variant->image) }}"
                                            alt="{{ $variant->description }}"
                                            class="w-full h-auto aspect-square object-cover rounded-l-xl"/>
                                    @else
                                        {{-- Image url not in db --}}
                                        <img src="{{ asset('no-image.jpg') }}"
                                             class="w-full h-auto aspect-square object-cover rounded-l-xl">
                                    @endif
                                </div>
                                <div class="basis-2/4 flex flex-col flex-auto justify-between p-2">
                                    <div>
                                        <flux:subheading class="text-[xs]!">{{ $variant->sku }}</flux:subheading>
                                        <flux:heading class="text-sm! mt-0!">{{ $variant->description }}</flux:heading>
                                        <flux:subheading
                                            class="text-xs!">{{ Str::ucfirst(Str::lower($variant->parent_category_label ?? "Variants Label")) }}
                                            : {{ Str::ucfirst(Str::lower($variant->parent_category_value ?? "-")) }}</flux:subheading>
                                    </div>
                                    <flux:heading class="">
                                        <span>{{ eu_currency($variant->sellingPrice) }}</span>
                                    </flux:heading>
                                </div>
                                @if ($product->id === $variant->id)
                                    <div
                                        class="flex justify-center items-center w-4 rounded-r-xl p-3 bg-zinc-100 dark:bg-zinc-700"
                                        style="writing-mode: vertical-rl;">
                                        <flux:text variant="strong">Selected</flux:text>
                                    </div>
                                @endif
                            </div>
                        @endforeach
                    </flux:tab.panel>
                @endif

                {{-- Modules --}}
                @if ($product?->hasModules())
                    <flux:tab.panel name="options" class="flex flex-col gap-2 p-2">
                        @foreach ($product?->modules as $module)
                            <div class="flex flex-col gap-2" wire:key="module-{{ $module->id }}">
                                <flux:heading>Step {{ $loop->iteration }}</flux:heading>
                                <flux:select searchable wire:model.live="selectedCategories.{{ $loop->iteration - 1 }}"
                                             variant="listbox" searchable clearable placeholder="Choose category ...">
                                    @foreach($categories[$loop->iteration - 1] as $category)
                                        <flux:select.option value="{{ $category }}">{{ $category }}</flux:select.option>
                                    @endforeach
                                </flux:select>
                                <flux:select searchable wire:model.live="selectedOptions.{{ $loop->iteration - 1 }}"
                                             variant="listbox" searchable clearable placeholder="Choose option ...">
                                    @foreach($options[$loop->iteration - 1] as $option)
                                        <flux:select.option value="{{ $option['id'] }}">
                                            <div class="flex items-center gap-2">
                                                @if ($option['image'])
                                                    <img
                                                        src="{{ Storage::disk(config('filesystems.public'))->url($option['image']) }}"
                                                        alt="{{ $option['description'] }}"
                                                        class="w-4 object-cover rounded-full"/>
                                                @else
                                                    {{-- Image url not in db --}}
                                                    <img src="{{ asset('no-image.jpg') }}"
                                                         class="w-4 object-cover rounded-full">
                                                @endif
                                                {{ $option['description'] }}
                                            </div>
                                        </flux:select.option>
                                    @endforeach
                                </flux:select>
                            </div>
                        @endforeach
                    </flux:tab.panel>
                @endif
            </flux:tab.group>
        </div>
    </div>
</flux:modal>
