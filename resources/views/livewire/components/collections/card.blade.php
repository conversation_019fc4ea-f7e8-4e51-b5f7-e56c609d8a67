<div class="flex flex-col rounded-xl justify-between bg-white dark:bg-zinc-700 border border-zinc-200 dark:border-zinc-700">
    <div class="relative rounded-t-xl overflow-hidden">
        @if (in_array($collection->name, $selectedCollections))
            <div class="absolute top-0 left-0 m-2 p-2">
                <flux:tooltip content="Selected">
                    <flux:icon.check-circle variant="solid" class="text-zinc-700 dark:text-zinc-700" />
                </flux:tooltip>
            </div>
        @endif

        @if ($collection->image && Storage::disk(config('filesystems.public'))->exists($collection->image))
            <img src="{{ Storage::disk(config('filesystems.public'))->url($collection->image) }}"
                alt="{{ $collection->description }}" class="w-full object-cover rounded-t-lg" />
        @elseif ($collection->image)
            {{-- Image url in db but file not in filesystem --}}
            <img src="{{ asset('no-image.jpg') }}" class="w-full object-cover rounded-t-lg">
        @else
            {{-- Image url not in db --}}
            <img src="{{ asset('no-image.jpg') }}" class="w-full object-cover rounded-t-lg">
        @endif
    </div>
    <div class="flex flex-col flex-auto justify-between bg-zinc-100 dark:bg-zinc-700 rounded-b-xl p-2">
        <flux:heading @click="$dispatch('toggleCollection', { collection: {{ $collection->name }} })" class="text-xs! mt-0!">{{ $collection->name }}</flux:heading>
    </div>
</div>