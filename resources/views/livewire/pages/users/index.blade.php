<flux:main container>
    <div class="flex justify-between items-start">
        <livewire:components.breadcrumbs/>
        <livewire:components.modals.create-ticket resourceType="users" size="xs"/>
    </div>
    <flux:separator variant="subtle" class="my-4"/>

    <flux:heading size="xl" level="1">Users</flux:heading>
    <flux:subheading size="lg" class="mb-6">Here's the users list and details</flux:subheading>

    <div class="flex flex-col gap-8 mt-12">

        {{-- Actions --}}
        <div class="flex flex-col">
            <div class="flex justify-between gap-2">
                <div>
                    <flux:input wire:model.live="search" placeholder="Search..." clearable size="sm"/>
                </div>
                <div>
                    @can('create', \App\Models\User::class)
                        <flux:button href="{{ route('users.create') }}" wire:navigate size="sm">Create user
                        </flux:button>
                    @endcan
                </div>
            </div>
        </div>

        {{-- Users Listing --}}
        <flux:table :paginate="$this->users">
            <flux:table.columns>
                {{-- <flux:table.column>#</flux:table.column> --}}
                <flux:table.column sortable :sorted="$sortBy === 'id'" :direction="$sortDirection"
                                   wire:click="sort('id')">#
                </flux:table.column>
                @can(['view_users_first_name', 'view_users_last_name'])
                    <flux:table.column>Name</flux:table.column>
                @endcan
                @can('view_users_phone')
                    <flux:table.column>Phone</flux:table.column>
                @endcan
                @can('view_users_email')
                    <flux:table.column>Email</flux:table.column>
                @endcan
                @can('view_users_role')
                    <flux:table.column>Role</flux:table.column>
                @endcan
                @can('view_users_type')
                    <flux:table.column>Type</flux:table.column>
                @endcan
                @can('view_users_is_employee')
                    <flux:table.column sortable :sorted="$sortBy === 'is_employee'" :direction="$sortDirection"
                                       wire:click="sort('is_employee')">Employee
                    </flux:table.column>
                @endcan
            </flux:table.columns>

            <flux:table.rows>
                @foreach ($this->users as $user)
                    <flux:table.row :key="$user->id">
                        <flux:table.cell>{{ $user->id }}</flux:table.cell>
                        @can(['view_users_first_name', 'view_users_last_name'])
                            <flux:table.cell variant="strong">
                                @can('view', $user)
                                    <flux:link href="{{ route('users.show', $user->id) }}" wire:navigate>
                                        {{ $user->first_name }} {{ $user->last_name }}
                                    </flux:link>
                                @else
                                    {{ $user->first_name }} {{ $user->last_name }}
                                @endcan
                            </flux:table.cell>
                        @endcan
                        @can('view_users_phone')
                            <flux:table.cell>
                                @if($user->phone)
                                    {{ $user->phone }}
                                @else
                                    -
                                @endif
                            </flux:table.cell>
                        @endcan
                        @can('view_users_email')
                            <flux:table.cell>{{ $user->email }}</flux:table.cell>
                        @endcan
                        @can('view_users_role')
                            <flux:table.cell>
                                {{ $user->roles()->first()?->name ?? '-'  }}
                            </flux:table.cell>
                        @endcan
                        @can('view_users_type')
                            <flux:table.cell>
                                <flux:badge color="{{ $user->type->color() }}" size="sm"
                                            inset>{{ $user->type->label() }}</flux:badge>
                            </flux:table.cell>
                        @endcan
                        @can('view_users_is_employee')
                            <flux:table.cell>
                                @if($user->is_employee)
                                    <flux:icon.check-circle size="sm" class="text-green-500 dark:text-green-300"/>
                                @else
                                    <flux:icon.x-circle size="sm" class="text-red-500 dark:text-red-300"/>
                                @endif
                            </flux:table.cell>
                        @endcan
                        <flux:table.cell class="flex justify-end">
                            <flux:dropdown>
                                <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions"
                                             variant="ghost"></flux:button>

                                <flux:menu>
                                    @can('view', $user)
                                        <flux:menu.item wire:click="view({{ $user->id }})" icon="eye">View
                                        </flux:menu.item>
                                    @endcan
                                    @can('update', $user)
                                        <flux:menu.item wire:click="edit({{ $user->id }})" icon="pencil-square">Edit
                                        </flux:menu.item>
                                    @endcan

                                    @can('delete', $user)
                                        <flux:menu.separator/>
                                        <flux:menu.item
                                            wire:confirm="Are you sure you want to delete this user? This action cannot be undone."
                                            wire:click="delete({{ $user->id }})" variant="danger" icon="trash">
                                            Delete
                                        </flux:menu.item>
                                    @endcan
                                </flux:menu>
                            </flux:dropdown>
                        </flux:table.cell>
                    </flux:table.row>
                @endforeach
            </flux:table.rows>
        </flux:table>
    </div>
</flux:main>
