<flux:main container>
    <div class="flex justify-between items-start">
        <livewire:components.breadcrumbs />
        <livewire:components.modals.create-ticket :resourceValue="$user" size="xs"/>
    </div>
    <flux:separator variant="subtle" class="my-4" />

    <flux:heading size="xl" level="1">User: {{ $user->first_name }} {{ $user->last_name }}</flux:heading>
    <flux:subheading size="lg" class="mb-6">Here's the user info and details</flux:subheading>

    <div class="flex flex-col gap-12 mt-12">

        {{-- Actions --}}
        <div class="flex flex-col">
            <div class="flex justify-between gap-2">
                <div class="flex items-center">
                </div>
                <div>
                    @canany(['update', 'delete'], $user)
                        <flux:dropdown>
                            <flux:button size="sm" icon-trailing="chevron-down">Actions</flux:button>
                            <flux:menu>
                                @can('update', $user)
                                    <flux:menu.item wire:click="edit({{ $user->id }})" icon="pencil-square">Edit</flux:menu.item>
                                @endcan
                                @can('delete', $user)
                                    <flux:menu.separator />
                                    <flux:menu.item wire:confirm="Are you sure you want to delete this user? This action cannot be undone." wire:click="delete()" variant="danger" icon="trash">Delete</flux:menu.item>
                                @endcan
                            </flux:menu>
                        </flux:dropdown>
                    @endcanany
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">User details</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the user info and details</flux:subheading>
            </div>

            <div class="col-span-2">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    @can('view_users_first_name')
                        <flux:input wire:model="form.first_name" readonly variant="filled" label="First Name" description="This will be publicly displayed." />
                    @endcan
                    @can('view_users_last_name')
                        <flux:input wire:model="form.last_name" readonly variant="filled" label="Last Name" description="This will be publicly displayed." />
                    @endcan
                    @can('view_users_email')
                        <flux:input wire:model="form.email" readonly copyable variant="filled" label="Email" description="This will be publicly displayed." />
                    @endcan
                    @can('view_users_phone')
                        <flux:input wire:model="form.phone" readonly variant="filled" label="Phone" description="This will be publicly displayed." />
                    @endcan
                </div>
            </div>
        </div>

        <flux:separator variant="subtle" />

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Type and role</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the user info and details</flux:subheading>
            </div>

            <div class="col-span-2">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    @can('view_users_is_employee')
                        <flux:checkbox.group label="Employee" description="This will be publicly displayed." variant="cards" class="flex-col">
                            <flux:checkbox wire:model="form.is_employee"
                                icon="building-storefront"
                                label="Internal Employee"
                                description="Check it if the user is an internal employee."
                                disabled
                            />
                        </flux:checkbox.group>
                        <div></div>
                    @endcan
                    @can('view_users_type')
                        <flux:select searchable disabled wire:model="form.type" variant="listbox" placeholder="Choose user type..." label="Type" description="This will be publicly displayed." required>
                            @foreach($userTypes as $userType)
                                <flux:select.option value="{{ $userType->value }}">{{ $userType->label() }}</flux:select.option>
                            @endforeach
                        </flux:select>
                    @endcan
                    @can('view_users_role')
                        <flux:select searchable disabled wire:model="form.role" variant="listbox" placeholder="Choose user type..." label="Role" description="This will be publicly displayed." required>
                            @foreach($userRoles as $userRole)
                                <flux:select.option value="{{ $userRole->name }}">{{ $userRole->name }}</flux:select.option>
                            @endforeach
                        </flux:select>
                    @endcan
                </div>
            </div>
        </div>
    </div>

</flux:main>
