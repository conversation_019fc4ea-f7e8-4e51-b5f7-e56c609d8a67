<flux:main container>
    <livewire:components.breadcrumbs />
    <flux:separator variant="subtle" class="my-4" />

    {{-- Order Stats --}}
    <div class="flex justify-between">
        <div>
            <flux:heading size="xl" level="1">
                Stats for order: {{ $order->code }}
                <flux:button wire:click="backToOrder({{ $order->id }})" size="xs" square>
                    <flux:icon name="arrow-uturn-left" variant="micro" />
                </flux:button>
            </flux:heading>
            <flux:subheading size="lg" class="mb-6">Here's you can see the stats for the order</flux:subheading>
        </div>
        <div class="flex gap-2">
            <div><flux:badge icon="hashtag" size="sm md:lg">{{ $order->order_code ?? "XX_XXX00-000000" }}</flux:badge></div>
            <div><flux:badge icon="calendar" size="sm md:lg">{{ $order->date->format('d-m-y') }}</flux:badge></div>
            <div><flux:badge icon="{{ $order->status->icon() }}" color="{{ $order->status->color() }}" size="sm md:lg">{{ $order->status->label() }}</flux:badge></div>
        </div>
    </div>

    <div class="flex flex-col gap-8 mt-12">
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
            <flux:callout>
                <flux:callout.heading>Amount Over Discount Groups</flux:callout.heading>

                <flux:callout.text>
                    <p>Amount over discount groups.</p>
                </flux:callout.text>

                <flux:chart :value="$this->amountData" class="aspect-[3/1] mt-8">
                    <flux:chart.svg>
                        <flux:chart.line field="amount" class="text-pink-500" />
                        <flux:chart.point field="amount" class="text-pink-400" />

                        <flux:chart.axis axis="x" field="discount_group">
                            <flux:chart.axis.tick />
                            <flux:chart.axis.line />
                        </flux:chart.axis>

                        <flux:chart.axis axis="y" field="amount" :format="['style' => 'currency', 'currency' => 'EUR']">
                            <flux:chart.axis.grid />
                            <flux:chart.axis.tick />
                        </flux:chart.axis>
                    </flux:chart.svg>
                </flux:chart>
            </flux:callout>

            <flux:callout>
                <flux:callout.heading>Discount Percentage Over Discount Groups</flux:callout.heading>

                <flux:callout.text>
                    <p>Discount percentage over discount groups.</p>
                </flux:callout.text>

                <flux:chart :value="$this->discountData" class="aspect-[3/1] mt-8">
                    <flux:chart.svg>
                        <flux:chart.line field="client_discount" class="text-blue-500" />
                        <flux:chart.point field="client_discount" class="text-blue-400" />

                        <flux:chart.axis axis="x" field="discount_group">
                            <flux:chart.axis.tick />
                            <flux:chart.axis.line />
                        </flux:chart.axis>

                        <flux:chart.axis axis="y" field="client_discount" :format="['style' => 'percent']
">
                            <flux:chart.axis.grid />
                            <flux:chart.axis.tick />
                        </flux:chart.axis>
                    </flux:chart.svg>
                </flux:chart>
            </flux:callout>
        </div>

        {{-- Actions --}}
        <div class="flex flex-col">
            <div class="flex justify-between gap-2">
                <div>
                    {{-- <flux:input wire:model.live="search" placeholder="Search..." clearable size="sm"/> --}}
                </div>
                <div>
                    {{-- <flux:button href="{{ route('orders.create') }}" wire:navigate size="sm">Create order</flux:button> --}}
                </div>
            </div>
        </div>

        {{-- Orders Listing --}}
        @if ($this->discountGroups()->isEmpty())
            <flux:subheading class="flex justify-center pb-4">No results.</flux:subheading>
        @else
            <flux:table :paginate="$this->discountGroups()">
                <flux:table.columns>
                    <flux:table.column sortable :sorted="$sortBy === 'id'" :direction="$sortDirection" wire:click="sort('id')">#</flux:table.column>
                    <flux:table.column sortable :sorted="$sortBy === 'description'" :direction="$sortDirection" wire:click="sort('description')">Description</flux:table.column>
                    <flux:table.column align="end">Price</flux:table.column>
                    <flux:table.column align="end">Discount %</flux:table.column>
                    <flux:table.column align="end">Net €</flux:table.column>
                    <flux:table.column align="end">Cl. Discount %</flux:table.column>
                    <flux:table.column align="end">Var. %</flux:table.column>
                    <flux:table.column align="end">Cl. Net €</flux:table.column>
                    <flux:table.column align="end">Margin %</flux:table.column>
                    <flux:table.column align="end"></flux:table.column>
                    <flux:table.column align="end">Margin €</flux:table.column>
                    <flux:table.column align="end"></flux:table.column>
                </flux:table.columns>

                <flux:table.rows>
                    @foreach ($this->discountGroups() as $discountGroup)
                        @php
                            // Optimization: calculate stats only once per row
                            $stats = $this->stats($discountGroup);
                        @endphp
                        <flux:table.row :key="$discountGroup->id" >
                            <flux:table.cell>{{ $discountGroup->id }}</flux:table.cell>
                            <flux:table.cell>
                                <flux:link href="{{ route('discount-groups.show', $discountGroup->id) }}" wire:navigate>{{ $discountGroup->code ?? '-' }}</flux:link>
                                <flux:text size="sm" class="mt-2">{{ $discountGroup->description }}</flux:text>
                                <flux:text size="sm" class="mt-2">Brand: {{ $discountGroup->brand->name ?? '-' }}</flux:text>
                            </flux:table.cell>
                            <flux:table.cell align="end">{{ eu_currency($stats->selling_price) }}</flux:table.cell>
                            <flux:table.cell align="end">{{ $stats->discount }}</flux:table.cell>
                            <flux:table.cell align="end">{{ eu_currency($stats->net_value) }}</flux:table.cell>
                            <flux:table.cell align="end">{{ $stats->client_discount }}</flux:table.cell>
                            <flux:table.cell align="end">{{ $stats->variation }}</flux:table.cell>
                            <flux:table.cell align="end">{{ eu_currency($stats->client_net_value) }}</flux:table.cell>
                            <flux:table.cell align="end">{{ $stats->margin_percentage }}%</flux:table.cell>
                            <flux:table.cell>
                                @if ($stats->margin_percentage >= 20)
                                    <flux:icon name="arrow-trending-up" variant="solid" class="text-green-400" />
                                @elseif ($stats->margin_percentage >= 10)
                                    <flux:icon name="arrow-trending-up" variant="solid" class="text-amber-400" />
                                @else
                                    <flux:icon name="arrow-trending-down" variant="solid" class="text-red-400" />
                                @endif
                            </flux:table.cell>
                            <flux:table.cell align="end">{{ eu_currency($stats->margin_value) }}</flux:table.cell>

                            <flux:table.cell align="end">
                                <flux:dropdown>
                                    <flux:button size="sm" icon="ellipsis-vertical" variant="ghost"></flux:button>

                                    <flux:menu>
                                        @can('update', $order)
                                            <flux:menu.item wire:click="editDiscountGroup({{ $discountGroup->id }})" icon="receipt-percent">Set Discount</flux:menu.item>
                                            <flux:menu.separator />
                                        @endcan

                                        @can('view', $discountGroup->brand)
                                            <flux:menu.item href="{{ route('brands.show', $discountGroup->brand->id) }}" wire:navigate icon="eye">View Brand</flux:menu.item>
                                        @endcan
                                        @can('view', $discountGroup)
                                            <flux:menu.item href="{{ route('discount-groups.show', $discountGroup->id) }}" wire:navigate icon="eye">View Discount Group</flux:menu.item>
                                        @endcan
                                    </flux:menu>
                                </flux:dropdown>
                            </flux:table.cell>
                        </flux:table.row>
                    @endforeach
                </flux:table.rows>
            </flux:table>
        @endif
    </div>

    {{-- Edit Discount Group Value - Modal --}}
    <flux:modal name="edit-discount-group" @close="resetForm" variant="flyout" class="w-full md:w-2/3 lg:w-1/3 space-y-12" :dismissible="false">
        <div>
            <flux:heading size="lg">Edit discount group</flux:heading>
            <flux:subheading>Edit discount override for the rows that belongs to the discount group.</flux:subheading>
        </div>

        <form wire:submit="updateDiscountGroup" class="flex flex-col gap-6">
            <flux:input wire:model="discountOverride" type="number" step="0.01" icon="receipt-percent" placeholder="Discount Percentage" variant="filled" label="Discount (%)" description="Enter the discount percentage." />

            <div class="flex">
                <flux:spacer />
                <flux:button size="sm" type="submit" variant="primary">Edit discount</flux:button>
            </div>
        </form>
    </flux:modal>
</flux:main>
