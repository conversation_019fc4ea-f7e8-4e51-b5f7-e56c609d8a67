<flux:main container>
    <div class="flex justify-between items-start">
        <livewire:components.breadcrumbs/>
        <livewire:components.modals.create-ticket resourceType="orders" size="xs"/>
    </div>
    <flux:separator variant="subtle" class="my-4"/>

    <flux:heading size="xl" level="1">Orders</flux:heading>
    <flux:subheading size="lg" class="mb-6">Here's the orders list and details</flux:subheading>

    <div class="flex flex-col gap-8 mt-12">

        {{-- Actions --}}
        <div class="flex flex-col">
            <div class="flex justify-between gap-2">
                @if ($this->areFiltersActive())
                    <div class="flex justify-start">
                        <flux:button wire:click="clearSearchAndFilters" icon="x-mark" square size="sm"/>
                    </div>
                @endif
                <div class="grow">
                    <flux:input wire:model.live="search" placeholder="Search..." clearable size="sm"/>
                </div>
                <div>
                    @can('create', \App\Models\Order\Order::class)
                        <flux:button href="{{ route('orders.create') }}" wire:navigate size="sm">Create order
                        </flux:button>
                    @endcan
                </div>
            </div>
        </div>

        {{-- Filtri --}}
        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <flux:field>
                <flux:select searchable wire:model.live="hasConfirmationFile" variant="listbox" size="sm" clearable
                             placeholder="Has confirmation file...">
                    @foreach ($confirmationFileOptions as $value => $label)
                        <flux:select.option value="{{ $value }}">{{ $label }}</flux:select.option>
                    @endforeach
                </flux:select>
            </flux:field>
            <flux:field>
                <flux:select searchable wire:model.live="selectedStatus" variant="listbox" size="sm" clearable
                             placeholder="Choose status...">
                    @foreach ($statuses as $status)
                        <flux:select.option value="{{ $status->value }}">{{ $status->label() }}</flux:select.option>
                    @endforeach
                </flux:select>
            </flux:field>
            <flux:field>
                <flux:select searchable wire:model.live="hasCustomProducts" variant="listbox" size="sm" clearable
                             placeholder="Has custom products...">
                    @foreach ($customProductsOptions as $value => $label)
                        <flux:select.option value="{{ $value }}">{{ $label }}</flux:select.option>
                    @endforeach
                </flux:select>
            </flux:field>
        </div>

        {{-- Orders Listing --}}
        @if ($this->orders->isEmpty())
            <flux:subheading class="flex justify-center pb-4">No results.</flux:subheading>
        @else
            <flux:table :paginate="$this->orders">
                <flux:table.columns>
                    <flux:table.column sortable :sorted="$sortBy === 'id'" :direction="$sortDirection"
                                       wire:click="sort('id')">#
                    </flux:table.column>
                    @canany(['view_orders_code', 'view_orders_order_code', 'view_orders_description'])
                        <flux:table.column>Details</flux:table.column>
                    @endcanany
                    <flux:table.column sortable :sorted="$sortBy === 'status'" :direction="$sortDirection"
                                       wire:click="sort('status')">Status
                    </flux:table.column>
                    @can('view_orders_internal_referent_id')
                        <flux:table.column>Internal Ref.</flux:table.column>
                    @endcan
                    @can('view_orders_client_id')
                        <flux:table.column>Client</flux:table.column>
                    @endcan
                    @can('view_orders_partner_id')
                        <flux:table.column>Partner</flux:table.column>
                    @endcan

                </flux:table.columns>

                <flux:table.rows>
                    @foreach ($this->orders as $order)
                        <flux:table.row :key="$order->id">
                            <flux:table.cell>{{ $order->id }}</flux:table.cell>
                            @canany(['view_orders_code', 'view_orders_order_code', 'view_orders_description'])
                                <flux:table.cell>
                                    @can('view_orders_code')
                                        @can('view', $order)
                                            <flux:link href="{{ route('orders.show', $order->id) }}" variant="strong"
                                                       wire:navigate>
                                                {{ $order->code ?? '-' }}
                                            </flux:link>
                                        @else
                                            {{ $order->code ?? '-' }}
                                        @endcan
                                    @endcan
                                    @can('view_orders_order_code')
                                        <flux:text class="italic mt-2">{{ $order->order_code }}</flux:text>
                                    @endcan
                                    @can('view_orders_description')
                                        <flux:text
                                            class="mt-1 line-clamp-2">{{ $order->description ?? '-' }}</flux:text>
                                    @endcan
                                </flux:table.cell>
                            @endcanany
                            <flux:table.cell>
                                <flux:badge icon="{{ $order->status->icon() }}" color="{{ $order->status->color() }}"
                                            size="sm" inset>{{ $order->status->label() }}</flux:badge>
                            </flux:table.cell>
                            @can('view_orders_internal_referent_id')
                                <flux:table.cell>{{ $order->internalReferent ? ($order->internalReferent->first_name . ' ' . $order->internalReferent->last_name) : '-' }}</flux:table.cell>
                            @endcan
                            @can('view_orders_client_id')
                                <flux:table.cell variant="strong">
                                    @if ($order->client)
                                        @can('view', $order->client)
                                            <flux:link href="{{ route('clients.show', $order->client->id ?? '') }}"
                                                       wire:navigate>
                                                {{ $order->client->company ?? '-' }}
                                            </flux:link>
                                        @else
                                            {{ $order->client->company ?? '-' }}
                                        @endcan
                                    @else
                                        -
                                    @endif
                                </flux:table.cell>
                            @endcan
                            @can('view_orders_partner_id')
                                <flux:table.cell variant="strong">
                                    @if ($order->partner)
                                        @can('view', $order->partner)
                                            <flux:link href="{{ route('partners.show', $order->partner->id ?? '') }}"
                                                       wire:navigate>
                                                {{ $order->partner->company ?? '-' }}
                                            </flux:link>
                                        @else
                                            {{ $order->partner->company ?? '-' }}
                                        @endcan
                                    @else
                                        -
                                    @endif
                                </flux:table.cell>
                            @endcan
                            <flux:table.cell align="end">
                                <flux:dropdown>
                                    <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions"
                                                 variant="ghost"></flux:button>

                                    <flux:menu>
                                        @can('view', $order)
                                            <flux:menu.item wire:click="view({{ $order->id }})" icon="eye">View
                                            </flux:menu.item>
                                        @endcan
                                        @can('update', $order)
                                            <flux:menu.item wire:click="edit({{ $order->id }})" icon="pencil-square">
                                                Edit
                                            </flux:menu.item>
                                        @endcan

                                        @can('delete', $order)
                                            <flux:menu.separator/>
                                            <flux:menu.item
                                                wire:confirm="Are you sure you want to delete this order? This action cannot be undone."
                                                wire:click="delete({{ $order->id }})" variant="danger" icon="trash">
                                                Delete
                                            </flux:menu.item>
                                        @endcan
                                    </flux:menu>
                                </flux:dropdown>
                            </flux:table.cell>
                        </flux:table.row>
                    @endforeach
                </flux:table.rows>
            </flux:table>
        @endif
    </div>
</flux:main>
