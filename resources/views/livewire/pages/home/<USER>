<flux:main container>

    <flux:heading size="xl" level="1">Good afternoon, {{ $user->first_name }}</flux:heading>
    <flux:subheading size="lg" class="mb-6">Here's what's new today</flux:subheading>

    <div class="grid grid-cols-2 gap-4 md:grid-cols-2 lg:grid-cols-4">
        {{-- Clients Count --}}
        <flux:card size="sm">
            <flux:subheading>Clients</flux:subheading>
            <flux:heading size="xl" class="mb-1">{{ $clientsCount }}</flux:heading>

            <div class="flex items-center gap-2">
                @if ($clientsGrowth > 0)
                    <flux:icon.arrow-trending-up variant="micro" class="text-green-600 dark:text-green-500" />
                    <span class="text-sm text-green-600 dark:text-green-500">{{ $clientsGrowth }}%</span>
                @else
                    <flux:icon.arrow-trending-down variant="micro" class="text-red-600 dark:text-red-500" />
                    <span class="text-sm text-red-600 dark:text-red-500">{{ $clientsGrowth }}%</span>
                @endif
            </div>
            <flux:subheading size="sm">7 days compare</flux:subheading>
        </flux:card>

        {{-- Brands Count --}}
        <flux:card size="sm">
            <flux:subheading>Brands</flux:subheading>
            <flux:heading size="xl" class="mb-1">{{ $brandsCount }}</flux:heading>

            <div class="flex items-center gap-2">
                @if ($brandsGrowth > 0)
                    <flux:icon.arrow-trending-up variant="micro" class="text-green-600 dark:text-green-500" />
                    <span class="text-sm text-green-600 dark:text-green-500">{{ $brandsGrowth }}%</span>
                @else
                    <flux:icon.arrow-trending-down variant="micro" class="text-red-600 dark:text-red-500" />
                    <span class="text-sm text-red-600 dark:text-red-500">{{ $brandsGrowth }}%</span>
                @endif
            </div>
            <flux:subheading size="sm">7 days compare</flux:subheading>
        </flux:card>

        {{-- Partners Count --}}
        <flux:card size="sm">
            <flux:subheading>Partners</flux:subheading>
            <flux:heading size="xl" class="mb-1">{{ $partnersCount }}</flux:heading>

            <div class="flex items-center gap-2">
                @if ($partnersGrowth > 0)
                    <flux:icon.arrow-trending-up variant="micro" class="text-green-600 dark:text-green-500" />
                    <span class="text-sm text-green-600 dark:text-green-500">{{ $partnersGrowth }}%</span>
                @else
                    <flux:icon.arrow-trending-down variant="micro" class="text-red-600 dark:text-red-500" />
                    <span class="text-sm text-red-600 dark:text-red-500">{{ $partnersGrowth }}%</span>
                @endif
            </div>
            <flux:subheading size="sm">7 days compare</flux:subheading>
        </flux:card>

        {{-- Contacts Count --}}
        <flux:card size="sm">
            <flux:subheading>Contacts</flux:subheading>
            <flux:heading size="xl" class="mb-1">{{ $contactsCount }}</flux:heading>

            <div class="flex items-center gap-2">
                @if ($contactsGrowth > 0)
                    <flux:icon.arrow-trending-up variant="micro" class="text-green-600 dark:text-green-500" />
                    <span class="text-sm text-green-600 dark:text-green-500">{{ $contactsGrowth }}%</span>
                @else
                    <flux:icon.arrow-trending-down variant="micro" class="text-red-600 dark:text-red-500" />
                    <span class="text-sm text-red-600 dark:text-red-500">{{ $contactsGrowth }}%</span>
                @endif
            </div>
            <flux:subheading size="sm">7 days compare</flux:subheading>
        </flux:card>
    </div>

</flux:main>
