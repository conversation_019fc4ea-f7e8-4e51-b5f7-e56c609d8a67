<flux:main container>
    <div class="flex justify-between items-start">
        <livewire:components.breadcrumbs/>
        <livewire:components.modals.create-ticket resourceType="contacts" size="xs"/>
    </div>
    <flux:separator variant="subtle" class="my-4"/>

    <flux:heading size="xl" level="1">Contacts</flux:heading>
    <flux:subheading size="lg" class="mb-6">Here's the contacts list and details</flux:subheading>

    <div class="flex flex-col gap-8 mt-12">

        {{-- Actions --}}
        <div class="flex flex-col">
            <div class="flex justify-between gap-2">
                <div>
                    <flux:input wire:model.live="search" placeholder="Search..." clearable size="sm"/>
                </div>
                <div>
                    @can('create', \App\Models\Contact::class)
                        <flux:button href="{{ route('contacts.create') }}" wire:navigate size="sm">Create contact
                        </flux:button>
                    @endcan
                </div>
            </div>
        </div>

        {{-- Contacts Listing --}}
        <flux:table :paginate="$this->contacts">
            <flux:table.columns>
                <flux:table.column sortable :sorted="$sortBy === 'id'" :direction="$sortDirection"
                                   wire:click="sort('id')">#
                </flux:table.column>
                @can('view_contacts_name')
                    <flux:table.column sortable :sorted="$sortBy === 'name'" :direction="$sortDirection"
                                       wire:click="sort('name')">Name
                    </flux:table.column>
                @endcan
                @can('view_contacts_departments')
                    <flux:table.column>Departments</flux:table.column>
                @endcan
                @can('view_contacts_email')
                    <flux:table.column>Email</flux:table.column>
                @endcan
                @can('view_contacts_phone')
                    <flux:table.column>Phone</flux:table.column>
                @endcan
                @can('view_contacts_country')
                    <flux:table.column>Country</flux:table.column>
                @endcan
                @can('view_contacts_languages')
                    <flux:table.column>Languages</flux:table.column>
                @endcan
            </flux:table.columns>

            <flux:table.rows>
                @foreach ($this->contacts as $contact)
                    <flux:table.row :key="$contact->id">
                        <flux:table.cell>{{ $contact->id }}</flux:table.cell>
                        @can('view_contacts_name')
                            <flux:table.cell variant="strong">
                                @can('view', $contact)
                                    <flux:link href="{{ route('contacts.show', $contact->id) }}" wire:navigate>
                                        {{ $contact->name }}
                                    </flux:link>
                                @else
                                    {{ $contact->name }}
                                @endcan
                            </flux:table.cell>
                        @endcan
                        @can('view_contacts_departments')
                            <flux:table.cell>
                                @if($contact->departments)
                                    @foreach($contact->departments as $department)
                                        <span>{{ \App\Enums\ContactPositions::from($department)->label() }}</span>@if(!$loop->last)
                                            ,
                                        @endif
                                    @endforeach
                                @else
                                    -
                                @endif
                            </flux:table.cell>
                        @endcan
                        @can('view_contacts_email')
                            <flux:table.cell>{{ $contact->email }}</flux:table.cell>
                        @endcan
                        @can('view_contacts_phone')
                            <flux:table.cell>{{ $contact->phone }}</flux:table.cell>
                        @endcan
                        @can('view_contacts_country')
                            <flux:table.cell>{{ $contact->country ? str_replace('_', ' ', $contact->country->name) : '-' }}</flux:table.cell>
                        @endcan
                        @can('view_contacts_languages')
                            <flux:table.cell>
                                @if($contact->languages)
                                    @foreach($contact->languages as $language)
                                        <span class="uppercase">{{ $language }}</span>@if(!$loop->last)
                                            ,
                                        @endif
                                    @endforeach
                                @else
                                    -
                                @endif
                            </flux:table.cell>
                        @endcan

                        <flux:table.cell class="flex justify-end">
                            <flux:dropdown>
                                <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions"
                                             variant="ghost"></flux:button>

                                <flux:menu>
                                    @can('view', $contact)
                                        <flux:menu.item wire:click="view({{ $contact->id }})" icon="eye">View
                                        </flux:menu.item>
                                    @endcan
                                    @can('update', $contact)
                                        <flux:menu.item wire:click="edit({{ $contact->id }})" icon="pencil-square">
                                            Edit
                                        </flux:menu.item>
                                    @endcan

                                    @can('delete', $contact)
                                        <flux:menu.separator/>
                                        <flux:menu.item
                                            wire:confirm="Are you sure you want to delete this contact? This action cannot be undone."
                                            wire:click="delete({{ $contact->id }})" variant="danger" icon="trash">Delete
                                        </flux:menu.item>
                                    @endcan
                                </flux:menu>
                            </flux:dropdown>
                        </flux:table.cell>
                    </flux:table.row>
                @endforeach
            </flux:table.rows>
        </flux:table>
    </div>
</flux:main>
