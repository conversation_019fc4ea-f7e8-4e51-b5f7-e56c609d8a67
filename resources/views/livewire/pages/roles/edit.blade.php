<flux:main container>

    <livewire:components.breadcrumbs/>
    <flux:separator variant="subtle" class="my-4"/>

    <flux:heading size="xl" level="1">Role: {{ $role->name }}</flux:heading>
    <flux:subheading size="lg" class="mb-6">Here's the role info and details</flux:subheading>

    <form wire:submit="save" class="flex flex-col gap-12 my-12">

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Role details</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the role info and details</flux:subheading>
            </div>

            <div class="col-span-2">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <flux:input wire:model="form.name" type="text" placeholder="Name" variant="filled" label="Name"
                                description="This will be publicly displayed."/>
                </div>
            </div>
        </div>

        <flux:separator variant="subtle"/>

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Role permission</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the role permission and details</flux:subheading>
            </div>

            <div class="col-span-2">
                <div class="grid grid-cols-1 md:grid-cols-1 gap-4">
                    <flux:table>
                        <flux:table.columns>
                            <flux:table.column></flux:table.column>
                            <flux:table.column>Read</flux:table.column>
                            <flux:table.column>Write</flux:table.column>
                            <flux:table.column>Attributes</flux:table.column>
                        </flux:table.columns>

                        <flux:table.rows>
                            @foreach($resources as $resource)
                                <flux:table.row>
                                    <flux:table.cell>
                                        <flux:text class="capitalize">{{ $resource['name'] }}</flux:text>
                                    </flux:table.cell>
                                    <flux:table.cell>
                                        <flux:checkbox wire:model="form.permissions.{{ $resource['value'] }}.read"/>
                                    </flux:table.cell>
                                    <flux:table.cell>
                                        <flux:checkbox wire:model="form.permissions.{{ $resource['value'] }}.write"/>
                                    </flux:table.cell>
                                    <flux:table.cell>
                                        @if($resource['attributes'])
                                            <flux:select size="sm" variant="listbox" multiple
                                                         placeholder="Choose industries..."
                                                         wire:model="form.permissions.{{ $resource['value'] }}.attributes">
                                                @foreach($resource['attributes'] as $attribute)
                                                    <flux:select.option
                                                        value="{{ $attribute['key'] }}"
                                                        :disabled="$attribute['required'] ?? false"
                                                    >
                                                        {{ $attribute['label'] }}
                                                        @if($attribute['required'] ?? false)
                                                            <span class="text-xs text-gray-400 ml-1">(Required)</span>
                                                        @endif
                                                    </flux:select.option>
                                                @endforeach
                                            </flux:select>
                                        @else
                                            <flux:select size="sm" placeholder="No attribute available"></flux:select>
                                        @endif
                                    </flux:table.cell>
                                </flux:table.row>
                            @endforeach

                        </flux:table.rows>

                    </flux:table>
                </div>
            </div>
        </div>

        <div class="flex justify-end gap-4">
            <flux:button @click="$dispatch('redirect-to-roles')" size="sm">Cancel</flux:button>
            <flux:button size="sm" type="submit" variant="primary">Save</flux:button>
        </div>

    </form>

</flux:main>

