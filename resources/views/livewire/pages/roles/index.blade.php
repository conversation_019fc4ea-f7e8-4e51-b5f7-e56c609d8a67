<flux:main container>
    <livewire:components.breadcrumbs />
    <flux:separator variant="subtle" class="my-4" />

    <flux:heading size="xl" level="1">Roles and permissions</flux:heading>
    <flux:subheading size="lg" class="mb-6">Here's the roles list and details</flux:subheading>

    <div class="flex flex-col gap-8 mt-12">

        {{-- Actions --}}
        <div class="flex flex-col">
            <div class="flex justify-between gap-2">
                <div>
                    <flux:input wire:model.live="search" placeholder="Search..." clearable size="sm"/>
                </div>
                <div>
                    @can('create', \Spatie\Permission\Models\Role::class)
                        <flux:button href="{{ route('roles.create') }}" wire:navigate size="sm">Create role</flux:button>
                    @endcan
                </div>
            </div>
        </div>

        {{-- Roles Listing --}}
        <flux:table :paginate="$this->roles">
            <flux:table.columns>
                <flux:table.column sortable :sorted="$sortBy === 'id'" :direction="$sortDirection" wire:click="sort('id')">#</flux:table.column>
                <flux:table.column sortable :sorted="$sortBy === 'name'" :direction="$sortDirection" wire:click="sort('name')">Name</flux:table.column>
                <flux:table.column>Users count</flux:table.column>
            </flux:table.columns>

            <flux:table.rows>
                @foreach ($this->roles as $role)
                    <flux:table.row :key="$role->id">
                        <flux:table.cell>{{ $role->id }}</flux:table.cell>
                        <flux:table.cell variant="strong">
                            @can('view', $role)
                                <flux:link href="{{ route('roles.show', $role->id) }}" wire:navigate>
                                    {{ $role->name }}
                                </flux:link>
                            @else
                                {{ $role->name }}
                            @endcan
                        </flux:table.cell>
                        <flux:table.cell>
                            {{ $role->users_count }} users
                        </flux:table.cell>
                        <flux:table.cell class="flex justify-end">
                            <flux:dropdown>
                                <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions" variant="ghost"></flux:button>

                                <flux:menu>
                                    @can('view', $role)
                                        <flux:menu.item wire:click="view({{ $role->id }})" icon="eye">View</flux:menu.item>
                                    @endcan
                                    @can('update', $role)
                                        <flux:menu.item wire:click="edit({{ $role->id }})" icon="pencil-square">Edit</flux:menu.item>
                                    @endcan

                                    @can('delete', $role)
                                        <flux:menu.separator />
                                        <flux:menu.item wire:confirm="Are you sure you want to delete this role? This action cannot be undone." wire:click="delete({{ $role->id }})" variant="danger" icon="trash">Delete</flux:menu.item>
                                    @endcan
                                </flux:menu>
                            </flux:dropdown>
                        </flux:table.cell>
                    </flux:table.row>
                @endforeach
            </flux:table.rows>
        </flux:table>
    </div>
</flux:main>
