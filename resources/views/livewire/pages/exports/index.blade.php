<flux:main container>

    <livewire:components.breadcrumbs />
    <flux:separator variant="subtle" class="my-4" />

    <flux:heading size="xl" level="1">Exports</flux:heading>
    <flux:subheading size="lg" class="mb-6">Here's the exports list and details</flux:subheading>

    <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        {{-- Product XLSX --}}
        <flux:card size="sm">
            <flux:subheading>Product Resources</flux:subheading>
            <flux:button href="#" target="_blank" variant="primary" class="w-full mt-2">Products</flux:button>

            <div class="flex items-center gap-2 mt-4">
                <span class="text-sm">Download related resources:</span>
            </div>
            
            <div class="grid grid-cols-1 gap-2 mt-2">
                <flux:button wire:click="downloadCustomProducts" target="_blank" size="xs">Custom products</flux:button>
                <flux:button wire:click="downloadMissingDiscountGroup" target="_blank" size="xs">Missing discount group</flux:button>
                <flux:button wire:click="downloadProductsWithoutImageUsedInOrders" target="_blank" size="xs">Without image used in orders</flux:button>
            </div>
        </flux:card>
    </div>

    <div class="flex flex-col gap-8 mt-12">

        {{-- Actions --}}
        <div class="flex flex-col">
            <div class="flex justify-between gap-2">
                <div>
                    <flux:input
                        wire:model.live.debounce.300ms="search"
                        placeholder="Search exports..."
                        clearable
                        size="sm"
                    />
                </div>
            </div>
        </div>

        {{-- Exports Listing --}}
        <div>
            @if ($exports->isEmpty())
                <flux:subheading class="flex justify-center pb-4">No results.</flux:subheading>
            @else
                <flux:table :paginate="$exports">
            <flux:table.columns>
                <flux:table.column>#</flux:table.column>
                <flux:table.column>Type / Source</flux:table.column>
                <flux:table.column>Status</flux:table.column>
                <flux:table.column>Progress</flux:table.column>
                <flux:table.column>Resource</flux:table.column>
                <flux:table.column>User</flux:table.column>
                <flux:table.column>Date</flux:table.column>
                <flux:table.column></flux:table.column>
            </flux:table.columns>

            <flux:table.rows>
                @forelse ($exports as $export)
                    <flux:table.row>
                        <flux:table.cell>{{ $export->id }}</flux:table.cell>
                        <flux:table.cell>
                            <div>
                                <flux:text variant="strong">{{ $export->type->label() }}</flux:text>
                                <flux:text size="sm" variant="muted">{{ $export->source->label() }}</flux:text>
                            </div>
                        </flux:table.cell>
                        <flux:table.cell>
                            <flux:badge color="{{ $export->status->color() }}" size="sm">
                                @if($export->status->value === 'processing')
                                    <flux:icon.loading variant="micro" class="mr-1" />
                                @else
                                    <flux:icon name="{{ $export->status->icon() }}" variant="micro" class="mr-1" />
                                @endif
                                {{ $export->status->label() }}
                            </flux:badge>
                        </flux:table.cell>
                        <flux:table.cell>
                            <div class="space-y-1">
                                <flux:text size="xs" variant="muted">
                                    {{ $export->processed_files }}/{{ $export->total_files }} files
                                    @if($export->failed_files > 0)
                                        ({{ $export->failed_files }} failed)
                                    @endif
                                </flux:text>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-400 h-2 rounded-full" style="width: {{ $export->progress_percentage }}%"></div>
                                </div>
                            </div>
                        </flux:table.cell>
                        <flux:table.cell>
                            @if($export->resource_id && $export->resource_type)
                                <flux:text variant="strong">{{ class_basename($export->resource_type) }}</flux:text>
                                <flux:text size="xs" variant="muted">ID: {{ $export->resource_id }}</flux:text>
                                @if($export->resource && method_exists($export->resource, 'code'))
                                    <flux:text size="xs" variant="muted">{{ $export->resource->code }}</flux:text>
                                @endif
                            @else
                                <flux:text size="xs" variant="muted">N/A</flux:text>
                            @endif
                        </flux:table.cell>
                        <flux:table.cell>
                            @if($export->user)
                                <flux:text>{{ $export->user->name }}</flux:text>
                                <flux:text size="xs" variant="muted">{{ $export->user->email }}</flux:text>
                            @else
                                <flux:text size="xs" variant="muted">System</flux:text>
                            @endif
                        </flux:table.cell>
                        <flux:table.cell>
                            <div class="text-sm">
                                {{ $export->created_at->format('d/m/Y H:i') }}
                                @if($export->completed_at)
                                    <flux:text class="text-xs">
                                        Completed: {{ $export->completed_at->format('H:i') }}
                                    </flux:text>
                                @endif
                            </div>
                        </flux:table.cell>
                        <flux:table.cell class="flex justify-end">
                            <flux:dropdown>
                                <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions" variant="ghost"></flux:button>

                                <flux:menu>
                                    <flux:menu.item
                                        icon="exclamation-triangle"
                                        wire:click="showErrors({{ $export->id }})"
                                        :disabled="!$export->hasErrors()"
                                    >
                                        View Errors ({{ $export->failed_files }})
                                    </flux:menu.item>
                                </flux:menu>
                            </flux:dropdown>
                        </flux:table.cell>
                    </flux:table.row>
                @empty
                @endforelse

            </flux:table.rows>
        </flux:table>
        @endif
        </div>
    </div>

    {{-- Error Modal --}}
    <flux:modal name="error-modal" class="w-full md:max-w-4xl lg:max-w-6xl" wire:model="showErrorModal">
        <div class="flex flex-col gap-4 p-4">
            {{-- Header --}}
            <div>
                <flux:heading size="lg">Export Errors</flux:heading>
                @if($selectedExport)
                    <flux:subheading>Export #{{ $selectedExport->id }} - {{ $selectedExport->type->label() }} ({{ $selectedExport->failed_files }} errors)</flux:subheading>
                @endif
            </div>

            {{-- Body --}}
            @if($selectedExport)
                <flux:textarea
                    rows="20"
                    readonly
                    class="font-mono text-xs"
                >{{ $selectedExport->getFormattedErrorLog() }}</flux:textarea>
            @else
                <div class="text-center text-gray-500">No export data available</div>
            @endif
        </div>
    </flux:modal>
</flux:main>
