<flux:main container>

    <livewire:components.breadcrumbs/>
    <flux:separator variant="subtle" class="my-4"/>

    <flux:heading size="xl" level="1">Create a new collection</flux:heading>
    <flux:subheading size="lg" class="mb-6">Here's the collection info and details</flux:subheading>

    <form wire:submit="save" class="flex flex-col gap-12 mt-12">
        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Collection details</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the collection info and details</flux:subheading>
            </div>
            <div class="col-span-2">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    @can('view_collections_code')
                        <flux:input wire:model="form.code" icon="hashtag" variant="filled" label="Code"
                                    description="This will be publicly displayed."/>
                    @endcan
                    @can('view_collections_name')
                        <flux:input wire:model="form.name" variant="filled" label="Name"
                                    description="This will be publicly displayed."/>
                    @endcan
                    @can('view_collections_image')
                        <flux:input wire:model="form.image" type="file" size="sm" label="Logo"
                                    description="Upload the collection logo." badge="Optional"/>

                        @if ($this->form->image)
                            <div class="relative">
                                <img src="{{ $this->form->image->temporaryUrl() }}" class="w-full rounded-lg"/>
                                <div class="absolute top-0 right-0 p-2">
                                    <flux:button wire:click="unsetImage" size="sm" icon="x-mark"/>
                                </div>
                            </div>
                        @endif
                    @endcan
                </div>
            </div>
        </div>

        <div class="flex justify-end gap-4">
            <flux:button @click="$dispatch('redirect-to-collections')" size="sm">Cancel</flux:button>
            <flux:button size="sm" type="submit" variant="primary">Save</flux:button>
        </div>
    </form>
</flux:main>
