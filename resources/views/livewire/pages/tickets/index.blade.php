<flux:main container>
    <div class="flex justify-between items-start">
        <livewire:components.breadcrumbs/>
    </div>
    <flux:separator variant="subtle" class="my-4"/>

    <flux:heading size="xl" level="1">Tickets</flux:heading>
    <flux:subheading size="lg" class="mb-6">Here's the tickets list and details</flux:subheading>

    <div class="flex flex-col gap-8 mt-12">

        <div class="flex flex-col">
            <div class="flex justify-between gap-2">
                @if ($this->areFiltersActive())
                    <div class="flex justify-start">
                        <flux:button wire:click="clearSearchAndFilters" icon="x-mark" square size="sm"/>
                    </div>
                @endif
                <div class="grow">
                    <flux:input wire:model.live="search" placeholder="Search..." clearable size="sm"/>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <flux:field>
                <flux:select wire:model.live="selectedStatus" variant="listbox" size="sm" clearable
                             placeholder="Choose status...">
                    @foreach ($this->statuses as $status)
                        <flux:select.option value="{{ $status->value }}"
                                            :selected="$status->value === $selectedStatus">
                            {{ $status->label() }}
                        </flux:select.option>
                    @endforeach
                </flux:select>
            </flux:field>
            <flux:field>
                <flux:select wire:model.live="selectedModelType" variant="listbox" size="sm" clearable
                             placeholder="Choose model type...">
                    @foreach ($this->modelTypes as $modelType => $modelLabel)
                        <flux:select.option value="{{ $modelType }}" :selected="$modelType === $selectedModelType">
                            {{ $modelLabel }}
                        </flux:select.option>
                    @endforeach
                </flux:select>
            </flux:field>
            <flux:field>
                <flux:select wire:model.live="selectedDateRange" variant="listbox" size="sm" clearable
                             placeholder="Choose date range...">
                    @foreach ($this->dateRanges as $dateRange => $dateLabel)
                        <flux:select.option value="{{ $dateRange }}" :selected="$dateRange === $selectedDateRange">
                            {{ $dateLabel }}
                        </flux:select.option>
                    @endforeach
                </flux:select>
            </flux:field>
        </div>

        {{-- Tickets Listing --}}
        <flux:table :paginate="$this->tickets">
            <flux:table.columns>
                <flux:table.column sortable :sorted="$sortBy === 'id'" :direction="$sortDirection"
                                   wire:click="sort('id')">#
                </flux:table.column>
                <flux:table.column>Ticketable</flux:table.column>
                <flux:table.column sortable :sorted="$sortBy === 'name'" :direction="$sortDirection"
                                   wire:click="sort('name')">Title
                </flux:table.column>
                <flux:table.column sortable :sorted="$sortBy === 'status'" :direction="$sortDirection"
                                   wire:click="sort('status')">Status
                </flux:table.column>
                <flux:table.column>User</flux:table.column>
                <flux:table.column>Created</flux:table.column>
                <flux:table.column>Updated</flux:table.column>
            </flux:table.columns>

            <flux:table.rows>
                @foreach ($this->tickets as $ticket)
                    <flux:table.row :key="$ticket->id">
                        <flux:table.cell>{{ $ticket->id }}</flux:table.cell>
                        <flux:table.cell>
                            @if ($ticket->ticketable_id)
                                @if($ticket->resource_type->value === 'products')
                                    <flux:link
                                        href="{{ route('products.index') }}"
                                        wire:navigate>
                                        {{ class_basename($ticket->ticketable_type) }} {{ $ticket->ticketable_id }}
                                    </flux:link>
                                @else
                                    <flux:link
                                        href="{{ route($ticket->resource_type->value . '.show', $ticket->ticketable_id) }}"
                                        wire:navigate>
                                        {{ class_basename($ticket->ticketable_type) }} {{ $ticket->ticketable_id }}
                                    </flux:link>
                                @endif
                            @else
                                <flux:link
                                    href="{{ route($ticket->resource_type->value . '.index') }}"
                                    wire:navigate>
                                    {{ $ticket->resource_type->label()  }}
                                </flux:link>
                            @endif
                        </flux:table.cell>
                        <flux:table.cell>
                            <flux:link class="cursor-pointer">
                                <flux:modal.trigger name="ticket-details" wire:click="selectTicket({{ $ticket->id }})">
                                    <strong>{{ $ticket->title }}</strong>
                                </flux:modal.trigger>
                            </flux:link>
                        </flux:table.cell>
                        <flux:table.cell>
                            <flux:badge icon="{{ $ticket->status->icon() }}"
                                        color="{{ $ticket->status->color() }}"
                                        size="sm">{{ $ticket->status->label() }}</flux:badge>
                        </flux:table.cell>
                        <flux:table.cell>
                            {{ $ticket->user->email ?? '' }}
                        </flux:table.cell>
                        <flux:table.cell>
                            {{ $ticket->created_at->translatedFormat('j F Y H:i') }}
                        </flux:table.cell>
                        <flux:table.cell>
                            {{ $ticket->updated_at->diffForHumans() }}
                        </flux:table.cell>

                        <flux:table.cell class="flex justify-end">
                            <flux:dropdown>
                                <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions"
                                             variant="ghost"></flux:button>

                                <flux:menu>
                                    @can('update', $ticket)
                                        @if ($ticket->status->value !== 'open')
                                            <flux:menu.item wire:click="setStatus({{ $ticket->id }}, 'open')"
                                                            icon="document-text">
                                                Set Status to Open
                                            </flux:menu.item>
                                        @endif
                                        @if ($ticket->status->value !== 'progress')
                                            <flux:menu.item wire:click="setStatus({{ $ticket->id }}, 'progress')"
                                                            icon="arrow-path">
                                                Set Status to Progress
                                            </flux:menu.item>
                                        @endif
                                        @if ($ticket->status->value !== 'closed')
                                            <flux:menu.item wire:click="setStatus({{ $ticket->id }}, 'closed')"
                                                            icon="check-circle">
                                                Set Status to Closed
                                            </flux:menu.item>
                                        @endif
                                    @endcan

                                    @can('delete', $ticket)
                                        <flux:menu.separator/>
                                        <flux:menu.item
                                            wire:confirm="Are you sure you want to delete this ticket? This action cannot be undone."
                                            wire:click="delete({{ $ticket->id }})" variant="danger" icon="trash">Delete
                                        </flux:menu.item>
                                    @endcan
                                </flux:menu>
                            </flux:dropdown>
                        </flux:table.cell>
                    </flux:table.row>
                @endforeach
            </flux:table.rows>
        </flux:table>
    </div>

    <flux:modal name="ticket-details" class="w-11/12 md:w-2/3" :dismissible="true" @close="clearSelectedTicket">
        <div class="space-y-4">
            @if($selectedTicket)
                <div class="flex flex-col gap-y-2">
                    <flux:heading size="xl">{{ $selectedTicket->title }}</flux:heading>
                    <flux:text class="flex items-center gap-x-2 text-sm">
                        <flux:icon.users variant="micro" class="text-zinc-400"/>
                        {{ $selectedTicket->user->email ?? 'Unknown' }}
                        <flux:icon.calendar variant="micro" class="text-zinc-400"/>
                        {{ $selectedTicket->created_at->translatedFormat('j F Y H:i') }}
                    </flux:text>
                    <flux:select wire:model.live="selectedTicketModalStatus" variant="listbox" size="sm"
                                 placeholder="Choose status...">
                        @foreach ($this->statuses as $status)
                            <flux:select.option value="{{ $status->value }}"
                                                :selected="$status->value === $selectedTicketModalStatus">
                                <flux:text class="flex items-center gap-x-2 text-sm">
                                    <flux:icon :name="$status->icon()" variant="micro" class="text-zinc-400"/>
                                    {{ $status->label() }}
                                </flux:text>
                            </flux:select.option>
                        @endforeach
                    </flux:select>
                </div>

                <flux:separator/>

                <flux:textarea readonly rows="6" label="Description">{{ $selectedTicket->description }}</flux:textarea>

                <div class="flex gap-x-2">
                    <flux:spacer/>

                    <flux:modal.close>
                        <flux:button size="sm" variant="outline">Close</flux:button>
                    </flux:modal.close>

                    @can('update', $selectedTicket)
                        <flux:modal.close>
                            <flux:button size="sm" variant="primary"
                                         wire:click="setStatus({{ $selectedTicket->id }}, '{{ $selectedTicketModalStatus }}')">
                                Set status
                            </flux:button>
                        </flux:modal.close>
                    @endcan

                </div>
            @endif
        </div>
    </flux:modal>

</flux:main>
