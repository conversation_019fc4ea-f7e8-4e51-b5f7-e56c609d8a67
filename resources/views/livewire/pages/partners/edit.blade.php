<flux:main container>

    <livewire:components.breadcrumbs/>
    <flux:separator variant="subtle" class="my-4"/>

    <flux:heading size="xl" level="1">Partner: {{ $partner->company }}</flux:heading>
    <flux:subheading size="lg" class="mb-6">Here's the partner info and details</flux:subheading>

    <form wire:submit="save" class="flex flex-col gap-12 mt-12">
        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Partner details</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the partner info and details</flux:subheading>
            </div>

            <div class="col-span-2">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    @can('view_partners_type')
                        <flux:select searchable wire:model="form.type" variant="listbox" placeholder="Choose type..."
                                     label="Type" description="This will be publicly displayed.">
                            @foreach($partnerTypes as $partnerType)
                                <flux:select.option
                                    value="{{ $partnerType->value }}">{{ $partnerType->label() }}</flux:select.option>
                            @endforeach
                        </flux:select>
                    @endcan
                    <div></div>
                    @can('view_partners_company')
                        <flux:input wire:model="form.company" type="text" placeholder="Company Name" variant="filled"
                                    label="Company" description="This will be publicly displayed."/>
                    @endcan
                    @can('view_partners_email')
                        <flux:input wire:model="form.email" type="text" placeholder="<EMAIL>" icon="envelope"
                                    variant="filled" label="Email" description="This will be publicly displayed."/>
                    @endcan
                    @can('view_partners_internal_referent_id')
                        <flux:select searchable wire:model="form.internal_referent_id" variant="listbox" clearable
                                     placeholder="Choose internal referent..." label="Internal Referent"
                                     description="This will be publicly displayed." badge="Optional">
                            @foreach($internalReferents as $internalReferent)
                                <flux:select.option
                                    value="{{ $internalReferent->id }}">{{ $internalReferent->first_name . ' ' . $internalReferent->last_name }}</flux:select.option>
                            @endforeach
                        </flux:select>
                    @endcan
                    @can('view_partners_area_manager_id')
                        <flux:select searchable wire:model="form.area_manager_id" variant="listbox" clearable
                                     placeholder="Choose area manager..." label="Area Manager"
                                     description="This will be publicly displayed." badge="Optional">
                            @foreach($areaManagers as $areaManager)
                                <flux:select.option
                                    value="{{ $areaManager->id }}">{{ $areaManager->first_name . ' ' . $areaManager->last_name }}</flux:select.option>
                            @endforeach
                        </flux:select>
                    @endcan
                    @can('view_partners_country')
                        <flux:select searchable wire:model="form.country" variant="listbox" searchable clearable
                                     placeholder="Choose country for the partner..." label="Country"
                                     description="This will be publicly displayed." badge="Optional">
                            @foreach($countries as $country)
                                <flux:select.option
                                    value="{{ $country->value }}">{{ str_replace('_', ' ', $country->name) }}</flux:select.option>
                            @endforeach
                        </flux:select>
                    @endcan
                    @can('view_partners_countries_of_expertise')
                        <flux:field>
                            <flux:select searchable wire:model.live="form.countries_of_expertise" variant="listbox"
                                         searchable clearable multiple placeholder="Choose countries of expertise..."
                                         label="Countries of Expertise" description="This will be publicly displayed."
                                         badge="Optional">
                                @foreach ($countries_of_expertise as $country_of_expertise)
                                    <flux:select.option
                                        value="{{ $country_of_expertise->value }}">{{ str_replace('_', ' ', $country_of_expertise->name) }}</flux:select.option>
                                @endforeach
                            </flux:select>
                        </flux:field>
                    @endcan
                    @can('view_partners_payment_term_id')
                        <flux:select searchable wire:model="form.payment_term_id" variant="listbox" clearable
                                     placeholder="Choose payment term for the partner..." label="Payment Term"
                                     description="This will be publicly displayed." badge="Optional">
                            @foreach($paymentTerms as $paymentTerm)
                                <flux:select.option
                                    value="{{ $paymentTerm->id }}">{{ $paymentTerm->code . ' - ' . $paymentTerm->name }}</flux:select.option>
                            @endforeach
                        </flux:select>
                    @endcan
                </div>
            </div>
        </div>

        @canany(['view_partners_commercial_category', 'view_partners_priority'])
            <flux:separator variant="subtle"/>

            <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
                <div class="col-span-1">
                    <flux:heading size="lg" level="1">Partner details</flux:heading>
                    <flux:subheading size="md" class="mb-6">Here's the partner info and details</flux:subheading>
                </div>

                <div class="col-span-2">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        @can('view_partners_commercial_category')
                            <flux:select searchable wire:model="form.commercial_category" clearable variant="listbox"
                                         placeholder="Choose partner commercial category..." label="Commercial Category"
                                         description="This will be publicly displayed." badge="Optional">
                                @foreach($commercialCategories as $commercialCategory)
                                    <flux:select.option
                                        value="{{ $commercialCategory->value }}">{{ $commercialCategory->label() }}</flux:select.option>
                                @endforeach
                            </flux:select>
                        @endcan
                        @can('view_partners_priority')
                            <flux:radio.group wire:model="form.priority" label="Priority" variant="segmented"
                                              badge="Optional"
                                              description="This will be publicly displayed.">
                                @foreach ($partnerPriorities as $partnerPriority)
                                    <flux:radio value="{{ $partnerPriority->value }}"
                                                label="{{ $partnerPriority->label() }}"
                                                description="This will be publicly displayed."/>
                                @endforeach
                            </flux:radio.group>
                        @endcan
                    </div>
                </div>
            </div>
        @endcanany

        @canany(['view_partners_minimum_orderable', 'view_partners_handling_and_packaging', 'view_partners_delivery_terms'])

            <flux:separator variant="subtle"/>

            <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
                <div class="col-span-1">
                    <flux:heading size="lg" level="1">Sales info</flux:heading>
                    <flux:subheading size="md" class="mb-6">Here's the sales info and details</flux:subheading>
                </div>

                <div class="col-span-2">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        @can('view_partners_minimum_orderable')
                            <flux:input wire:model="form.minimum_orderable" type="number" step="0.01"
                                        icon="currency-euro"
                                        placeholder="Minimum Orderable" variant="filled" label="Minimum Orderable"
                                        description="This will be publicly displayed." badge="Optional"/>
                        @endcan
                        @can('view_partners_handling_and_packaging')
                            <flux:input wire:model="form.handling_and_packing" type="number" icon="receipt-percent"
                                        placeholder="Handling and Packing" variant="filled" label="Handling and Packing"
                                        description="This will be publicly displayed." badge="Optional"/>
                        @endcan
                        @can('view_partners_delivery_terms')
                            <flux:select searchable wire:model="form.delivery_terms" clearable variant="listbox"
                                         placeholder="Choose partner delivery terms..." label="Delivery Terms"
                                         description="This will be publicly displayed." badge="Optional">
                                @foreach($deliveryTerms as $deliveryTerm)
                                    <flux:select.option
                                        value="{{ $deliveryTerm->value }}">{{ $deliveryTerm->label() }}</flux:select.option>
                                @endforeach
                            </flux:select>
                        @endcan
                    </div>
                </div>
            </div>
        @endcanany

        <flux:separator variant="subtle"/>

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Extra info</flux:heading>
                {{-- <flux:subheading size="md" class="mb-6">Here's the brand info and details</flux:subheading> --}}
            </div>
            <div class="col-span-2">
                <div class="grid grid-cols-1 gap-4">
                    <flux:textarea wire:model="form.notes" variant="filled" label="Notes" badge="Optional"
                                   description="This will be publicly displayed."/>
                </div>
            </div>
        </div>

        <div class="flex justify-end gap-4">
            <flux:button @click="$dispatch('redirect-to-partners')" size="sm">Cancel</flux:button>
            <flux:button size="sm" type="submit" variant="primary">Save</flux:button>
        </div>

    </form>

</flux:main>
