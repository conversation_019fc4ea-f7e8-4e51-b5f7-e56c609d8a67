<flux:main container>
    <div class="flex justify-between items-start">
        <livewire:components.breadcrumbs/>
        <livewire:components.modals.create-ticket resourceType="partners" size="xs"/>
    </div>
    <flux:separator variant="subtle" class="my-4"/>

    <flux:heading size="xl" level="1">Partners</flux:heading>
    <flux:subheading size="lg" class="mb-6">Here's the partners list and details</flux:subheading>

    <div class="flex flex-col gap-8 mt-12">

        {{-- Actions --}}
        <div class="flex flex-col">
            <div class="flex justify-between gap-2">
                @if ($this->areFiltersActive())
                    <div class="flex justify-start">
                        <flux:button wire:click="clearSearchAndFilters" icon="x-mark" square size="sm"/>
                    </div>
                @endif
                <div class="grow">
                    <flux:input wire:model.live="search" placeholder="Search..." clearable size="sm"/>
                </div>
                <div>
                    @can('create', \App\Models\Partner::class)
                        <flux:button href="{{ route('partners.create') }}" wire:navigate size="sm">Create partner
                        </flux:button>
                    @endcan
                </div>
            </div>
        </div>

        {{-- Filtri --}}
        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <flux:field>
                <flux:select searchable wire:model.live="selectedType" variant="listbox" size="sm" clearable
                             placeholder="Choose type...">
                    @foreach ($types as $type)
                        <flux:select.option value="{{ $type->value }}">{{ $type->label() }}</flux:select.option>
                    @endforeach
                </flux:select>
            </flux:field>
            <flux:field>
                <flux:select searchable wire:model.live="selectedCommercialCategory" variant="listbox" size="sm" clearable
                             placeholder="Choose commercial category...">
                    @foreach ($commercialCategories as $category)
                        <flux:select.option value="{{ $category->value }}">{{ $category->label() }}</flux:select.option>
                    @endforeach
                </flux:select>
            </flux:field>
            <flux:field>
                <flux:select searchable wire:model.live="selectedPriority" variant="listbox" size="sm" clearable
                             placeholder="Choose priority...">
                    @foreach ($priorities as $priority)
                        <flux:select.option value="{{ $priority->value }}">{{ $priority->label() }}</flux:select.option>
                    @endforeach
                </flux:select>
            </flux:field>
        </div>

        {{-- Partners Listing --}}
        <flux:table :paginate="$this->partners">
            <flux:table.columns>
                <flux:table.column sortable :sorted="$sortBy === 'id'" :direction="$sortDirection"
                                   wire:click="sort('id')">#
                </flux:table.column>
                @can('view_partners_company')
                    <flux:table.column sortable :sorted="$sortBy === 'company'" :direction="$sortDirection"
                                       wire:click="sort('company')">Name
                    </flux:table.column>
                @endcan
                @can('view_partners_type')
                    <flux:table.column sortable :sorted="$sortBy === 'type'" :direction="$sortDirection"
                                       wire:click="sort('type')">Type
                    </flux:table.column>
                @endcan
                @can('view_partners_internal_referent_id')
                    <flux:table.column>Internal Ref.</flux:table.column>
                @endcan
                @can('view_partners_commercial_category')
                    <flux:table.column sortable :sorted="$sortBy === 'commercial_category'" :direction="$sortDirection"
                                       wire:click="sort('commercial_category')">Commercial Category
                    </flux:table.column>
                @endcan
                @can('view_partners_priority')
                    <flux:table.column sortable :sorted="$sortBy === 'priority'" :direction="$sortDirection"
                                       wire:click="sort('priority')">Priority
                    </flux:table.column>
                @endcan
            </flux:table.columns>

            <flux:table.rows>
                @foreach ($this->partners as $partner)
                    <flux:table.row :key="$partner->id">
                        <flux:table.cell>{{ $partner->id }}</flux:table.cell>
                        @can('view_partners_company')
                            <flux:table.cell variant="strong">
                                @can('view', $partner)
                                    <flux:link href="{{ route('partners.show', $partner->id) }}" wire:navigate>
                                        {{ $partner->company }}
                                    </flux:link>
                                @else
                                    {{ $partner->company }}
                                @endcan
                            </flux:table.cell>
                        @endcan
                        @can('view_partners_type')
                            <flux:table.cell>
                                <flux:badge icon="{{ $partner->type->icon() }}" color="{{ $partner->type->color() }}"
                                            size="sm" inset>{{ $partner->type->label() }}</flux:badge>
                            </flux:table.cell>
                        @endcan
                        @can('view_partners_internal_referent_id')
                            <flux:table.cell>{{ $partner->internalReferent ? ($partner->internalReferent->first_name . ' ' . $partner->internalReferent->last_name) : '-' }}</flux:table.cell>
                        @endcan
                        @can('view_partners_commercial_category')
                            <flux:table.cell>{{ $partner->commercial_category ? $partner->commercial_category->label() : '-' }}</flux:table.cell>
                        @endcan
                        @can('view_partners_priority')
                            <flux:table.cell>{{ $partner->priority ? $partner->priority->label() : '-' }}</flux:table.cell>
                        @endcan

                        <flux:table.cell class="flex justify-end">
                            <flux:dropdown>
                                <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions"
                                             variant="ghost"></flux:button>

                                <flux:menu>
                                    @can('view', $partner)
                                        <flux:menu.item wire:click="view({{ $partner->id }})" icon="eye">View
                                        </flux:menu.item>
                                    @endcan
                                    @can('update', $partner)
                                        <flux:menu.item wire:click="edit({{ $partner->id }})" icon="pencil-square">
                                            Edit
                                        </flux:menu.item>
                                    @endcan

                                    @can('delete', $partner)
                                        <flux:menu.separator/>
                                        <flux:menu.item
                                            wire:confirm="Are you sure you want to delete this partner? This action cannot be undone."
                                            wire:click="delete({{ $partner->id }})" variant="danger" icon="trash">Delete
                                        </flux:menu.item>
                                    @endcan
                                </flux:menu>
                            </flux:dropdown>
                        </flux:table.cell>
                    </flux:table.row>
                @endforeach
            </flux:table.rows>
        </flux:table>
    </div>
</flux:main>
