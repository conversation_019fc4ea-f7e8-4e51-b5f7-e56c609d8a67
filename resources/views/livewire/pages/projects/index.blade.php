<flux:main container>
    <div class="flex justify-between items-start">
        <livewire:components.breadcrumbs/>
        <livewire:components.modals.create-ticket resourceType="projects" size="xs"/>
    </div>
    <flux:separator variant="subtle" class="my-4"/>

    <flux:heading size="xl" level="1">Projects</flux:heading>
    <flux:subheading size="lg" class="mb-6">Here's the projects list and details</flux:subheading>

    <div class="flex flex-col gap-8 mt-12">

        {{-- Actions --}}
        <div class="flex flex-col">
            <div class="flex justify-between gap-2">
                @if ($this->areFiltersActive())
                    <div class="flex justify-start">
                        <flux:button wire:click="clearSearchAndFilters" icon="x-mark" square size="sm"/>
                    </div>
                @endif
                <div class="grow">
                    <flux:input wire:model.live="search" placeholder="Search..." clearable size="sm"/>
                </div>
                <div>
                    @can('create', \App\Models\Project\Project::class)
                        <flux:button href="{{ route('projects.create') }}" wire:navigate size="sm">Create project
                        </flux:button>
                    @endcan
                </div>
            </div>
        </div>

        {{-- Filtri --}}
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-4">
            <flux:field>
                <flux:select searchable wire:model.live="selectedStatus" variant="listbox" size="sm" clearable
                             placeholder="Choose status...">
                    @foreach ($statuses as $status)
                        <flux:select.option value="{{ $status->value }}">{{ $status->label() }}</flux:select.option>
                    @endforeach
                </flux:select>
            </flux:field>
            <flux:field>
                <flux:select searchable wire:model.live="selectedProjectType" variant="listbox" size="sm" clearable
                             placeholder="Choose project type...">
                    @foreach ($projectTypes as $projectType)
                        <flux:select.option value="{{ $projectType->value }}">{{ $projectType->label() }}</flux:select.option>
                    @endforeach
                </flux:select>
            </flux:field>
            <flux:field>
                <flux:select searchable wire:model.live="hasOrders" variant="listbox" size="sm" clearable
                             placeholder="Has orders...">
                    @foreach ($ordersOptions as $value => $label)
                        <flux:select.option value="{{ $value }}">{{ $label }}</flux:select.option>
                    @endforeach
                </flux:select>
            </flux:field>
            <flux:field>
                <flux:select searchable wire:model.live="hasGraphicPresentations" variant="listbox" size="sm" clearable
                             placeholder="Has graphic presentations...">
                    @foreach ($graphicPresentationsOptions as $value => $label)
                        <flux:select.option value="{{ $value }}">{{ $label }}</flux:select.option>
                    @endforeach
                </flux:select>
            </flux:field>
        </div>

        {{-- Projects Listing --}}
        @if ($this->projects->isEmpty())
            <flux:subheading class="flex justify-center pb-4">No results.</flux:subheading>
        @else
            <flux:table :paginate="$this->projects">
                <flux:table.columns>
                    <flux:table.column sortable :sorted="$sortBy === 'id'" :direction="$sortDirection"
                                       wire:click="sort('id')">#
                    </flux:table.column>
                    @canany(['view_projects_name', 'view_projects_project_code'])
                        <flux:table.column>Description</flux:table.column>
                    @endcanany
                    <flux:table.column sortable :sorted="$sortBy === 'status'" :direction="$sortDirection"
                                       wire:click="sort('status')">Status
                    </flux:table.column>
                    @can('view_projects_interla_referent_id')
                        <flux:table.column>Internal Ref.</flux:table.column>
                    @endcan
                    @can('view_projects_client_id')
                        <flux:table.column>Client</flux:table.column>
                    @endcan
                    @can('view_projects_partner_id')
                        <flux:table.column>Partner</flux:table.column>
                    @endcan
                </flux:table.columns>

                <flux:table.rows>
                    @foreach ($this->projects as $project)
                        <flux:table.row :key="$project->id">
                            <flux:table.cell>{{ $project->id }}</flux:table.cell>
                            @canany(['view_projects_name', 'view_projects_project_code'])
                                <flux:table.cell>
                                    @can('view_projects_name')
                                        @can('view', $project)
                                            <flux:link href="{{ route('projects.show', $project->id) }}" variant="strong" wire:navigate>
                                                {{ $project->name }}
                                            </flux:link>
                                        @else
                                            <flux:text variant="strong">{{ $project->name }}</flux:text>
                                        @endcan
                                    @endcan
                                    @can('view_projects_project_code')
                                        <flux:text class="italic mt-2">{{ $project->project_code ?? '-' }}</flux:text>
                                    @endcan
                                </flux:table.cell>
                            @endcanany
                            <flux:table.cell>
                                <flux:badge icon="{{ $project->status->icon() }}"
                                            color="{{ $project->status->color() }}" size="sm"
                                            inset>{{ $project->status->label() }}</flux:badge>
                            </flux:table.cell>
                            @can('view_projects_internal_referent_id')
                                <flux:table.cell>{{ $project->internalReferent ? ($project->internalReferent->first_name . ' ' . $project->internalReferent->last_name) : '-' }}</flux:table.cell>
                            @endcan
                            @can('view_projects_client_id')
                                <flux:table.cell variant="strong">
                                    @if ($project->client)
                                        @can('view', $project->client)
                                            <flux:link href="{{ route('clients.show', $project->client->id ?? '') }}"
                                                       wire:navigate>
                                                {{ $project->client->company ?? '-' }}
                                            </flux:link>
                                        @else
                                            {{ $project->client->company ?? '-' }}
                                        @endcan
                                    @else
                                        -
                                    @endif
                                </flux:table.cell>
                            @endcan
                            @can('view_projects_partner_id')
                                <flux:table.cell variant="strong">
                                    @if ($project->partner)
                                        @can('view', $project->partner)
                                            <flux:link href="{{ route('partners.show', $project->partner->id ?? '') }}"
                                                       wire:navigate>
                                                {{ $project->partner->company ?? '-' }}
                                            </flux:link>
                                        @else
                                            {{ $project->partner->company ?? '-' }}
                                        @endcan
                                    @else
                                        -
                                    @endif
                                </flux:table.cell>
                            @endcan
                            <flux:table.cell class="flex justify-end">
                                <flux:dropdown>
                                    <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions"
                                                 variant="ghost"></flux:button>

                                    <flux:menu>
                                        @can('view', $project)
                                            <flux:menu.item wire:click="view({{ $project->id }})" icon="eye">View
                                            </flux:menu.item>
                                        @endcan
                                        @can('update', $project)
                                            <flux:menu.item wire:click="edit({{ $project->id }})" icon="pencil-square">
                                                Edit
                                            </flux:menu.item>
                                        @endcan

                                        @can('delete', $project)
                                            <flux:menu.separator/>
                                            <flux:menu.item
                                                wire:confirm="Are you sure you want to delete this project? This action cannot be undone."
                                                wire:click="delete({{ $project->id }})" variant="danger" icon="trash">
                                                Delete
                                            </flux:menu.item>
                                        @endcan
                                    </flux:menu>
                                </flux:dropdown>
                            </flux:table.cell>
                        </flux:table.row>
                    @endforeach
                </flux:table.rows>
            </flux:table>
        @endif
    </div>
</flux:main>
