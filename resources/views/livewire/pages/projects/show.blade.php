<flux:main container>

    <div class="flex justify-between items-start">
        <livewire:components.breadcrumbs/>
        <livewire:components.modals.create-ticket :resourceValue="$project" size="xs"/>
    </div>
    <flux:separator variant="subtle" class="my-4"/>

    <div class="flex justify-between">
        <div>
            <flux:heading size="xl" level="1">Project: {{ $project->name }}</flux:heading>
            <flux:subheading size="lg" class="mb-6">Here's the project info and details</flux:subheading>
        </div>
        <div class="flex gap-2">
            <div>
                <flux:badge icon="hashtag"
                            size="sm md:lg">{{ $project->project_code ?? "PRJ_XXX00-000000" }}</flux:badge>
            </div>
            <div>
                <flux:badge icon="calendar" size="sm md:lg">{{ $project->date->format('d-m-y') }}</flux:badge>
            </div>
            <div>
                <flux:badge icon="{{ $project->status->icon() }}" color="{{ $project->status->color() }}"
                            size="sm md:lg">{{ $project->status->label() }}</flux:badge>
            </div>
        </div>
    </div>

    {{-- Actions --}}
    <div class="flex flex-col my-4">
        <div class="flex justify-end gap-2">
            <div class="flex items-center">
            </div>
            <div>
                @canany(['create', 'update', 'delete'], $project)
                    <flux:dropdown>
                        <flux:button size="sm" icon-trailing="chevron-down">Actions</flux:button>
                        <flux:menu>
                            @can('create', \App\Models\Project\Project::class)
                                <flux:menu.item wire:click="" icon="check" disabled>Submit</flux:menu.item>
                            @endcan

                            @can('update', $project)
                                <flux:menu.separator/>
                                <flux:menu.item wire:click="edit({{ $project->id }})" icon="pencil-square">Edit
                                </flux:menu.item>
                            @endcan

                            @can('delete', $project)
                                <flux:menu.separator/>
                                <flux:menu.item
                                    wire:confirm="Are you sure you want to delete this project? This action cannot be undone."
                                    wire:click="delete({{ $project->id }})" variant="danger" icon="trash">Delete
                                </flux:menu.item>
                            @endcan
                        </flux:menu>
                    </flux:dropdown>
                @endcan
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
        {{-- Clients and Partner --}}
        <flux:card size="sm" class="flex flex-col justify-between gap-4">
            <div>
                <flux:subheading class="mb-1">Clients details</flux:subheading>
                <flux:separator class="mb-2"></flux:separator>
            </div>
            <div>
                <flux:heading size="lg" class="mb-1 flex justify-between"><span class="text-sm font-light">Client</span>
                    <flux:link href="{{ route('clients.show', $project->client->id ?? '') }}"
                               wire:navigate>{{ $project->client->company ?? '-' }}</flux:link>
                </flux:heading>
                <flux:heading size="lg" class="mb-1 flex justify-between"><span
                        class="text-sm font-light">Partner</span>
                    <flux:link href="{{ route('partners.show', $project->partner->id ?? '') }}"
                               wire:navigate>{{ $project->partner->company ?? '-' }}</flux:link>
                </flux:heading>
                <flux:heading size="lg" class="mb-1 flex justify-between"><span
                        class="text-sm font-light">Int. Referent</span>{{ $project->internalReferent ? $project->internalReferent->first_name . $project->internalReferent->last_name : '-' }}
                </flux:heading>
                <flux:heading size="lg" class="mb-1 flex justify-between"><span
                        class="text-sm font-light">Area Manager</span>{{ $project->areaManager ? $project->areaManager->first_name . $project->areaManager->last_name : '-' }}
                </flux:heading>
            </div>
        </flux:card>

        {{-- Invoicing and Shipping --}}
        <flux:card size="sm" class="flex flex-col justify-between gap-4">
            <div>
                <div>
                    <flux:subheading class="mb-1">Invoicing address</flux:subheading>
                    <flux:separator class="mb-2"></flux:separator>
                </div>
                <flux:heading class="mt-1">{{ $project->invoicingAddress->company ?? '-' }}</flux:heading>
            </div>
            <div>
                <div>
                    <flux:subheading class="mb-1">Shipping address</flux:subheading>
                    <flux:separator class="mb-2"></flux:separator>
                </div>
                <flux:heading class="mt-1">{{ $project->shippingAddress->company ?? '-' }}</flux:heading>
            </div>
            <flux:heading size="lg" class="mb-1 flex justify-between"><span
                    class="text-sm font-light">Payment Term</span> {{ $project->paymentTerm ? $project->paymentTerm->name : '-' }}
            </flux:heading>
        </flux:card>

        {{-- Extra Info --}}
        <flux:card size="sm" class="flex flex-col justify-between gap-4">
            <div>
                <flux:subheading class="mb-1">Extra info</flux:subheading>
                <flux:separator class="mb-2"></flux:separator>
            </div>
            <div class="flex flex-col">
                <flux:heading size="lg" class="mb-1 flex justify-between"><span class="text-sm font-light">Type</span>
                    <flux:badge icon="{{ $project->type ? $project->type->icon() : '' }}"
                                color="{{ $project->type ? $project->type->color() : '' }}" size="sm"
                                inset>{{ $project->type ? $project->type->label() : '-' }}</flux:badge>
                </flux:heading>
                <flux:heading size="lg" class="mb-1 mt-4 flex justify-between"><span
                        class="text-sm font-light">Name</span> {{ $project->type_name ?? '-' }}</flux:heading>
                <flux:heading size="lg" class="mb-1 flex justify-between"><span class="text-sm font-light">Link</span>
                    <flux:link href="{{ $project->type_link ?? '#' }}"
                               target="_blank">{{ $project->type_link ?? '-' }}</flux:link>
                </flux:heading>
            </div>
        </flux:card>
    </div>

    <div class="flex flex-col gap-12 mt-12">
        <flux:separator variant="subtle"/>

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Extra info</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the project extra info</flux:subheading>
            </div>

            <div class="col-span-2">
                <div class="grid grid-cols-1 gap-4">
                    <flux:editor wire:model="form.description" disabled label="Description"
                                 description="This will be publicly displayed." badge="Optional"/>
                </div>
            </div>
        </div>
    </div>

    @can('read_orders')
        <flux:separator variant="subtle" class="my-12"/>

        {{-- Orders Listing --}}
        <livewire:components.inner-table.orders :resourceType="class_basename($project)" :resourceValue="$project"/>
    @endcan

    <flux:separator variant="subtle" class="my-12"/>

    {{-- Graphic Presentations Listing --}}
    <livewire:components.inner-table.graphic-presentations :resourceType="class_basename($project)"
                                                           :resourceValue="$project"/>

    <flux:separator variant="subtle" class="my-12"/>

    {{-- Assets Listing --}}
    <livewire:components.inner-table.assets :resourceType="class_basename($project)" :resourceValue="$project"/>

    <flux:separator variant="subtle" class="my-12"/>

    {{-- Meetings Listing --}}
    <livewire:components.inner-table.meetings :resourceType="class_basename($project)" :resourceValue="$project"/>

    <flux:separator variant="subtle" class="my-12"/>

    {{-- Moodboards Listing --}}
    <livewire:components.inner-table.moodboards :resourceType="class_basename($project)" :resourceValue="$project"/>

    <flux:separator variant="subtle" class="my-12"/>

    {{-- Collaborators Listing --}}
    <livewire:components.inner-table.collaborators :resourceType="class_basename($project)" :resourceValue="$project"/>

    @can('read_users')
        <flux:separator variant="subtle" class="my-12"/>

        {{-- Users Listing --}}
        <livewire:components.inner-table.users :resourceType="class_basename($project)" :resourceValue="$project"/>
    @endcan
</flux:main>
