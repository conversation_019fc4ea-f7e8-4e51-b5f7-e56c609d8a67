<flux:main container>

    <livewire:components.breadcrumbs />
    <flux:separator variant="subtle" class="my-4" />

    <flux:heading size="xl" level="1">Imports</flux:heading>
    <flux:subheading size="lg" class="mb-6">Manage your data imports and view their status</flux:subheading>

    <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        {{-- Client XLSX Template --}}
        <flux:card size="sm">
            <flux:subheading>Client Resource</flux:subheading>
            <flux:button href="https://bergomi-webapp.fra1.digitaloceanspaces.com/import-template/clients.xlsx" target="_blank" variant="primary" class="w-full mt-2">Client</flux:button>

            <div class="flex items-center gap-2 mt-4">
                {{-- <flux:icon.arrow-trending-up variant="micro" class="text-green-600 dark:text-green-500" /> --}}
                <span class="text-sm">Download related resources:</span>
            </div>
            
            <div class="grid grid-cols-2 gap-2 mt-2">
                <flux:button href="https://bergomi-webapp.fra1.digitaloceanspaces.com/import-template/client-addresses.xlsx" target="_blank" size="xs">Address</flux:button>
                <flux:button href="https://bergomi-webapp.fra1.digitaloceanspaces.com/import-template/client-brands.xlsx" target="_blank" size="xs">Brand</flux:button>
                <flux:button href="https://bergomi-webapp.fra1.digitaloceanspaces.com/import-template/client-contacts.xlsx" target="_blank" size="xs">Contact</flux:button>
                <flux:button href="https://bergomi-webapp.fra1.digitaloceanspaces.com/import-template/client-discount-groups.xlsx" target="_blank" size="xs">Discount group</flux:button>
            </div>
        </flux:card>

        {{-- Partner XLSX Template --}}
        <flux:card size="sm">
            <flux:subheading>Partner Resource</flux:subheading>
            <flux:button href="https://bergomi-webapp.fra1.digitaloceanspaces.com/import-template/partners.xlsx" target="_blank" variant="primary" class="w-full mt-2">Partner</flux:button>

            <div class="flex items-center gap-2 mt-4">
                {{-- <flux:icon.arrow-trending-up variant="micro" class="text-green-600 dark:text-green-500" /> --}}
                <span class="text-sm">Download related resources:</span>
            </div>
            
            <div class="grid grid-cols-2 gap-2 mt-2">
                <flux:button href="https://bergomi-webapp.fra1.digitaloceanspaces.com/import-template/partner-addresses.xlsx" target="_blank" size="xs">Address</flux:button>
                <flux:button href="https://bergomi-webapp.fra1.digitaloceanspaces.com/import-template/partner-brands.xlsx" target="_blank" size="xs">Brand</flux:button>
                <flux:button href="https://bergomi-webapp.fra1.digitaloceanspaces.com/import-template/partner-contacts.xlsx" target="_blank" size="xs">Contact</flux:button>
                <flux:button href="https://bergomi-webapp.fra1.digitaloceanspaces.com/import-template/partner-discount-groups.xlsx" target="_blank" size="xs">Discount group</flux:button>
            </div>
        </flux:card>

        {{-- Brand XLSX Template --}}
        <flux:card size="sm">
            <flux:subheading>Brand Resource</flux:subheading>
            <flux:button href="https://bergomi-webapp.fra1.digitaloceanspaces.com/import-template/brands.xlsx" target="_blank" variant="primary" class="w-full mt-2">Brand</flux:button>

            <div class="flex items-center gap-2 mt-4">
                {{-- <flux:icon.arrow-trending-up variant="micro" class="text-green-600 dark:text-green-500" /> --}}
                <span class="text-sm">Download related resources:</span>
            </div>
            
            <div class="grid grid-cols-2 gap-2 mt-2">
                <flux:button href="https://bergomi-webapp.fra1.digitaloceanspaces.com/import-template/brand-contacts.xlsx" target="_blank" size="xs">Contact</flux:button>
                <flux:button href="https://bergomi-webapp.fra1.digitaloceanspaces.com/import-template/discount-groups.xlsx" target="_blank" size="xs">Discount group</flux:button>
            </div>
        </flux:card>

        {{-- Supplier XLSX Template --}}
        <flux:card size="sm">
            <flux:subheading>Supplier Resource</flux:subheading>
            <flux:button href="https://bergomi-webapp.fra1.digitaloceanspaces.com/import-template/suppliers.xlsx" target="_blank" variant="primary" class="w-full mt-2">Supplier</flux:button>

            <div class="flex items-center gap-2 mt-4">
                {{-- <flux:icon.arrow-trending-up variant="micro" class="text-green-600 dark:text-green-500" /> --}}
                <span class="text-sm">Download related resources:</span>
            </div>
            
            <div class="grid grid-cols-2 gap-2 mt-2">
                <flux:button href="https://bergomi-webapp.fra1.digitaloceanspaces.com/import-template/supplier-addresses.xlsx" target="_blank" size="xs">Address</flux:button>
                <flux:button href="#" target="_blank" size="xs">Contact (TBD)</flux:button>
            </div>
        </flux:card>
    </div>

    <div class="flex flex-col gap-8 mt-12">

        {{-- Actions --}}
        <div class="flex flex-col">
            <div class="flex justify-between gap-2">
                <div>
                    <flux:input
                        wire:model.live.debounce.300ms="search"
                        placeholder="Search imports..."
                        clearable
                        size="sm"
                    />
                </div>
                <div class="flex items-center gap-2">
                    <flux:button href="{{ route('imports.create') }}" wire:navigate size="sm">Create import</flux:button>
                </div>
            </div>
        </div>

        {{-- Imports Listing --}}
        <div>
            @if ($imports->isEmpty())
                <flux:subheading class="flex justify-center pb-4">No results.</flux:subheading>
            @else
                <flux:table :paginate="$imports">
            <flux:table.columns>
                <flux:table.column>#</flux:table.column>
                <flux:table.column>Type / Source</flux:table.column>
                <flux:table.column>Status</flux:table.column>
                <flux:table.column>Progress</flux:table.column>
                <flux:table.column>User</flux:table.column>
                <flux:table.column>Date</flux:table.column>
                <flux:table.column></flux:table.column>
            </flux:table.columns>

            <flux:table.rows>
                @forelse ($imports as $import)
                    <flux:table.row>
                        <flux:table.cell>{{ $import->id }}</flux:table.cell>
                        <flux:table.cell>
                            <div>
                                <flux:text variant="strong">{{ $import->type->label() }}</flux:text>
                                <flux:text size="sm" variant="muted">{{ $import->source->label() }}</flux:text>
                            </div>
                        </flux:table.cell>
                        <flux:table.cell>
                            <flux:badge color="{{ $import->status->color() }}" size="sm">
                                @if($import->status->value === 'processing')
                                    <flux:icon.loading variant="micro" class="mr-1" />
                                @else
                                    <flux:icon name="{{ $import->status->icon() }}" variant="micro" class="mr-1" />
                                @endif
                                {{ $import->status->label() }}
                            </flux:badge>
                        </flux:table.cell>
                        <flux:table.cell>
                            @if($import->total_jobs > 0)
                                <div class="space-y-1">
                                    <flux:text size="xs" variant="muted">
                                        {{ $import->processed_jobs }}/{{ $import->total_jobs }}
                                        @if($import->failed_jobs > 0)
                                            ({{ $import->failed_jobs }} failed)
                                        @endif
                                    </flux:text>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-blue-400 h-2 rounded-full" style="width: {{ $import->progress_percentage }}%"></div>
                                    </div>
                                </div>
                            @else
                                <flux:text size="xs" variant="muted"> - </flux:text>
                            @endif
                        </flux:table.cell>
                        <flux:table.cell>
                            @if($import->user)
                                <flux:text>{{ $import->user->name }}</flux:text>
                            @else
                                <flux:text>System</flux:text>
                            @endif
                        </flux:table.cell>
                        <flux:table.cell>
                            <div class="text-sm">
                                {{ $import->created_at->format('d/m/Y H:i') }}
                                @if($import->completed_at)
                                    <flux:text class="text-xs">
                                        Completed: {{ $import->completed_at->format('H:i') }}
                                    </flux:text>
                                @endif
                            </div>
                        </flux:table.cell>
                        <flux:table.cell class="flex justify-end">
                            <flux:dropdown>
                                <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions" variant="ghost"></flux:button>

                                <flux:menu>
                                    <flux:menu.item
                                        icon="exclamation-triangle"
                                        wire:click="showErrors({{ $import->id }})"
                                        :disabled="!$import->hasErrors()"
                                    >
                                        View Errors ({{ $import->failed_jobs }})
                                    </flux:menu.item>
                                </flux:menu>
                            </flux:dropdown>
                        </flux:table.cell>
                    </flux:table.row>
                @empty
                @endforelse

            </flux:table.rows>
        </flux:table>
        @endif
        </div>
    </div>

    {{-- Error Modal --}}
    <flux:modal name="error-modal" class="w-full md:max-w-4xl lg:max-w-6xl" wire:model="showErrorModal">
        <div class="flex flex-col gap-4 p-4">
            {{-- Header --}}
            <div>
                <flux:heading size="lg">Import Errors</flux:heading>
                @if($selectedImport)
                    <flux:subheading>Import #{{ $selectedImport->id }} - {{ $selectedImport->type->label() }} ({{ $selectedImport->failed_jobs }} errors)</flux:subheading>
                @endif
            </div>

            {{-- Body --}}
            @if($selectedImport)
                <flux:textarea
                    rows="20"
                    readonly
                    class="font-mono text-xs"
                >{{ $selectedImport->getFormattedErrorLog() }}</flux:textarea>
            @else
                <div class="text-center text-gray-500">No import data available</div>
            @endif
        </div>
    </flux:modal>
</flux:main>
