<flux:main container>
    <div class="flex justify-between items-start">
        <livewire:components.breadcrumbs/>
        <livewire:components.modals.create-ticket :resourceValue="$discountGroup" size="xs"/>
    </div>
    <flux:separator variant="subtle" class="my-4"/>

    <flux:heading size="xl" level="1">Discount group: {{ $discountGroup->code }}</flux:heading>
    <flux:subheading size="lg" class="mb-6">Here's the price group info and details</flux:subheading>

    <div class="flex flex-col gap-12 my-12">

        {{-- Actions --}}
        <div class="flex flex-col">
            <div class="flex justify-between gap-2">
                <div class="flex items-center">
                </div>
                <div>
                    @canany(['update', 'delete'], $discountGroup)
                        <flux:dropdown>
                            <flux:button size="sm" icon-trailing="chevron-down">Actions</flux:button>
                            <flux:menu>
                                @can('update', $discountGroup)
                                    <flux:menu.item wire:click="edit({{ $discountGroup->id }})" icon="pencil-square">
                                        Edit
                                    </flux:menu.item>
                                @endcan
                                @can('delete', $discountGroup)
                                    <flux:menu.separator/>
                                    <flux:menu.item
                                        wire:confirm="Are you sure you want to delete this discount group? This action cannot be undone."
                                        wire:click="delete({{ $discountGroup->id }})" variant="danger" icon="trash">
                                        Delete
                                    </flux:menu.item>
                                @endcan
                            </flux:menu>
                        </flux:dropdown>
                    @endcanany
                </div>
            </div>
        </div>

        {{-- Price Group Details --}}
        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Discount group details</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the price group info and details</flux:subheading>
            </div>

            <div class="col-span-2">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    @can('view_discount_groups_code')
                        <flux:input wire:model="form.code" type="text" readonly placeholder="Code" variant="filled"
                                    label="Code" description="Code of the discount group."/>
                    @endcan
                    @can('view_discount_groups_discount')
                        <flux:input wire:model="form.discount" readonly type="number" variant="filled"
                                    icon="receipt-percent" label="Discount"
                                    description="Enter the discount percentage."/>
                    @endcan
                    @can('view_discount_groups_brand_id')
                        <flux:select searchable wire:model="form.brand_id" disabled variant="listbox" searchable
                                     placeholder="Choose brand..." label="Brand" description="Enter the brand."
                                     badge="Optional">
                            @foreach($brands as $brand)
                                <flux:select.option value="{{ $brand->id }}">{{ $brand->name }}</flux:select.option>
                            @endforeach
                        </flux:select>
                    @endcan
                    @can('view_discount_groups_description')
                        <flux:input wire:model="form.description" readonly variant="filled" label="Description"
                                    description="Description of the discount group."/>
                    @endcan
                </div>
            </div>
        </div>

    </div>
</flux:main>
