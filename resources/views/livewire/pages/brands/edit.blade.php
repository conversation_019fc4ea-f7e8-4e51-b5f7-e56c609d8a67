<flux:main container>

    <livewire:components.breadcrumbs/>
    <flux:separator variant="subtle" class="my-4"/>

    <flux:heading size="xl" level="1">Brand: {{ $brand->name }}</flux:heading>
    <flux:subheading size="lg" class="mb-6">Update the brand info and details</flux:subheading>

    <form wire:submit="update" class="flex flex-col gap-12 mt-12">
        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Brand details</flux:heading>
                {{-- <flux:subheading size="md" class="mb-6">Here's the brand info and details</flux:subheading> --}}
            </div>
            <div class="col-span-2">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4" x-data="{ imageName: '', imagePreview: '' }">
                    @can('view_brands_prefix')
                        <flux:input wire:model="form.prefix" icon="hashtag" variant="filled" label="Prefix"
                                    description="This will be publicly displayed."/>
                    @endcan
                    @can('view_brands_name')
                        <flux:input wire:model="form.name" variant="filled" label="Name"
                                    description="This will be publicly displayed."/>
                    @endcan
                    @can('view_brands_image')
                        <flux:input wire:model.live="form.image" type="file" size="sm" label="Logo"
                                    description="Upload the brand logo." badge="Optional"/>

                        @if ($this->form->image && Storage::disk(config('filesystems.public'), 'brands')->exists($this->form->image))
                            <div class="relative">
                                <img src="{{ Storage::disk(config('filesystems.public'))->url($this->form->image) }}"
                                     alt="Brand logo" class="w-full rounded-lg"/>
                                <div class="absolute top-0 right-0 p-2">
                                    <flux:button wire:click="unsetImage" size="sm" icon="x-mark"/>
                                </div>
                            </div>
                        @elseif ($this->form->image && $this->form->image instanceof \Illuminate\Http\UploadedFile && $this->form->image->temporaryUrl())
                            <div class="relative">
                                <img src="{{ $this->form->image->temporaryUrl() }}" alt="Brand logo"
                                     class="w-full rounded-lg"/>
                                <div class="absolute top-0 right-0 p-2">
                                    <flux:button wire:click="unsetImage" size="sm" icon="x-mark"/>
                                </div>
                            </div>
                        @elseif ($this->form->image)
                            <div class="flex justify-center items-center">
                                <flux:badge icon="exclamation-triangle" color="yellow">Possible broken link.
                                </flux:badge>
                            </div>
                        @else
                            <div class="flex justify-center items-center">
                                <flux:badge icon="information-circle" color="grey">No image uploaded.</flux:badge>
                            </div>
                        @endif
                    @endcan
                </div>
            </div>
        </div>

        @canany(['view_brands_price_range', 'view_brands_rating', 'view_brands_partnership_level', 'view_brands_lead_time'])

            <flux:separator variant="subtle"/>

            <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
                <div class="col-span-1">
                    <flux:heading size="lg" level="1">Sales info</flux:heading>
                    {{-- <flux:subheading size="md" class="mb-6">Here's the brand info and details</flux:subheading> --}}
                </div>
                <div class="col-span-2">
                    <div class="grid grid-cols-1 gap-4">
                        @can('view_brands_price_range')
                            <flux:radio.group wire:model="form.price_range" label="Price Range" variant="segmented"
                                              badge="Optional" description="This will be publicly displayed.">
                                @foreach ($brandPriceRanges as $brandPriceRange)
                                    <flux:radio value="{{ $brandPriceRange->value }}"
                                                label="{{ $brandPriceRange->label() }}"/>
                                @endforeach
                            </flux:radio.group>
                        @endcan
                        @can('view_brands_rating')
                            <flux:radio.group wire:model="form.rating" label="Rating" variant="segmented"
                                              badge="Optional"
                                              description="This will be publicly displayed.">
                                @foreach ($brandRatings as $brandRating)
                                    <flux:radio value="{{ $brandRating->value }}" label="{{ $brandRating->label() }}"
                                                description="This will be publicly displayed."/>
                                @endforeach
                            </flux:radio.group>
                        @endcan
                        @can('view_brands_partnership_level')
                            <flux:radio.group wire:model="form.partnership_level" label="Partnership Level"
                                              variant="segmented"
                                              badge="Optional" description="This will be publicly displayed.">
                                @foreach ($brandPartnershipLevels as $partnershipLevel)
                                    <flux:radio value="{{ $partnershipLevel->value }}"
                                                label="{{ $partnershipLevel->label() }}"/>
                                @endforeach
                            </flux:radio.group>
                        @endcan
                        @can('view_brands_lead_time')
                            <div class="hidden md:block">
                                <flux:radio.group wire:model="form.lead_time" variant="segmented" label="Lead Time"
                                                  badge="Optional" description="This will be publicly displayed.">
                                    @foreach ($brandLeadTimes as $brandLeadTime)
                                        <flux:radio value="{{ $brandLeadTime->value }}"
                                                    label="{{ $brandLeadTime->label() }}"/>
                                    @endforeach
                                </flux:radio.group>
                            </div>
                        @endcan
                        @can('view_brands_lead_time')
                            <div class="md:hidden">
                                <flux:select searchable wire:model="form.lead_time" variant="listbox" clearable
                                             placeholder="Choose lead time..." label="Lead Time"
                                             description="This will be publicly displayed." badge="Optional">
                                    @foreach ($brandLeadTimes as $brandLeadTime)
                                        <flux:select.option
                                            value="{{ $brandLeadTime->value }}">{{ $brandLeadTime->label() }}</flux:select.option>
                                    @endforeach
                                </flux:select>
                            </div>
                        @endcan
                    </div>
                </div>
            </div>
        @endcanany

        @canany(['view_brands_purchase_price_list', 'view_brands_purchase_conditions', 'view_brands_minimum_orderable', 'view_brands_extra_costs', 'view_brands_delivery_terms', 'view_brands_yearly_bonus_info'])
            <flux:separator variant="subtle"/>

            <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
                <div class="col-span-1">
                    <flux:heading size="lg" level="1">Purchase info</flux:heading>
                    {{-- <flux:subheading size="md" class="mb-6">Here's the brand info and details</flux:subheading> --}}
                </div>
                <div class="col-span-2">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        @can('view_brands_purchase_price_list')
                            <flux:select searchable wire:model="form.purchase_price_list" variant="listbox"
                                         placeholder="Choose price list..." label="Purchase Price List" badge="Optional"
                                         description="This will be publicly displayed.">
                                <flux:select.option>NOIVA</flux:select.option>
                                <flux:select.option>NETPR</flux:select.option>
                                <flux:select.option>IVATO</flux:select.option>
                                <flux:select.option>LISTP</flux:select.option>
                            </flux:select>
                        @endcan
                        @can('view_brands_purchase_conditions')
                            <flux:input wire:model="form.purchase_conditions" variant="filled"
                                        label="Purchase Conditions"
                                        badge="Optional" description="This will be publicly displayed."/>
                        @endcan
                        @can('view_brands_minimum_orderable')
                            <flux:input wire:model="form.minimum_orderable" type="number" step="0.01"
                                        icon="currency-euro"
                                        variant="filled" placeholder="Minimum Orderable" label="Minimum Orderable"
                                        badge="Optional" description="This will be publicly displayed."/>
                        @endcan
                        @can('view_brands_extra_costs')
                            <flux:input wire:model="form.extra_costs" variant="filled" label="Extra Costs"
                                        badge="Optional"
                                        description="This will be publicly displayed."/>
                        @endcan
                        @can('view_brands_delivery_terms')
                            <flux:select searchable wire:model="form.delivery_terms" variant="listbox" clearable
                                         placeholder="Choose brand delivery terms..." label="Delivery Terms"
                                         description="This will be publicly displayed." badge="Optional">
                                @foreach($deliveryTerms as $deliveryTerm)
                                    <flux:select.option
                                        value="{{ $deliveryTerm->value }}">{{ $deliveryTerm->label() }}</flux:select.option>
                                @endforeach
                            </flux:select>
                        @endcan
                        @can('view_brands_yearly_bonus_info')
                            <flux:input wire:model="form.yearly_bonus_info" variant="filled" label="Yearly Bonus Info"
                                        badge="Optional" description="This will be publicly displayed."/>
                        @endcan
                    </div>
                </div>
            </div>
        @endcanany

        @canany(['view_brands_catalogs', 'view_brands_pricelist', 'view_brands_valid_from', 'view_brands_expected_pricelist_update', 'view_brands_social_link', 'view_brands_supplier_media_link', 'view_brands_supplier_media_link_user', 'view_brands_supplier_media_link_password'])

            <flux:separator variant="subtle"/>

            <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
                <div class="col-span-1">
                    <flux:heading size="lg" level="1">Assets info</flux:heading>
                    {{-- <flux:subheading size="md" class="mb-6">Here's the brand info and details</flux:subheading> --}}
                </div>
                <div class="col-span-2">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        @can('view_brands_catalogs')
                            <flux:input wire:model="form.catalogs" icon="link" variant="filled" label="Catalogs"
                                        badge="Optional" description="This will be publicly displayed."/>
                        @endcan
                        @can('view_brands_pricelist')
                            <flux:input wire:model="form.pricelist" icon="link" variant="filled" label="Pricelist"
                                        badge="Optional" description="This will be publicly displayed."/>
                        @endcan
                        @can('view_brands_valid_from')
                            <flux:input wire:model="form.valid_from" type="date" max="2999-12-31" label="Valid From"
                                        badge="Optional" description="This will be publicly displayed."/>
                        @endcan
                        @can('view_brands_expected_pricelist_update')
                            <flux:input wire:model="form.expected_pricelist_update" type="date" max="2999-12-31"
                                        label="Expected Pricelist Update" badge="Optional"
                                        description="This will be publicly displayed."/>
                        @endcan
                        @can('view_brands_social_link')
                            <flux:input wire:model="form.social_link" icon="link" variant="filled" label="Social Link"
                                        badge="Optional" description="This will be publicly displayed."/>
                        @endcan
                        @can('view_brands_supplier_media_link')
                            <flux:input wire:model="form.supplier_media_link" icon="link" variant="filled"
                                        label="Supplier Media Link" badge="Optional"
                                        description="This will be publicly displayed."/>
                        @endcan
                        @can('view_brands_supplier_media_link_user')
                            <flux:input wire:model="form.supplier_media_link_user" variant="filled"
                                        label="Supplier Media Link User" badge="Optional"
                                        description="This will be publicly displayed."/>
                        @endcan
                        @can('view_brands_supplier_media_link_password')
                            <flux:input wire:model="form.supplier_media_link_password" variant="filled"
                                        label="Supplier Media Link Password" badge="Optional"
                                        description="This will be publicly displayed."/>
                        @endcan
                    </div>
                </div>
            </div>
        @endcanany

        @can('view_brands_notes')
            <flux:separator variant="subtle"/>

            <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
                <div class="col-span-1">
                    <flux:heading size="lg" level="1">Extra info</flux:heading>
                    {{-- <flux:subheading size="md" class="mb-6">Here's the brand info and details</flux:subheading> --}}
                </div>
                <div class="col-span-2">
                    <div class="grid grid-cols-1 gap-4">
                        <flux:textarea wire:model="form.notes" variant="filled" label="Notes" badge="Optional"
                                       description="This will be publicly displayed."/>
                    </div>
                </div>
            </div>
        @endcan

        <div class="flex justify-end gap-4">
            <flux:button @click="$dispatch('redirect-to-brands')" size="sm">Cancel</flux:button>
            <flux:button size="sm" type="submit" variant="primary">Save</flux:button>
        </div>

    </form>

</flux:main>
