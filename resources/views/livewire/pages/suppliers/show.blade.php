<flux:main container>
    <div class="flex justify-between items-start">
        <livewire:components.breadcrumbs/>
        <livewire:components.modals.create-ticket :resourceValue="$supplier" size="xs"/>
    </div>
    <flux:separator variant="subtle" class="my-4"/>

    <flux:heading size="xl" level="1">Supplier: {{ $supplier->name }}</flux:heading>
    <flux:subheading size="lg" class="mb-6">Here's the supplier info and details</flux:subheading>

    <div class="flex flex-col gap-12 my-12">

        {{-- Actions --}}
        <div class="flex flex-col">
            <div class="flex justify-between gap-2">
                <div class="flex items-center">
                </div>
                <div>
                    @canany(['update', 'delete'], $supplier)
                        <flux:dropdown>
                            <flux:button size="sm" icon-trailing="chevron-down">Actions</flux:button>
                            <flux:menu>
                                @can('update', $supplier)
                                    <flux:menu.item wire:click="edit({{ $supplier->id }})" icon="pencil-square">Edit
                                    </flux:menu.item>
                                @endcan
                                @can('delete', $supplier)
                                    <flux:menu.separator/>
                                    <flux:menu.item
                                        wire:confirm="Are you sure you want to delete this supplier? This action cannot be undone."
                                        wire:click="delete({{ $supplier->id }})" variant="danger" icon="trash">Delete
                                    </flux:menu.item>
                                @endcan
                            </flux:menu>
                        </flux:dropdown>
                    @endcanany
                </div>
            </div>
        </div>

        {{-- Supplier Details --}}
        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Supplier details</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the supplier info and details</flux:subheading>
            </div>

            <div class="col-span-2">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    @can('view_suppliers_type')
                        <flux:select searchable wire:model="form.type" disabled variant="listbox"
                                     placeholder="Choose type..." label="Type"
                                     description="This will be publicly displayed.">
                            @foreach($types as $type)
                                <flux:select.option value="{{ $type->value }}">{{ $type->label() }}</flux:select.option>
                            @endforeach
                        </flux:select>
                    @endcan
                    <div></div>
                    @can('view_suppliers_name')
                        <flux:input wire:model="form.name" readonly variant="filled" label="Name"
                                    description="This will be publicly displayed."/>
                    @endcan
                    @can('view_suppliers_code')
                        <flux:input wire:model="form.code" readonly variant="filled" label="Code"
                                    description="This will be publicly displayed." badge="Optional"/>
                    @endcan
                    @can('view_suppliers_payment_conditions')
                        <flux:input wire:model="form.payment_conditions" readonly variant="filled"
                                    label="Payment Conditions" badge="Optional"
                                    description="This will be publicly displayed."/>
                    @endcan
                    @can('view_suppliers_price_range')
                        <flux:radio.group wire:model="form.price_range" disabled label="Price Range" variant="segmented"
                                          badge="Optional" description="This will be publicly displayed.">
                            @foreach ($priceRanges as $priceRange)
                                <flux:radio value="{{ $priceRange->value }}" label="{{ $priceRange->label() }}"/>
                            @endforeach
                        </flux:radio.group>
                    @endcan
                </div>

                @canany(['view_suppliers_materials', 'view_suppliers_product_types', 'view_suppliers_destination_rooms'])

                    <flux:separator variant="subtle" text="select also" class="my-12"/>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        @can('view_suppliers_materials')
                            <flux:field>
                                <flux:label badge="Optional">Materials</flux:label>
                                <flux:description>This will be publicly displayed.</flux:description>

                                <div class="flex flex-wrap gap-2 mt-2">
                                    @foreach ($form->materials as $material)
                                        <flux:badge size="sm">
                                            {{ $material }}
                                        </flux:badge>
                                    @endforeach
                                </div>
                            </flux:field>
                        @endcan
                        @can('view_suppliers_product_types')
                            <flux:field>
                                <flux:label badge="Optional">Product Types</flux:label>
                                <flux:description>This will be publicly displayed.</flux:description>

                                <div class="flex flex-wrap gap-2 mt-2">
                                    @foreach ($form->productTypes as $productType)
                                        <flux:badge size="sm">
                                            {{ $productType }}
                                        </flux:badge>
                                    @endforeach
                                </div>
                            </flux:field>
                        @endcan
                        @can('view_suppliers_destination_rooms')
                            <flux:field>
                                <flux:label badge="Optional">Destination Rooms</flux:label>
                                <flux:description>This will be publicly displayed.</flux:description>

                                <div class="flex flex-wrap gap-2 mt-2">
                                    @foreach ($form->destinationRooms as $destinationRoom)
                                        <flux:badge size="sm">
                                            {{ $destinationRoom }}
                                        </flux:badge>
                                    @endforeach
                                </div>
                            </flux:field>
                        @endcan
                    </div>
                @endcanany
            </div>
        </div>

        @can('view_suppliers_notes')
            <flux:separator variant="subtle"/>

            <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
                <div class="col-span-1">
                    <flux:heading size="lg" level="1">Extra info</flux:heading>
                    {{-- <flux:subheading size="md" class="mb-6">Here's the brand info and details</flux:subheading> --}}
                </div>
                <div class="col-span-2">
                    <div class="grid grid-cols-1 gap-4">
                        <flux:textarea wire:model="form.notes" readonly variant="filled" label="Notes" badge="Optional"
                                       description="This will be publicly displayed."/>
                    </div>
                </div>
            </div>
        @endcan

    </div>

    <flux:separator variant="subtle" class="my-12"/>

    {{-- Addresses Listing --}}
    <livewire:components.inner-table.addresses :resourceType="class_basename($supplier)"
                                               :resourceValue="$supplier"/>

    @can('read_contacts')
        <flux:separator variant="subtle" class="my-12"/>

        {{-- Contacts Listing --}}
        <livewire:components.inner-table.contacts :resourceType="class_basename($supplier)"
                                                  :resourceValue="$supplier"/>
    @endcan

    @can('read_brands')
        <flux:separator variant="subtle" class="my-12"/>

        {{-- Brands Listing --}}
        <livewire:components.inner-table.brands :resourceType="class_basename($supplier)"
                                                :resourceValue="$supplier"/>
    @endcan
</flux:main>
