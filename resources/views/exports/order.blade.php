<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <style>
        @page {
            size: A4;
            margin-top: 5cm;
            margin-bottom: 5cm;
            margin-left: 1cm;
            margin-right: 1cm;
        }

        body {
            font-family: 'Open Sans', sans-serif;
        }

        table {
            width: 100%;
            table-layout: fixed;
            line-height: 1.0;
        }

        p {
            line-height: 1.0;
        }

        .header {
            position: fixed;
            top: -4.5cm;
            left: 0;
            right: 0;
        }

        .footer {
            position: fixed;
            bottom: -4.5cm;
            left: 0;
            right: 0;
        }

        .page-number:before {
            content: "Page " counter(page);
        }
    </style>
</head>

<body>

<!-- Header -->
<div class="header">

    <!-- Heading Row -->
    <table style="width:100%; height: 4cm;">
        <tr>
            <td style="width: 40%; vertical-align: top;">

                <!-- Logo Column -->
                <img style="width: 4.5cm;"
                     src="{{ $companyInfo->logo_url }}">

                <!-- Under Logo Text -->
                <p style="padding-top: 0.2cm; font-size: 4pt;">
                    {{ $companyInfo->name }} <br>
                    {{ $companyInfo->address }} <br>
                    {{ $companyInfo->phone }} {{ $companyInfo->fax }} <br>
                    {{ $companyInfo->vat_number }} {{ $companyInfo->fiscal_code }} <br>
                    {{ $companyInfo->rea }} <br>
                    {{ $companyInfo->email }} | {{ $companyInfo->website }} <br>
                </p>
            </td>
            <td style="width: 60%; vertical-align: top;">

                <!-- Order Data Column -->
                <table style="width:100%; border-collapse: collapse;">
                    <tr style="border-top: lightgray solid 0.5pt;">
                        <td style="width: 25%; vertical-align: top; text-align: left; font-size: 7pt;">
                            <strong>DATE</strong><br>
                            {{ $order->date->format("d-m-Y") ?? ''}}
                        </td>
                        <td style="width: 50%; vertical-align: top; text-align: left; font-size: 7pt;">
                            <strong>CODE</strong><br>
                            {{ $order->code ?? ''}}
                        </td>
                        <td style="width: 50%; vertical-align: top; text-align: left; font-size: 7pt;">
                            <strong>ORDER CODE</strong><br>
                            {{ $order->order_code ?? ''}}
                        </td>
                        <td style="width: 25%; vertical-align: top; text-align: right; font-size: 7pt;">
                            <strong>PAG.</strong><br>
                            <span class="page-number"></span>
                        </td>
                    </tr>
                </table>

                <!-- Order Shipped To Column -->
                @if ($order->client && $order->shippingAddress)
                    <table style="width:100%; border-collapse: collapse;">
                        <tr style="border-top: lightgray solid 0.5pt;">
                            <td style="width: 100%; font-size: 7pt;">
                                <strong>SHIPPED TO:</strong>
                                <br>
                                <strong>{{ $order->shippingAddress->company ?? '' }}</strong>
                                <br>
                                {{ $order->shippingAddress->street ?? '' }}
                                |
                                {{ $order->shippingAddress->city ?? '' }} - {{ $order->shippingAddress->zip ?? '' }}
                                |
                                {{ $order->shippingAddress->state ?? '' }}, {{ $order->shippingAddress->country ?? '' }}
                            </td>
                        </tr>
                    </table>
                @endif

                <!-- Order Invoiced To Column -->
                @if ($order->client && $order->invoicingAddress)
                    <table style="width:100%; border-collapse: collapse;">
                        <tr style="border-top: lightgray solid 0.5pt; border-bottom: lightgray solid 0.5pt;">
                            <td style="width: 100%; font-size: 7pt;">
                                <strong>INVOICED TO:</strong><br>
                                <strong>{{ $order->invoicingAddress->company ?? '' }}</strong>
                                <br>
                                {{ $order->invoicingAddress->street ?? '' }}
                                |
                                {{ $order->invoicingAddress->city ?? '' }} - {{ $order->invoicingAddress->zip ?? '' }}
                                |
                                {{ $order->invoicingAddress->state ?? '' }}
                                , {{ $order->invoicingAddress->country ?? '' }}
                                <br>
                                VAT ID: {{ $order->invoicingAddress->vat_number ?? '' }} | FISCAL
                                CODE: {{ $order->invoicingAddress->fiscal_code ?? '' }} |
                                SDI: {{ $order->invoicingAddress->sdi_code ?? '' }}
                            </td>
                        </tr>
                    </table>
                @endif
            </td>
        </tr>
    </table>
</div>

<!-- Footer -->
<div class="footer">

    <!-- Footer Data Row -->
    <table style="width:100%; height: 2cm; font-size:5pt; border-top: black solid 0.5pt;">
        <tr>
            <td style="width: 10%; text-align: center; vertical-align: top; padding: 2pt;"><strong>TERMS</strong></td>
            <td style="width: 30%; text-align: left; vertical-align: top; padding: 2pt; border-left: black solid 0.5pt;">
                Bank Transfer Accepted. <br>
                Credit Card Transfer Accepted with an extra 3% fee. <br>
                Please check that any transfer charges are paid on your end. <br>
                <br>
                Order Processed upon initial deposit. <br>
                <strong>Shipping Cost to be Invoiced prior to Delivery</strong> <br>
                Discount vary according to Net Total. <br>
                Custom-made items are not returnable. <br>
            </td>
            <td style="width: 5%; text-align: left; vertical-align: top; padding: 2pt;"></td>
            <td style="width: 25%; text-align: left; vertical-align: top; padding: 2pt; border-left: black solid 0.5pt;">
                Bank Transfer Details <br>
                Account Name: {{ $companyInfo->bank_account_name }} <br>
                IBAN: {{ $companyInfo->iban }} <br>
                BIC: {{ $companyInfo->bic }} <br>
                <strong>Important: On transfer please note invoice number</strong>
            </td>
            <td style="width: 20%; text-align: right; vertical-align: top; padding: 2pt;">
                NET TOTAL <br>
                ADDONS CHARGES <br>
                VAT
                <br><br>
                <strong style="text-decoration: underline;">GROSS TOTAL</strong>
            </td>
            <td style="width: 10%; text-align: right; vertical-align: top; padding: 2pt;">
                {{ $totalAmount }}
                <br>
                {{ $addonsAmount }}
                <br>
                {{ $vatTotal }}
                <br><br>
                <strong style="text-decoration: underline;">
                    {{ $grossTotal }}
                </strong>
            </td>
        </tr>
    </table>

    <!-- Footer Disclaimer and Deposit Row -->
    <table style="width:100%; font-size: 5pt; margin-bottom: 4pt;">
        <tr>
            <td style="width: 10%; text-align: left; padding: 2pt;"></td>
            <td style="width: 45%; text-align: left; padding: 2pt;">
                <strong style="border: black solid 2pt; padding: 2pt;">PLEASE NOTE: This quotation is valid for 30
                    days.</strong>
            </td>

            @if ($order->paymentTerm)
                @foreach ($payments as $payment)
                    <td style="width: 35%; text-align: right; padding: 2pt;">
                        <strong>
                            {{ $payment['description'] }}
                        </strong>
                    </td>
                    <td style="width: 10%; text-align: right; padding: 2pt;">
                        <strong>
                            {{ $payment['formatted_value'] }}
                        </strong>
                    </td>
                @endforeach
            @endif
        </tr>
    </table>

    <!-- Footer Approval Row -->
    <table style="width:100%; font-size: 5pt; border-top: black solid 0.5pt; border-bottom: black solid 0.5pt;">
        <tr>
            <td style="width: 10%; text-align: right; padding: 2pt; border-right: black solid 0.5pt;">
                <strong>APPROVAL</strong></td>
            <td style="width: 25%; text-align: right; padding: 2pt; border-right: black solid 0.5pt;">Print Name</td>
            <td style="width: 25%; text-align: right; padding: 2pt; border-right: black solid 0.5pt;">Signature</td>
            <td style="width: 20%; text-align: right; padding: 2pt; border-right: black solid 0.5pt;">Stamp</td>
            <td style="width: 20%;"></td>
        </tr>
    </table>
</div>

<!-- Product Listing Row -->
<table style="width:100%; font-size: 5pt; border-collapse: collapse;">

    {{-- Table headers --}}
    <tr style="border-bottom: black solid 0.5pt;">
        <th style="width:10%;text-align: left;">
            IMAGE
        </th>
        <th style="width:30%; text-align: left;">
            DESCRIPTION
        </th>
        <th style="width:10%; text-align: center;">
            QUANTITY
        </th>
        <th style="width:10%; text-align: center;">
            PRICE
        </th>
        <th style="width:10%; text-align: center;">
            DISCOUNT
        </th>
        <th style="width:10%; text-align: center;">
            REQUIRED DELIVERY
        </th>
        <th style="width:10%; text-align: center;">
            NET PRICE
        </th>
        <th style="width:10%; text-align: center;">
            TOTAL PRICE
        </th>
    </tr>

    {{-- Table datas --}}
    @foreach ($sortedCartGroups as $group)
        <tr style="background-color: #f2f2f2;">
            <td colspan="9" style="text-align: left;">
                <strong>{{ $group->name ?? 'Default' }}</strong>
            </td>
        </tr>

        @foreach ($group->orderRows as $row)
            <tr>
                <td style="padding: 2pt;">
                    @if ($row->product)
                        @if ($row->product->image)
                            <img src="{{ Storage::disk(config('filesystems.public'))->url($row->product->image) }}"
                                 style="height: 45px"/>
                        @else
                            <img src="https://bergomi.fra1.cdn.digitaloceanspaces.com/Image%20not%20Available.jpg"
                                 style="height: 45px">
                        @endif
                    @elseif ($row->customProduct)
                        @if ($row->customProduct->image)
                            <img
                                src="{{ Storage::disk(config('filesystems.public'))->url($row->customProduct->image) }}"
                                style="height: 45px"/>
                        @else
                            <img src="https://bergomi.fra1.cdn.digitaloceanspaces.com/Image%20not%20Available.jpg"
                                 style="height: 45px">
                        @endif
                    @endif
                </td>
                <td style="padding: 2pt;">
                    @if ($row->product)
                        <strong>BRAND:</strong> {{ $row->product->brand->name ?? '' }} <br>
                        <strong>COLLECTION:</strong> {{ $row->product->collection ? explode('-', $row->product->collection->name)[0] : '' }}
                        <br>
                        <strong>DESC:</strong> {{ $row->description ?? $row->product->description ?? '-' }} <br>
                        @if ($row->product->dimensions)
                            <strong>DIMENSIONS:</strong> {{ $row->product->dimensions ?? '' }} <br>
                        @endif
                        @if ($row->product->supplier_color && $row->options == null)
                            <strong>COLOR:</strong> {{ $row->product->supplier_color ?? '' }} <br>
                        @endif
                        <strong>SKU:</strong> {{ $row->sku ?? ($row->product->hasModules() ? $row->getModularSku() : $row->product->sku) ?? '-' }}
                        <br>
                        @if ($row->options)
                            <div>
                                <div style="text-decoration: underline;">Options:</div>
                                @foreach ($row->getSelectedOptions() as $option)
                                    <div>
                                        {{ $option['description'] }}
                                    </div>
                                @endforeach
                            </div>
                        @endif
                    @elseif ($row->customProduct)
                        <strong>BRAND:</strong> {{ $row->customProduct->brand_name ?? '' }} <br>
                        <strong>DESC:</strong> {{ $row->description ?? $row->customProduct->description ?? '-' }} <br>
                        <strong>SKU:</strong> {{ $row->sku ?? $row->customProduct->sku ?? '-' }} <br>
                        @if ($row->customProduct->dimensions)
                            <strong>DIMENSIONS:</strong> {{ $row->customProduct->dimensions ?? '' }} <br>
                        @endif
                        @if ($row->customProduct->supplier_color)
                            <strong>COLOR:</strong> {{ $row->customProduct->supplier_color ?? '' }} <br>
                        @endif
                    @endif
                </td>
                <td style="padding: 2pt; text-align: center;">
                    {{ $row->quantity }}
                </td>
                <td style="padding: 2pt; text-align: center;">
                    @if ($row->selling_price_override)
                        {{ eu_currency($row->sellingPriceOverride) }}
                    @else
                        {{ $row->sellingPrice ? eu_currency($row->sellingPrice) : ($row->product?->hasModules() ? eu_currency($row->product?->getModularPrice($row->options)) : eu_currency($row->product->sellingPrice ?? 0)) }}
                    @endif
                </td>
                <td style="padding: 2pt; text-align: center;">
                    {{-- Discount --}}
                    @if (isset($row->discount_override))
                        - {{ $row->discount_override }} %
                    @else
                        - {{ $row->discount ?? $row->getDiscountForClient() }} %
                    @endif

                    {{-- Variation --}}
                    @if (isset($row->variation))
                        ({{ $row->variation > 0 ? '+' : '' }}{{ $row->variation }} %)
                    @endif
                </td>
                <td style="padding: 2pt; text-align: center;">
                    {{ $row->required_delivery_date?->format('d-m-y') ?? '-' }}
                </td>
                <td style="padding: 2pt; text-align: center;">
                    {{ eu_currency($row->rowUnitPrice) }}
                </td>
                <td style="padding: 2pt; text-align: center;">
                    {{ eu_currency($row->rowFinalPrice) }}
                </td>
            </tr>

            <tr style="width: 100vw; border-bottom: lightgray solid 0.5pt;">
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
        @endforeach
    @endforeach

    {{-- Addons --}}
    <tr style="border-bottom: black solid 0.5pt;">
        <th style="width:10%;text-align: left;">
            CODE
        </th>
        <th style="width:30%; text-align: left;">
            DESCRIPTION
        </th>
        <th style="width:10%; text-align: center;">
            CHARGE in %
        </th>
        <th style="width:10%; text-align: center;">
        </th>
        <th style="width:10%; text-align: center;">
        </th>
        <th style="width:10%; text-align: center;">
        </th>
        <th style="width:10%; text-align: center;">
        </th>
        <th style="width:10%; text-align: center;">
            ADDON PRICE
        </th>
    </tr>

    <tr style="background-color: #f2f2f2;">
        <td colspan="9" style="text-align: left;">
            <strong>Addons</strong>
        </td>
    </tr>
    @foreach ($order->addons as $addon)
        <tr style="">
            <td style="padding: 2pt;">
                {{ $addon->code }}
            </td>
            <td style="padding: 2pt;">
                {{ $addon->description }}
            </td>
            <td style="padding: 2pt; text-align: center;">
                {{ $addon->percent }} %
            </td>
            <td style="padding: 2pt; text-align: center;">
            </td>
            <td style="padding: 2pt; text-align: center;">
            </td>
            <td style="padding: 2pt; text-align: center;">
            </td>
            <td style="padding: 2pt; text-align: center;">
            </td>
            <td style="padding: 2pt; text-align: center;">
                {{ $addon->formatted_price }}
            </td>
        </tr>
    @endforeach

</table>

</body>
</html>
