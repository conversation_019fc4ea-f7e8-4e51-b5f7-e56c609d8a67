@props([
    'unsetAction' => null,
    'icon' => 'document-text',
])

<flux:card size="sm" class="flex justify-center items-center">
    <div class="relative flex justify-center items-center w-18 h-28">
        <div class="absolute top-0 right-0">
            <flux:button wire:click="{{ $unsetAction }}" size="sm" icon="x-mark" variant="ghost" />
        </div>
        <div class="flex flex-col justify-center items-center gap-2">
            <flux:icon name="{{ $icon }}" class="w-16 h-16" />
        </div>
    </div>
</flux:card>