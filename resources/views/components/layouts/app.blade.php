<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">

    @vite('resources/css/app.css')
    @fluxAppearance

    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600&display=swap" rel="stylesheet" />

    <!-- Alpine Plugins for Sortable -->
    <script defer src="https://cdn.jsdelivr.net/npm/@alpinejs/sort@3.x.x/dist/cdn.min.js"></script>
    <!-- Lightbox Plugin -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpine-tailwind-lightbox@1.x.x/dist/alpine-tailwind-lightbox.min.js"></script>

    <title>{{ $title ?? config('app.name') }}</title>
</head>

<body class="min-h-screen bg-white dark:bg-zinc-800 pb-12">

    <livewire:components.main-menu >

    {{ $slot }}

    {{-- Include Toast Notification --}}
    @persist('toast')
        <flux:toast />
    @endpersist

    @vite('resources/js/app.js')
    @fluxScripts
</body>
</html>
