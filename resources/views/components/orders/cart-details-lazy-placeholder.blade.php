{{-- Cart and Summary Placeholder --}}
<flux:card size="sm" class="flex flex-col justify-between animate-pulse">
    <div>
        <flux:subheading class="mb-1">Cart details</flux:subheading>
        <flux:separator class="mb-2"></flux:separator>
    </div>
    <div>
        <div class="flex justify-between mt-1">
            <span class="text-sm font-light">Amount</span>
            <div class="w-20 h-6 bg-gray-100 dark:bg-zinc-700 rounded-lg"></div>
        </div>
        <div class="flex justify-between mt-1">
            <span class="text-sm font-light">With addons</span>
            <div class="w-24 h-6 bg-gray-100 dark:bg-zinc-700 rounded-lg"></div>
        </div>
    </div>
    <div>
        <div class="flex justify-end mb-1">
            <div class="w-32 h-6 bg-gray-100 dark:bg-zinc-700 rounded-lg"></div>
        </div>
    </div>
</flux:card>