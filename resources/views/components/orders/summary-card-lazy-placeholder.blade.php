{{-- Car<PERSON> and Summary --}}
<div class="flex flex-col gap-2">
    <flux:card size="sm" class="flex flex-col justify-between">
        <div>
            <flux:subheading class="mb-1">Cart details</flux:subheading>
            <flux:separator class="mb-2"></flux:separator>
        </div>
        <div>
            <flux:heading size="lg" class="mt-1 flex justify-between"><span class="text-sm font-light">Amount</span> -- </flux:heading>
            <flux:heading size="lg" class="mt-1 flex justify-between"><span class="text-sm font-light">With addons</span> -- </flux:heading>
        </div>
    </flux:card>

    <div class="flex gap-2">
        <flux:button icon="shopping-bag" variant="primary" size="sm" class="w-full" disabled>Show cart</flux:button>
        <flux:dropdown>
            <flux:button icon-trailing="chevron-down" size="sm" class="w-full" disabled>Actions</flux:button>
            <flux:menu>
            </flux:menu>
        </flux:dropdown>
    </div>
</div>