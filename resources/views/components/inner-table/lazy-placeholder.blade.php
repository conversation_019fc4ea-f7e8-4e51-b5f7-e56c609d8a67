<div class="animate-pulse">
    <div class="flex flex-col">
        <div class="flex justify-between">
            <div class="flex flex-col">
                <flux:heading size="lg" level="1"><div class="w-6 bg-gray-100 dark:bg-zinc-700 rounded-lg">&nbsp;</div></flux:heading>
                <flux:subheading size="md" class="mb-6"><div class="w-12 bg-gray-100 dark:bg-zinc-700 rounded-lg">&nbsp;</div></flux:subheading>
            </div>
            <div class="flex items-center gap-2">
                <flux:modal.trigger name="attach-brand">
                    <div class="w-12 bg-gray-100 dark:bg-zinc-700 rounded-lg">&nbsp;</div>
                </flux:modal.trigger>
            </div>
        </div>

        <div>
            <flux:table>
                <flux:table.columns>
                    <flux:table.column><div class="w-full bg-gray-200 dark:bg-zinc-700 rounded-lg">&nbsp;</div></flux:table.column>
                    <flux:table.column><div class="w-full bg-gray-200 dark:bg-zinc-700 rounded-lg">&nbsp;</div></flux:table.column>
                    <flux:table.column><div class="w-full bg-gray-200 dark:bg-zinc-700 rounded-lg">&nbsp;</div></flux:table.column>
                    <flux:table.column><div class="w-full bg-gray-200 dark:bg-zinc-700 rounded-lg">&nbsp;</div></flux:table.column>
                    <flux:table.column><div class="w-full bg-gray-200 dark:bg-zinc-700 rounded-lg">&nbsp;</div></flux:table.column>
                    <flux:table.column><div class="w-full bg-gray-200 dark:bg-zinc-700 rounded-lg">&nbsp;</div></flux:table.column>
                </flux:table.columns>

                <flux:table.rows>
                    @foreach (range(0, 3) as $i)
                    <flux:table.row>
                        <flux:table.cell><div class="w-full bg-gray-200 dark:bg-zinc-700 rounded-lg">&nbsp;</div></flux:table.cell>
                        <flux:table.cell><div class="w-full bg-gray-200 dark:bg-zinc-700 rounded-lg">&nbsp;</div></flux:table.cell>
                        <flux:table.cell><div class="w-full bg-gray-200 dark:bg-zinc-700 rounded-lg">&nbsp;</div></flux:table.cell>
                        <flux:table.cell><div class="w-full bg-gray-200 dark:bg-zinc-700 rounded-lg">&nbsp;</div></flux:table.cell>
                        <flux:table.cell><div class="w-full bg-gray-200 dark:bg-zinc-700 rounded-lg">&nbsp;</div></flux:table.cell>
                        <flux:table.cell><div class="w-full bg-gray-200 dark:bg-zinc-700 rounded-lg">&nbsp;</div></flux:table.cell>
                        <flux:table.cell class="flex justify-end items-center gap-2">
                            <div class="w-full bg-gray-200 dark:bg-zinc-700 rounded-lg">&nbsp;</div>
                        </flux:table.cell>
                    </flux:table.row>
                    @endforeach
                </flux:table.rows>
            </flux:table>
        </div>
    </div>
</div>
