@props(['item'])

<flux:menu>
    <flux:menu.item wire:click="view({{ $item->id }})" icon="eye">View</flux:menu.item>
    <flux:menu.item wire:click="edit({{ $item->id }})" icon="pencil-square">Edit</flux:menu.item>

    <flux:menu.separator />

    <flux:menu.item wire:confirm="Are you sure you want to delete this resource? This action cannot be undone." wire:click="delete({{ $item->id }})" variant="danger" icon="trash">Delete</flux:menu.item>
</flux:menu>