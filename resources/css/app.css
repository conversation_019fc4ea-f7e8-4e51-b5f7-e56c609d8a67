@import 'tailwindcss';
@import '../../vendor/livewire/flux/dist/flux.css';

/* Required for dark mode in Flux... */
@custom-variant dark (&:where(.dark, .dark *));

@source '../views';

@source '../../vendor/livewire/flux-pro/stubs/**/*.blade.php';
@source '../../vendor/livewire/flux/stubs/**/*.blade.php';

@source '../node_modules/alpine-tailwind-lightbox/src/template.html';

@theme {
  --font-*: initial;
  --font-sans: Inter, sans-serif;
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}
