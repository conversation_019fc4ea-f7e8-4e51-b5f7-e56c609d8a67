<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('import_errors', function (Blueprint $table) {
            $table->id();

            // Relationship to import
            $table->foreignId('import_id')->constrained()->cascadeOnDelete();

            // Error details
            $table->string('job_class'); // Class name of the failed job
            $table->json('row_data'); // Original row data that caused the error
            $table->text('error_message'); // Exception message
            $table->longText('error_trace'); // Full stack trace

            $table->timestamps();

            // Indexes for performance
            $table->index('import_id');
            $table->index(['import_id', 'job_class']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('import_errors');
    }
};
