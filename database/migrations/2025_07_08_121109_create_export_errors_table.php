<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('export_errors', function (Blueprint $table) {
            $table->id();

            // Relationship to export
            $table->foreignId('export_id')->constrained()->cascadeOnDelete();

            // Error details
            $table->string('file_type'); // invoicing_address, shipping_address, order_rows
            $table->text('error_message'); // Exception message
            $table->longText('error_trace'); // Full stack trace

            $table->timestamps();

            // Indexes for performance
            $table->index('export_id');
            $table->index(['export_id', 'file_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('export_errors');
    }
};
