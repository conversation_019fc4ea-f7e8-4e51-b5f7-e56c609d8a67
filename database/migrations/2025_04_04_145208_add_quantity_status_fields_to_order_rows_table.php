<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_rows', function (Blueprint $table) {
            $table->integer('shipped_quantity')->default(0)->after('quantity');
            $table->integer('stocked_quantity')->default(0)->after('shipped_quantity');
            $table->integer('ordered_quantity')->default(0)->after('stocked_quantity');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_rows', function (Blueprint $table) {
            //
        });
    }
};
