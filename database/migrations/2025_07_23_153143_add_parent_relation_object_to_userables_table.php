<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('userables', function (Blueprint $table) {
            $table->json('relation_parents')->nullable()->after('userable_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('userables', function (Blueprint $table) {
            $table->dropColumn('relation_parents');
        });
    }
};
