<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('client_discount_group', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->constrained()->cascadeOnDelete();
            $table->foreignId('discount_group_id')->constrained()->cascadeOnDelete();
            $table->float('discount', 5, 2);
            $table->integer('extra_discount_threshold')->nullable()->comment('Extra discount threshold in cents');
            $table->float('extra_discount', 5, 2)->nullable()->comment('Extra discount in percent');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('client_discount_group');
    }
};
