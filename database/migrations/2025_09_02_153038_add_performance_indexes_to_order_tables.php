<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Add performance indexes to optimize order-related queries.
     */
    public function up(): void
    {
        Schema::table('order_rows', function (Blueprint $table) {
            // Composite index for cart_group_id and sort (most common query pattern)
            $table->index(['cart_group_id', 'sort'], 'idx_order_rows_cart_group_sort');
            
            // Index for product_id lookups
            $table->index('product_id', 'idx_order_rows_product_id');
            
            // Index for custom_product_id lookups
            $table->index('custom_product_id', 'idx_order_rows_custom_product_id');
            
            // Index for position_id searches
            $table->index('position_id', 'idx_order_rows_position_id');
        });

        Schema::table('cart_groups', function (Blueprint $table) {
            // Composite index for order_id and sort (for ordering cart groups)
            $table->index(['order_id', 'sort'], 'idx_cart_groups_order_sort');
        });

        Schema::table('orders', function (Blueprint $table) {
            // Index for status filtering
            $table->index('status', 'idx_orders_status');
            
            // Index for date filtering
            $table->index('date', 'idx_orders_date');
            
            // Composite index for client_id and status (common filtering pattern)
            $table->index(['client_id', 'status'], 'idx_orders_client_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_rows', function (Blueprint $table) {
            $table->dropIndex('idx_order_rows_cart_group_sort');
            $table->dropIndex('idx_order_rows_product_id');
            $table->dropIndex('idx_order_rows_custom_product_id');
            $table->dropIndex('idx_order_rows_position_id');
        });

        Schema::table('cart_groups', function (Blueprint $table) {
            $table->dropIndex('idx_cart_groups_order_sort');
        });

        Schema::table('orders', function (Blueprint $table) {
            $table->dropIndex('idx_orders_status');
            $table->dropIndex('idx_orders_date');
            $table->dropIndex('idx_orders_client_status');
        });
    }
};