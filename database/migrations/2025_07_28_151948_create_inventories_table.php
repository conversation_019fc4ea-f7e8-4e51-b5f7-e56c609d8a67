<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('inventories', function (Blueprint $table) {
            $table->id();

            $table->string('sku');
            $table->integer('quantity');
            $table->date('date')->nullable()->comment('If null = available stock, if set = incoming stock arrival date');

            $table->timestamps();

            // Indexes for performance
            $table->index('sku');
            $table->index(['sku', 'date']);
            $table->index('date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inventories');
    }
};
