<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_rows', function (Blueprint $table) {
            $table->id();

            $table->integer('sort')->default(0);

            $table->foreignId('cart_group_id')->constrained()->cascadeOnUpdate()->cascadeOnDelete();
            $table->foreignId('product_id')->nullable()->constrained()->cascadeOnUpdate()->nullOnDelete();

            $table->string('sku')->nullable();
            $table->string('description')->nullable();
            $table->json('options')->nullable()->comment('A json object that contains all the options selected for modular products');

            $table->string('position_id')->nullable();

            $table->integer('quantity')->nullable();
            $table->integer('selling_price')->nullable()->comment('Price in cents');
            $table->integer('selling_price_override')->nullable()->comment('Price in cents');
            $table->integer('purchasing_price')->nullable()->comment('Price in cents');
            $table->float('discount')->nullable();
            $table->float('discount_override')->nullable();
            $table->float('variation')->nullable();

            $table->date('expected_delivery_date')->nullable();
            $table->date('required_delivery_date')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_rows');
    }
};
