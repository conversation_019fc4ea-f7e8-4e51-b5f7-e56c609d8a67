<?php

use App\Enums\ClientTypes;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clients', function (Blueprint $table) {
            $table->id();
            $table->string('type')->default(ClientTypes::Lead);
            $table->foreignId('partner_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('internal_referent_id')->nullable()->constrained('users')->nullOnDelete();
            $table->string('company');
            $table->string('email');
            $table->string('country')->nullable();
            $table->foreignId('payment_term_id')->nullable()->constrained()->nullOnDelete();
            $table->string('commercial_category')->nullable();
            $table->integer('priority')->nullable()->comment('1-4');
            $table->integer('minimum_orderable')->nullable()->comment('Minimum orderable price in minor units.');
            $table->integer('handling_and_packing')->nullable()->comment('Handling and packing in percent.');
            $table->string('delivery_terms')->nullable();
            $table->json('countries_of_expertise')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clients');
    }
};
