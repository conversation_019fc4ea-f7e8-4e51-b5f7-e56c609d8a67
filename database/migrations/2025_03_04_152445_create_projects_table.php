<?php

use App\Enums\ProjectStatuses;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('projects', function (Blueprint $table) {
            $table->id();

            $table->date('date');
            $table->string('status')->default(ProjectStatuses::Draft->value);
            $table->string('name');
            $table->string('project_code')->nullable();
            $table->integer('project_code_progressive')->nullable();
            $table->date('project_code_date')->nullable();
            $table->string('project_code_region')->nullable();

            $table->foreignId('internal_referent_id')->nullable()->constrained('users')->cascadeOnUpdate()->nullOnDelete();
            $table->foreignId('client_id')->nullable()->constrained()->cascadeOnUpdate()->nullOnDelete();
            $table->foreignId('partner_id')->nullable()->constrained()->cascadeOnUpdate()->nullOnDelete();
            $table->foreignId('invoicing_address_id')->nullable()->constrained('addresses')->cascadeOnUpdate()->nullOnDelete();
            $table->foreignId('shipping_address_id')->nullable()->constrained('addresses')->cascadeOnUpdate()->nullOnDelete();
            $table->foreignId('payment_term_id')->nullable()->constrained()->cascadeOnUpdate()->nullOnDelete();

            $table->longText('description')->nullable();
            $table->string('type')->nullable();
            $table->string('type_name')->nullable();
            $table->string('type_link')->nullable();

            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('projects');
    }
};
