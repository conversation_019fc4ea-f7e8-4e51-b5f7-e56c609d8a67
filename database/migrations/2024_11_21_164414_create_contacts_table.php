<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contacts', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->json('departments')->nullable()->comment('Position of the contact');
            $table->string('position')->nullable()->comment('Position of the contact');
            $table->string('email')->unique();
            $table->string('phone')->nullable();
            $table->string('country')->nullable()->comment('Country of the contact');
            $table->json('languages')->nullable()->comment('Languages spoken by the contact');
            $table->text('notes')->nullable()->comment('Notes about the contact');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contacts');
    }
};
