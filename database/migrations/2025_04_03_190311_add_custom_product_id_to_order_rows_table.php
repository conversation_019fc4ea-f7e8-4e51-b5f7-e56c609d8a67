<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_rows', function (Blueprint $table) {
            $table->foreignId('custom_product_id')->nullable()->constrained()->cascadeOnUpdate()->nullOnDelete()->after('product_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_rows', function (Blueprint $table) {
            $table->dropColumn('custom_product_id');
        });
    }
};
