<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('module_option', function (Blueprint $table) {
            $table->index('module_id', 'module_option_module_id_index');            
            $table->index('option_id', 'module_option_option_id_index');
            $table->index(['module_id', 'option_id'], 'module_option_module_option_index');
            $table->index('deleted_at', 'module_option_deleted_at_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('module_option', function (Blueprint $table) {
            $table->dropIndex('module_option_module_id_index');
            $table->dropIndex('module_option_option_id_index');
            $table->dropIndex('module_option_module_option_index');
            $table->dropIndex('module_option_deleted_at_index');
        });
    }
};
