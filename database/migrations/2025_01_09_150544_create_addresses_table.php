<?php

use App\Enums\AddressTypes;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('addresses', function (Blueprint $table) {
            $table->id();
            $table->morphs('addressable');
            $table->string('name')->nullable();
            $table->string('type')->default(AddressTypes::Invoicing);
            $table->foreignId('parent_id')->nullable()->constrained('addresses')->nullOnDelete();
            $table->string('code_invoicing')->nullable();
            $table->string('code_shipping')->nullable();
            $table->string('company');
            $table->string('vat_number')->nullable();
            $table->string('fiscal_code')->nullable();
            $table->string('sdi_code')->nullable();
            $table->string('street')->nullable();
            $table->string('city')->nullable();
            $table->string('state')->nullable();
            $table->string('zip')->nullable();
            $table->string('country')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('addresses');
    }
};
