<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('exports', function (Blueprint $table) {
            $table->id();

            // Export metadata
            $table->string('type'); // order_files, invoicing_address, shipping_address, order_rows
            $table->string('source'); // order_submit, user, scheduler
            $table->foreignId('user_id')->nullable()->constrained()->nullOnDelete();

            // Related resource (generic - can be order, product, client, etc.)
            $table->unsignedBigInteger('resource_id')->nullable();
            $table->string('resource_type')->nullable(); // App\Models\Order\Order, App\Models\Product, etc.

            // File information
            $table->json('file_paths'); // Array of generated file paths
            $table->string('file_disk')->default('exchange');

            // Status and progress tracking
            $table->string('status')->default('pending'); // pending, processing, completed, failed
            $table->unsignedInteger('total_files')->default(0);
            $table->unsignedInteger('processed_files')->default(0);
            $table->unsignedInteger('failed_files')->default(0);

            // Timestamps
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index(['type', 'status']);
            $table->index(['source', 'created_at']);
            $table->index(['resource_id', 'resource_type']);
            $table->index(['resource_type', 'status']);
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('exports');
    }
};
