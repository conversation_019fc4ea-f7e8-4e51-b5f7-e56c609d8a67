<?php

use App\Enums\OrderStatuses;
use App\Enums\VatTypes;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();

            $table->foreignId('project_id')->nullable()->constrained()->cascadeOnUpdate()->nullOnDelete();

            $table->date('date');
            $table->string('status')->default(OrderStatuses::Open->value);
            $table->integer('code_progressive');
            $table->string('code');
            $table->string('description');
            $table->string('order_code')->nullable();
            $table->integer('order_code_progressive')->nullable();
            $table->date('order_code_date')->nullable();
            $table->string('order_code_type')->nullable();
            $table->string('order_code_region')->nullable();

            $table->foreignId('internal_referent_id')->nullable()->constrained('users')->cascadeOnUpdate()->nullOnDelete();
            $table->foreignId('client_id')->nullable()->constrained()->cascadeOnUpdate()->nullOnDelete();
            $table->foreignId('partner_id')->nullable()->constrained()->cascadeOnUpdate()->nullOnDelete();
            $table->foreignId('invoicing_address_id')->nullable()->constrained('addresses')->cascadeOnUpdate()->nullOnDelete();
            $table->foreignId('shipping_address_id')->nullable()->constrained('addresses')->cascadeOnUpdate()->nullOnDelete();
            $table->foreignId('payment_term_id')->nullable()->constrained()->cascadeOnUpdate()->nullOnDelete();
            $table->string('vat_type')->default(VatTypes::Standard->value);

            $table->string('confirmation_file')->nullable();

            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
