<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();

            $table->string('template')->nullable();

            $table->foreignId('brand_id')->nullable()->constrained('brands')->cascadeOnUpdate()->nullOnDelete();
            $table->foreignId('collection_id')->nullable()->constrained('collections')->cascadeOnUpdate()->nullOnDelete();
            $table->foreignId('discount_group_id')->nullable()->constrained('discount_groups')->cascadeOnUpdate()->nullOnDelete();

            $table->string('parent_sku')->nullable();
            $table->string('parent_category_label')->nullable();

            $table->string('sku')->unique();
            $table->string('adhoc_sku')->nullable();
            $table->string('adhoc_discount_category')->nullable();
            $table->string('ean_code')->nullable()->unique();
            
            $table->string('description');
            $table->string('extra_description')->nullable();
            $table->string('image')->nullable();
            $table->string('dimensions')->nullable();

            $table->integer('selling_price')->nullable()->comment('Price in cents');
            $table->integer('purchasing_price')->nullable()->comment('Price in cents');

            $table->integer('purchase_units')->nullable();
            $table->string('leadtime')->nullable();
            $table->string('type')->nullable();
            $table->string('country_of_origin')->nullable();
            $table->string('supplier_color')->nullable();
            $table->string('hs_code')->nullable();
            
            $table->string('height')->nullable();
            $table->string('length')->nullable();
            $table->string('width')->nullable();
            $table->string('diameter')->nullable();
            $table->string('volume')->nullable();
            $table->string('capacity')->nullable();
            $table->string('net_weight')->nullable();

            $table->string('notes')->nullable();

            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
