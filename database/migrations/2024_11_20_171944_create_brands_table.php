<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */ 
    public function up(): void
    {
        Schema::create('brands', function (Blueprint $table) {
            $table->id();
            $table->string('prefix')->unique();
            $table->string('name');
            $table->integer('price_range')->nullable();
            $table->integer('rating')->nullable();
            $table->integer('partnership_level')->nullable();
            $table->string('lead_time')->nullable();
            $table->string('purchase_price_list')->nullable();
            $table->string('purchase_conditions')->nullable();
            $table->integer('minimum_orderable')->nullable()->comment('Minimum orderable price in minor units.');
            $table->string('extra_costs')->nullable();
            $table->string('delivery_terms')->nullable();
            $table->string('yearly_bonus_info')->nullable();
            $table->string('catalogs')->nullable();
            $table->string('pricelist')->nullable();
            $table->date('valid_from')->nullable();
            $table->date('expected_pricelist_update')->nullable();
            $table->string('social_link')->nullable();
            $table->string('supplier_media_link')->nullable();
            $table->string('supplier_media_link_user')->nullable();
            $table->string('supplier_media_link_password')->nullable();
            $table->string('image')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('brands');
    }
};
