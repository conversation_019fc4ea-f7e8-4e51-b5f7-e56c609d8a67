<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('imports', function (Blueprint $table) {
            $table->id();

            // Import metadata
            $table->string('type'); // brands, clients, products, etc.
            $table->string('source'); // cli, scheduler, user
            $table->foreignId('user_id')->nullable()->constrained()->nullOnDelete();

            // File information
            $table->string('file_path');
            $table->string('file_disk')->default('private');

            // Status and progress tracking
            $table->string('status')->default('pending'); // pending, processing, completed, failed
            $table->unsignedInteger('total_jobs')->default(0);
            $table->unsignedInteger('processed_jobs')->default(0);
            $table->unsignedInteger('failed_jobs')->default(0);

            // Timestamps
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index(['type', 'status']);
            $table->index(['source', 'created_at']);
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('imports');
    }
};
