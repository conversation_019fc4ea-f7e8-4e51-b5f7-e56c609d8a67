<?php

namespace Database\Seeders;

use App\Models\PaymentTerm;
use App\Models\PaymentTermItem;
use Illuminate\Database\Seeder;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class PaymentTermItemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $paymentTerms = PaymentTerm::all();
        $paymentTerms->each(function (PaymentTerm $paymentTerm) {
            PaymentTermItem::factory(2)->create([
                'payment_term_id' => $paymentTerm->id,
            ]);
        });
    }
}
