<?php

namespace Database\Seeders;

use App\Models\Brand;
use App\Models\Address;
use App\Models\Contact;
use App\Models\Supplier;
use Illuminate\Database\Seeder;

class BrandSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Brand::factory(50)->create()->each(function ($brand) {
            $contacts = Contact::inRandomOrder()->take(3)->pluck('id');
            $brand->contacts()->attach($contacts);
            $suppliers = Supplier::inRandomOrder()->take(2)->pluck('id');
            $brand->suppliers()->attach($suppliers);
        });
    }
}