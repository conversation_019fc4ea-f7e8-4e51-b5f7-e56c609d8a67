<?php

namespace Database\Seeders;

use App\Models\Address;
use App\Models\Contact;
use App\Models\Supplier;
use App\Enums\AddressTypes;
use Illuminate\Database\Seeder;

class SupplierSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Supplier::factory(25)->create();

        Supplier::all()->each(function ($supplier) {
            // Attach 3 random contacts to the supplier
            $contacts = Contact::factory(10)->create();
            $supplier->contacts()->attach($contacts->pluck('id')->toArray());

            // Attach 3 random addresses to the supplier
            $addresses = Address::factory(3)->create([
                'addressable_id' => $supplier->id,
                'addressable_type' => Supplier::class,
            ]);
            $supplier->addresses()->saveMany($addresses);

            // Select only addresses of type Shipping and set their parent_id to a random Invoicing address
            $shippingAddresses = $addresses->where('type', AddressTypes::Shipping);
            $invoicingAddresses = $supplier->addresses()->where('type', AddressTypes::Invoicing)->get();

            foreach ($shippingAddresses as $shippingAddress) {
                if ($invoicingAddresses->isNotEmpty()) {
                    $randomInvoicingAddress = $invoicingAddresses->random();
                    $shippingAddress->parent_id = $randomInvoicingAddress->id;
                    $shippingAddress->save();
                }
            }
        });
    }
}