<?php

namespace Database\Seeders;

use App\Models\Brand;
use App\Models\DiscountGroup;
use Illuminate\Database\Seeder;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class DiscountGroupSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DiscountGroup::factory()->count(50)->create()->each(function ($discountGroup) {
            $brand = Brand::inRandomOrder()->first();
            $discountGroup->brand()->associate($brand);
            $discountGroup->save();
        });
    }
}
