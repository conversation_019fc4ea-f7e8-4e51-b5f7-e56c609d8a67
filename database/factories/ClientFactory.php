<?php

namespace Database\Factories;

use App\Enums\ClientTypes;
use App\Enums\DeliveryTerms;
use App\Enums\CommercialCategories;
use PrinsFrank\Standards\Country\CountryAlpha3;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Client>
 */
class ClientFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'type' => ClientTypes::Lead,
            'partner_id' => $this->faker->optional(0.5)->randomElement(\App\Models\Partner::pluck('id')->toArray()),
            'internal_referent_id' => $this->faker->optional(0.5)->randomElement(\App\Models\User::query()->where('is_employee', TRUE)->pluck('id')->toArray()),
            'company' => $this->faker->company,
            'email' => $this->faker->unique()->safeEmail,
            'country' => $this->faker->optional()->randomElement(array_map(fn($case) => $case->value, CountryAlpha3::cases())),
            'commercial_category' => $this->faker->optional()->randomElement(array_map(fn($case) => $case->value, CommercialCategories::cases())),
            'priority' => $this->faker->optional()->numberBetween(1, 4),
            'minimum_orderable' => $this->faker->optional()->numberBetween(1000, 10000),
            'handling_and_packing' => $this->faker->optional()->numberBetween(1, 10),
            'delivery_terms' => $this->faker->optional()->randomElement(array_map(fn($case) => $case->value, DeliveryTerms::cases())),
            'countries_of_expertise' => $this->faker->optional()->randomElements(array_column(CountryAlpha3::cases(), 'value'), $this->faker->numberBetween(1, 3)),
            'notes' => $this->faker->optional()->text,
        ];
    }
}
