<?php

namespace Database\Factories;

use App\Enums\ContactPositions;
use PrinsFrank\Standards\Country\CountryAlpha3;
use Illuminate\Database\Eloquent\Factories\Factory;
use PrinsFrank\Standards\Language\LanguageAlpha2;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Contact>
 */
class ContactFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'phone' => $this->faker->optional()->phoneNumber(),
            'country' => $this->faker->optional()->randomElement(array_map(fn($case) => $case->value, CountryAlpha3::cases())),
            'departments' => $this->faker->optional()->randomElements(array_column(ContactPositions::cases(), 'value'), $this->faker->numberBetween(1, 3)),
            'position' => $this->faker->optional()->text(10),
            'languages' => $this->faker->optional()->randomElements(array_column(LanguageAlpha2::cases(), 'value'), $this->faker->numberBetween(1, 3)),
            'notes' => $this->faker->optional()->realText(),
        ];
    }
}
