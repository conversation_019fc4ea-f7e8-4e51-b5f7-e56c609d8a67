<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'brand_id' => \App\Models\Brand::all()->random()->id,
            'collection_id' => \App\Models\Collection::all()->random()->id,

            'parent_sku' => $this->faker->optional()->randomElement([
                'parent_1', 
                'parent_2', 
                'parent_3',
                'parent_4',
            ]),

            'sku' => $this->faker->unique()->word,
            'adhoc_sku' => $this->faker->optional()->word,
            'ean_code' => $this->faker->unique()->ean13(),

            'description' => $this->faker->word,
            'extra_description' => $this->faker->word,
            'image' => $this->faker->optional()->imageUrl(),
            'dimensions' => $this->faker->word,

            'selling_price' => $this->faker->optional()->numberBetween(100, 1000),
            'purchasing_price' => $this->faker->optional()->numberBetween(100, 1000),
            
            'purchase_units' => $this->faker->optional()->numberBetween(1,10),
            'leadtime' => $this->faker->word,
            'type' => $this->faker->word,
            'country_of_origin' => $this->faker->optional()->country,
            'supplier_color' => $this->faker->optional()->colorName,
            'hs_code' => $this->faker->optional()->word,

            'height' => $this->faker->optional()->randomFloat(),
            'length' => $this->faker->optional()->randomFloat(),
            'width' => $this->faker->optional()->randomFloat(),
            'diameter' => $this->faker->optional()->randomFloat(),
            'volume' => $this->faker->optional()->randomFloat(),
            'capacity' => $this->faker->optional()->randomFloat(),
            'net_weight' => $this->faker->optional()->randomFloat(),
        ];
    }
}
