<?php

namespace Database\Factories;

use App\Enums\AddressTypes;
use PrinsFrank\Standards\Country\CountryAlpha3;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Address>
 */
class AddressFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->randomElement(['Weathouse', 'Retail', 'HeadQuarter']);
        $type = $this->faker->randomElement(AddressTypes::cases());

        if($type === AddressTypes::Invoicing) {
            return [
                'name' => $name,
                'type' => $type->value,
                'code_invoicing' => $this->faker->numerify('###########'),
                'company' => $this->faker->company,
                'vat_number' => $this->faker->optional()->numerify('###########'),
                'fiscal_code' => $this->faker->optional()->numerify('###########'),
                'sdi_code' => $this->faker->optional()->numerify('########'),            
                'street' => $this->faker->optional()->streetAddress,
                'city' => $this->faker->optional()->city,
                'state' => $this->faker->optional()->state,
                'zip' => $this->faker->optional()->postcode,
                'country' => $this->faker->optional()->randomElement(array_map(fn($case) => $case->value, CountryAlpha3::cases())),
            ];
        }
        elseif($type === AddressTypes::Shipping) {
            return [
                'name' => $name,
                'type' => $type->value,
                'code_shipping' => $this->faker->numerify('###########'),
                'company' => $this->faker->company,
                'street' => $this->faker->optional()->streetAddress,
                'city' => $this->faker->optional()->city,
                'state' => $this->faker->optional()->state,
                'zip' => $this->faker->optional()->postcode,
                'country' => $this->faker->optional()->randomElement(array_map(fn($case) => $case->value, CountryAlpha3::cases())),
            ];
        }
    }
}
